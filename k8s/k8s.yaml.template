apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${APP_NAME}
  namespace: ${K8S_NAMESPACE}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ${APP_NAME}
  template:
    metadata:
      labels:
        app: ${APP_NAME}
    spec:
      containers:
        - name: ${APP_NAME}
          image: ${IMAGE_URL}:${IMAGE_TAG}
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: env-application
          env:
            - name: UNIT_NO
              value: ${ENV_UNIT_NO}
          ports:
            - containerPort: ${APP_START_PORT}
              name: http
          volumeMounts:
            - name: log-volume
              mountPath: /app/logs
            - name: jacoco-volume
              mountPath: /jacoco  # 挂载 JaCoCo 目录
      volumes:
        - name: log-volume
          hostPath:
            path: /data/file/nfs/logs/${NAMESPACE}/${APP_NAME}  # 主机上的目录，用于保存日志文件
            type: DirectoryOrCreate  # 如果目录不存在，则创建
        - name: jacoco-volume
          hostPath:
            path: /data/file/nfs/coverage-data/${NAMESPACE}/  # 主机上的目录，用于保存覆盖率文件
            type: DirectoryOrCreate  # 如果目录不存在，则创建

---
apiVersion: v1
kind: Service
metadata:
  name: ${APP_NAME}
  namespace: ${K8S_NAMESPACE}
  labels:
    app: ${APP_NAME}
spec:
  type: NodePort
  ports:
    - port: ${APP_START_PORT}
      targetPort: ${APP_START_PORT}
      nodePort: ${DOCKER_NODE_PORT}
      name: http
  selector:
    app: ${APP_NAME}
