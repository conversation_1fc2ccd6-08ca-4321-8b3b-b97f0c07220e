#!/usr/bin/env bash

#应用ID
export APP_ID=cub
#应用名 根据不同模块修改，如anytxn-param
export APP_NAME=anytxn-collect-front
#应用启动端口 根据不同模块修改，如8765
export APP_START_PORT=8080
#DockerNode端口 开发默认为31，
export DOCKER_NODE_PORT=31890
#K8S URL
export K8S_URL=k8s.jrx.com
#K8s 仓库
export K8S_REGISTRY_NAME=cub-test02
#docker 模块,根据不同模块修改，如anytxn-param,默认与APP_NAME 一致
export DOCKER_MODULES=( anytxn-collect-front )
#单元数
export UNIT_NO_LIST=( "unit00")



