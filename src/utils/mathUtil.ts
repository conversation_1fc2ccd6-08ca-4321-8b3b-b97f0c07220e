/**
 * 数字格式化为千分位
 * @param number 需要格式化的数字
 * @param decimal 保留小数位
 * @returns {number}
 */
export const formatNumber = (number: number, decimal: number = 2) => {
  if (isNaN(number)) {
    return 0;
  }

  let integerPart: string = Math.floor(number).toString();
  let decimalPart = Math.round((number % 1) * 100)
    .toString()
    .padEnd(decimal, '0');

  let parts: Array<string> = [];
  while (integerPart.length > 3) {
    parts.unshift(integerPart.slice(-3));
    integerPart = integerPart.slice(0, -3);
  }
  parts.unshift(integerPart);

  let formattedIntegerPart = parts.join(',');
  return decimalPart ? `${formattedIntegerPart}.${decimalPart}` : formattedIntegerPart;
};

/**
 * 判空（除了数字0和false）
 * @param value 需要判断的值
 * @returns {boolean}
 */
export const isEmpty = (value) => {
  if (value == null) return true; // null 或 undefined
  if (typeof value === 'string' && value.trim() === '') return true; // 空字符串
  if (Array.isArray(value) && value.length === 0) return true; // 空数组
  if (typeof value === 'object' && Object.keys(value).length === 0) return true; // 空对象
  return false;
};

/**
 * 10进制转成64进制
 * @param num
 * @returns {string}
 */
export const decimalToBase64 = (num: number) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
  let base64 = '';

  while (num > 0) {
    let remainder = num % 64;
    base64 = chars[remainder] + base64;
    num = Math.floor(num / 64);
  }

  return base64 || '0';
};
