/**
 * Created by ca<PERSON><PERSON> on 2025/6/6.
 */

import { AUTH_FAILED, SESSION, SUCCESS_CODE } from '@/constants/publicConstant';
import { notification } from 'antd';
import axios from 'axios';
import BigJSON from 'json-bigint';

export const IS_DEV_MODE = process.env.NODE_ENV !== 'production';
export const baseURL = IS_DEV_MODE ? '/api/anytxn-collect-web' : '/api/anytxn-collect-web';

const JSONbigString = BigJSON({ storeAsString: true });

const requestUtil = axios.create({
  baseURL, // 采用NG统一接口地址，开发时候用代理就可以
  timeout: 10000,
  transformResponse: [
    function (data) {
      try {
        data = JSONbigString.parse(data);
        return data;
      } catch (err) {
        console.error(err);
        notification.error({ message: 'response error' });
        return data;
      }
    },
  ],
}) as typeof axios;

requestUtil.interceptors.request.use(
  (config) => {
    const token = sessionStorage.getItem(SESSION.token);
    if (token) {
      config.headers['X-Requested-With'] = 'XMLHttpRequest';
      config.headers.Authorization = ['Bearer', token].join(' ');
    }
    return config;
  },
  (error) => {
    console.error('request错误', error);
  },
);

requestUtil.interceptors.response.use(
  (response) => {
    // 下载，返回文件流（通过 responseType 判断）
    if (response.request && response.request.responseType === 'blob') {
      return response;
    }
    // 这里用类型断言，保证类型安全
    const res = response.data as any;
    const { header, message } = res || {};
    // 有返回码的普通请求
    if (header?.errorCode) {
      // 接口错误
      if (header.errorCode !== SUCCESS_CODE) {
        notification.error({ message: header.errorMsg });
        return Promise.reject(new Error(header.errorMsg));
      }
      // 请求成功
      return res;
    } else {
      // 没返回码的特殊请求
      if (message) notification.error({ message });
      return Promise.reject(new Error(message || 'response error'));
    }
  },
  (error) => {
    const code = error.response?.status;
    let errorMsg = error.response?.data?.header?.errorMsg || error.message;
    // 前端异常状态处理
    if ([502, 503].includes(code)) {
      // 要重定向到登录页面
      sessionStorage.clear();
      window.location.href = '/$';
    }
    // 后端异常状态码处理（header下errorCode为B1300001为接口无权访问，B1300002为token认证失败，需要处理跳转到404页面）
    if (code === 401) {
      // token过期重定向到404
      if (error.response?.data?.header?.errorCode === AUTH_FAILED) {
        sessionStorage.clear();
        notification.error({ message: errorMsg });
        setTimeout(() => {
          window.location.href = '/$';
        }, 1000);
        return Promise.reject(new Error(errorMsg));
      }
    }
    console.error('response错误', error);
    notification.error({ message: errorMsg });
    return Promise.reject(error);
  },
);

// 下载接口单独封装
export function downloadRequest(url: string, options: any = {}) {
  return requestUtil({
    url,
    method: options.method || 'get',
    responseType: 'blob',
    ...options,
  });
}

export default requestUtil;
