/**
 * Created by ca<PERSON>un on 2025/6/6.
 */

import requestUtil from './requestUtil';
import { obj2url } from './urlUtil';

export const CONTENT_TYPE = {
  formUrlencoded: 'application/x-www-form-urlencoded',
  json: 'application/json',
  formData: 'multipart/form-data',
};

export async function getData(url: string, data?: object): Promise<any> {
  if (data) {
    url += obj2url(data);
  }
  try {
    const res = await requestUtil({
      url,
      method: 'get',
      headers: { 'Content-Type': CONTENT_TYPE.formUrlencoded },
    });
    return res;
  } catch (err) {
    return null;
  }
}

export async function postData(url: string, data?: object): Promise<any> {
  try {
    const res = await requestUtil({
      url,
      data,
      method: 'post', // 提交资源，请求、创建、更新均可，不具有幂等性
      headers: { 'Content-Type': CONTENT_TYPE.json },
    });
    return res;
  } catch (err) {
    return null;
  }
}

export async function putData(url: string, data?: object): Promise<any> {
  try {
    const res = await requestUtil({
      url,
      data,
      method: 'put', // 更新整个资源，如果不存在则会创建，具有幂等性
      headers: { 'Content-Type': CONTENT_TYPE.json },
    });
    return res;
  } catch (err) {
    return null;
  }
}

export async function deleteData(url: string, data?: object): Promise<any> {
  if (data) {
    url += obj2url(data);
  }
  try {
    const res = await requestUtil({
      url,
      method: 'delete',
      headers: { 'Content-Type': CONTENT_TYPE.formUrlencoded },
    });
    return res;
  } catch (err) {
    return null;
  }
}

export async function uploadData(url: string, data?: object): Promise<any> {
  try {
    const res = await requestUtil({
      url,
      data,
      method: 'post',
      headers: { 'Content-Type': CONTENT_TYPE.formData },
    });
    return res;
  } catch (err) {
    return null;
  }
}

export async function downloadData(url: string, data?: object): Promise<any> {
  if (data) {
    url += obj2url(data);
  }
  try {
    const res = await requestUtil({
      url,
      method: 'get',
      headers: { 'Content-Type': CONTENT_TYPE.formUrlencoded },
      responseType: 'blob',
    });
    return res;
  } catch (err) {
    return null;
  }
}
