import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import { DATE_TYPE } from '@/constants/publicConstant';

const getYearMonth = (date: Dayjs) => date.year() * 12 + date.month();

/**
 * range_picker组件限制选择的范围
 * @param current range_picker的disabledDate方法参数1
 * @param info range_picker的disabledDate方法参数2
 * @param info.type 日期控件的类型
 * @param range 需要限制的范围
 * @param dateType DATE_TYPE 天/月
 * @returns {boolean}
 */
export const getRangePickerDisabledDate = (
  current,
  { from, type },
  range: number,
  dateType: string = DATE_TYPE.day,
) => {
  if (from) {
    const minDate = from.subtract(range - 1, 'months');
    const maxDate = from.add(range - 1, 'months');
    switch (type) {
      case 'year':
        return current.year() < minDate.year() || current.year() > maxDate.year();
      case 'month':
        return getYearMonth(current) < getYearMonth(minDate) || getYearMonth(current) > getYearMonth(maxDate);
      default: {
        const minDate = dayjs(from).subtract(range, DATE_TYPE[dateType] || DATE_TYPE.day);
        const maxDate = dayjs(from).add(range, 'month');
        return current < minDate || current > maxDate;
      }
    }
  }
  return false;
};
