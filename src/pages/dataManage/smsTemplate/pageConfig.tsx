import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.DATA_MANAGE,
  cardTitle: 'smsTemplate',
  // 页面接口请求
  urlObj: {
    list: urlConstants.STATION_MANAGEMENT.LIST,
    create: urlConstants.STATION_MANAGEMENT.CREATE,
    edit: urlConstants.STATION_MANAGEMENT.EDIT,
    delete: urlConstants.STATION_MANAGEMENT.DELETE,

    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'templateId',
      label: 'templateId',
      type: COMPONENT_TYPE.INPUT,
      data: DICT_CONSTANTS.STATION,
    },
    {
      value: 'templateName',
      label: 'templateName',
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 80,
    },
    {
      title: 'templateId',
      dataIndex: 'templateId',
      key: 'templateId',
    },
    {
      title: 'templateName',
      dataIndex: 'templateName',
      key: 'templateName',
      width: 120,
    },
    {
      title: 'contentTemplate',
      dataIndex: 'contentTemplate',
      key: 'contentTemplate',
      width: 120,
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 150,
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 160,
      valueType: RENDER_TYPE.DateTime,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};
