import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.DATA_MANAGE,
  cardTitle: 'dataManage',
  // 页面接口请求
  urlObj: {
    list: urlConstants.STATION_MANAGEMENT.LIST,
    create: urlConstants.STATION_MANAGEMENT.CREATE,
    edit: urlConstants.STATION_MANAGEMENT.EDIT,
    delete: urlConstants.STATION_MANAGEMENT.DELETE,
    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'caseCode',
      label: 'caseCode',
      type: COMPONENT_TYPE.INPUT,
      data: DICT_CONSTANTS.STATION,
    },
    {
      value: 'name',
      label: 'name',
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 80,
    },
    {
      title: 'caseCode',
      dataIndex: 'caseCode',
      key: 'caseCode',
    },
    {
      title: 'name',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: 'sex',
      dataIndex: 'sex',
      key: 'sex',
      width: 120,
    },
    {
      title: 'cardNo',
      dataIndex: 'cardNo',
      key: 'cardNo',
      width: 120,
    },
    {
      key: 'mobileNo',
      title: 'mobileNo',
      dataIndex: 'mobileNo',
    },
    {
      key: 'outboundWorkJ',
      title: 'outboundWorkJ',
      dataIndex: 'outboundWorkJ',
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};
