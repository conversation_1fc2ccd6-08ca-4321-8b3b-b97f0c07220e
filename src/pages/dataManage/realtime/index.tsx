import { OPERATE_TYPE } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import React, { Suspense, useState } from 'react';
import { dictEnum, pageConfig } from './pageConfig';
import useFormConfig from './useFormConfig';

const PageTemplate = React.lazy(() => import('@/components/templates/PageTemplate'));

const Realtime: React.FC = () => {
  const { translate } = useIntlCustom();
  const { urlObj, prefix, searchSource, columns, cardTitle } = pageConfig;
  const resetValue = { weightCoefficient: '', nodeAttriType: null };
  const [detailData, setDetailData] = useState<any>({});
  const [type, setType] = useState<string>(OPERATE_TYPE.list);
  const { infoFormConfig } = useFormConfig(type);

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 表格配置
  const tableConfig = {
    columns,
    rowKey: 'paramIndex',
    showPagination: true,
    optionList: [{ type: OPERATE_TYPE.fieldManage }],
  };

  // 表单配置
  const formConfig = {
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
    customChildren: {
      [OPERATE_TYPE.fieldManage]: (
        <PageTemplate
          searchConfig={searchConfig}
          tableConfig={{ columns, rowKey: 'roleId', optionList: [] }}
          urlObj={urlObj}
          cardTitle={cardTitle}
          intlPrefix={prefix}
          formActionConfig={{ showCreate: false }}
          isShowRouterBar={false}
        />
      ),
    },
  };

  // 列表按钮操作
  const handleAction = (editType: string, row: any) => {
    setDetailData({ ...row });
    setType(editType);
  };

  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        onAction={handleAction}
        formActionConfig={{
          showCreate: false,
        }}
      />
    </Suspense>
  );
};
export default React.memo(Realtime);
