import { COMPONENT_TYPE, FORMITEM_TYPE, I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import { IFormConfig } from '@/types/IForm';

const useFormConfig = (type: string) => {
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'tableChinese',
          label: 'tableChinese',
          disabled: true,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'tableChinese',
          label: 'tableChinese',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'tableChinese',
          label: 'tableChinese',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'considerInventory',
          label: 'considerInventory',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'considerWeight',
          label: 'considerWeight',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'description',
          label: 'description',
          rules: [{ required: true }],
        },
      ],
    },
  ];

  return {
    infoFormConfig,
  };
};

export default useFormConfig;
