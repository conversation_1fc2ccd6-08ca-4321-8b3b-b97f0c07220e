import { COMPONENT_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import DICT_CONSTANTS from '@/constants/dictConstants';
import { IFormConfig } from '@/types/IForm';

const useFormConfig = (type: string) => {
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'caseCode',
          label: 'caseCode',
          rules: [{ required: true }],
          disabled: true,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'orgCustNbr',
          label: 'orgCustNbr',
          rules: [{ required: true }],
          disabled: true,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'custName',
          label: 'custName',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'classIBalance',
          label: 'classIBalance',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'chargoffFlag',
          label: 'chargoffFlag',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'countOdue',
          label: 'countOdue',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'icType',
          label: 'icType',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'custIc',
          label: 'custIc',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'lastCaseCode',
          label: 'lastCaseCode',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.DATE_PICKER,
          name: 'lastCaseDate',
          label: 'lastCaseDate',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.DATE_PICKER,
          name: 'lawDate',
          label: 'lawDate',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'lawFlag',
          label: 'lawFlag',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'model',
          label: 'model',
          data: DICT_CONSTANTS.CASEMANAGE_MODEL,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.DATE_PICKER,
          name: 'dteIntoCollection',
          label: 'dteIntoCollection',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.DATE_PICKER,
          name: 'dteOutCollection',
          label: 'dteOutCollection',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.DATE_PICKER,
          name: 'firstDate',
          label: 'firstDate',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'currState',
          label: 'currState',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'caseState',
          label: 'caseState',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'teamCode',
          label: 'teamCode',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.DATE_PICKER,
          name: 'lastOpeTime',
          label: 'lastOpeTime',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.DATE_PICKER,
          name: 'inTeamDate',
          label: 'inTeamDate',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'reasonCode',
          label: 'reasonCode',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'actSerialId',
          label: 'actSerialId',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'noneContactRuleCode',
          label: 'noneContactRuleCode',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'contactCode',
          label: 'contactCode',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'contactTimes',
          label: 'contactTimes',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'resultCode',
          label: 'resultCode',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.DATE_PICKER,
          name: 'resultTime',
          label: 'resultTime',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'resultDirection',
          label: 'resultDirection',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'attemptCount',
          label: 'attemptCount',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'clientCode',
          label: 'clientCode',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'voiceCallType',
          label: 'voiceCallType',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'specialCaseNote',
          label: 'specialCaseNote',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'callTotal',
          label: 'callTotal',
          rules: [{ required: true }],
        },
      ],
    },
  ];

  return {
    infoFormConfig,
  };
};

export default useFormConfig;
