import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.CALL_PARAM,
  cardTitle: 'caseManage',
  // 页面接口请求
  urlObj: {
    list: urlConstants.CASE_BASE_INFO.LIST,

    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'caseCode',
      label: 'caseCode',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'model',
      label: 'model',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.CASEMANAGE_MODEL,
    },
    {
      value: 'currState',
      label: 'currState',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'caseCode',
      dataIndex: 'caseCode',
      key: 'caseCode',
      width: 120,
    },
    {
      title: 'orgCustNbr',
      dataIndex: 'orgCustNbr',
      key: 'orgCustNbr',
      width: 120,
    },
    {
      title: 'custName',
      dataIndex: 'custName',
      key: 'custName',
      width: 120,
    },
    {
      title: 'model',
      dataIndex: 'model',
      key: 'model',
      width: 120,
      data: DICT_CONSTANTS.CASEMANAGE_MODEL,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'CASEMANAGE_MODEL',
    },
    {
      title: 'icType',
      dataIndex: 'icType',
      key: 'icType',
      width: 120,
    },
    {
      title: 'custIc',
      dataIndex: 'custIc',
      key: 'custIc',
      width: 120,
    },
    {
      title: 'currState',
      dataIndex: 'currState',
      key: 'currState',
      width: 120,
    },
    {
      title: 'caseState',
      dataIndex: 'caseState',
      key: 'caseState',
      width: 120,
    },
    {
      title: 'callTotal',
      dataIndex: 'callTotal',
      key: 'callTotal',
      width: 120,
    },
    {
      title: 'dteIntoCollection',
      dataIndex: 'dteIntoCollection',
      key: 'dteIntoCollection',
      width: 120,
    },
    {
      title: 'dteOutCollection',
      dataIndex: 'dteOutCollection',
      key: 'dteOutCollection',
      width: 120,
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      width: 160,
      valueType: RENDER_TYPE.DateTime,
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      width: 120,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};
