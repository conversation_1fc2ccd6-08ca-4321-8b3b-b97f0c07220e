// table组件hooks
import { CommonTable } from '@/components';
import { IPaniation } from '@/types/ICommon';
import { pageConfig } from '../pageConfig';

const useTable = ({ searchValue, listData, loading, paginationConfig, getTableData, handleAction }) => {
  const { prefix, columns, optionList } = pageConfig;
  // 页码改变事件
  const handleTableChange = (pagination: IPaniation): void => {
    const { current: currentPage, pageSize } = pagination;
    getTableData(searchValue, { currentPage, pageSize });
  };

  // 渲染组件
  const tableChildren = () => {
    return (
      <CommonTable
        rowKey="paramIndex"
        dataSource={listData}
        columns={columns}
        loading={loading}
        optionList={optionList}
        paginationConfig={paginationConfig}
        intlPrefix={prefix}
        onAction={handleAction}
        onChange={handleTableChange}
      />
    );
  };

  return { tableChildren };
};

export default useTable;
