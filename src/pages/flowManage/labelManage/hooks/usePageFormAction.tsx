// formAction组件hooks
import { FormAction } from '@/components';
import { OPERATE_TYPE } from '@/constants/publicConstant';

const usePageFormAction = ({ formRef, labelSettingRef, getTableData, detailType, handleAction, handleCardBack }) => {
  // 表单提交事件
  const handleDrawerSubmit = async () => {
    try {
      // 获取基本信息表单数据
      const basicFormData = await formRef.current?.onSubmit();

      // 获取标签设置数据
      const labelSettingData = await labelSettingRef.current?.validateFields();

      if (basicFormData && labelSettingData) {
        const allData = {
          ...basicFormData,
          labelSetting: labelSettingData,
        };

        console.log('2222-完整提交数据--保存', allData);

        // 这里调用实际的提交接口
        // await submitData(allData);

        handleCardBack();
        getTableData();
      }
    } catch (error) {
      console.error('提交失败:', error);
    }
  };

  // 渲染formAction组件
  const renderFormAction = () => {
    return (
      <FormAction
        showCreate={detailType === OPERATE_TYPE.list}
        showSubmit={false}
        onCreate={() => handleAction(OPERATE_TYPE.create, {})}
      />
    );
  };
  return { renderFormAction, handleDrawerSubmit };
};

export default usePageFormAction;
