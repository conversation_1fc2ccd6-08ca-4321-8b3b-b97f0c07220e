import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import store from '@/store';
import { IFormConfig } from '@/types/IForm';
import { EditableFormInstance } from '@ant-design/pro-components';
import { useRef, useState } from 'react';

const useFormConfig = (type: string) => {
  const [dictState] = store.useModel('dict');
  const [editType, setEditType] = useState<string>(type);
  const [editRuleTableData, setEditTRuleableData] = useState<any[]>([{ id: '1' }]);
  const editRulesTableFormRef = useRef<EditableFormInstance>();

  // 可编辑表格操作列
  const editRuleColumns = [
    {
      width: 120,
      key: 'priority',
      title: 'priority',
      dataIndex: 'priority',
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: 'nodeAttriId',
      dataIndex: 'nodeAttriId',
      valueType: 'select',
      showKey: true,
      valueEnum: () => {
        const result = {};
        dictState.dictMap?.nodeAttriId?.map((item) => {
          const { key, value } = item;
          result[key] = { text: value };
        });
        return result;
      },
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
  ];
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'tagId',
          label: 'tagId',
          // rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'tagName',
          label: 'tagName',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'tagType',
          label: 'tagType',
          data: DICT_CONSTANTS.LABEL_TYPE,
          rules: [{ required: true }],
          // disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'nodeAttriId',
          label: 'nodeAttriId',
          isint: '0',
          data: dictState.dictMap?.nodeAttriId,
          mode: 'multiple',
          // disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'tagSource',
          label: 'tagSource',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.LABEL_SOURCE,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'tagAttribute',
          label: 'tagAttribute',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.LABEL_ATTRIBUTE,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'status',
          label: 'status',
          data: DICT_CONSTANTS.PARAM_STATUS,
          rules: [{ required: true }],
          // disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'nodeRuleype',
          label: 'nodeRuleype',
          data: DICT_CONSTANTS.PARAM_STATUS,
          // data: dictState.dictMap?.nodeRule?.filter((item) => item.ruleType === 'LabelHanldle'),
          isint: '0',
          mode: 'multiple',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'tagDescription',
          label: 'tagDescription',
          rules: [{ required: true }],
          maxLength: 200,
        },
      ],
    },
    {
      type: FORMITEM_TYPE.EditTable,
      rowKey: 'id',
      columns: editRuleColumns,
      dataSource: editRuleTableData,
      canEdit: true,
      editableFormRef: editRulesTableFormRef,
      optionCount: 1,
      getOptionList: (row) => [
        {
          optionType: OPERATE_TYPE.edit,
          disabled: editType === OPERATE_TYPE.detail,
        },
        {
          optionType: OPERATE_TYPE.delete,
          disabled: editType === OPERATE_TYPE.detail,
        },
      ],
      onCreate: (index) => ({ id: Date.now() + 1, isAdd: true }),
    },
  ];

  return {
    infoFormConfig,
    editRulesTableFormRef,
  };
};

export default useFormConfig;
