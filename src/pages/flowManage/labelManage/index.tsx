import { FormTemplate, LayoutTemplate } from '@/components';
import { DEFAULT_PAGINATION, OPERATE_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import useIntlCustom from '@/hooks/useIntlCustom';
import commonServices from '@/services/common';
import { TablePaginationConfig, Tabs, TabsProps } from 'antd';
import React, { ReactNode, Suspense, useEffect, useRef, useState } from 'react';
import LabelSetting from './componnets/LabelSetting';
import useFormConfig from './hooks/useFormConfig';
import usePageFormAction from './hooks/usePageFormAction';
import useSearch from './hooks/useSearch';
import useTable from './hooks/useTable';
import { pageConfig } from './pageConfig';

const ProgramPage: React.FC = () => {
  const { prefix, cardTitle } = pageConfig;
  const { translate } = useIntlCustom();
  const formRef = useRef<any>(null);
  const labelSettingRef = useRef<any>(null);

  const [detailType, setDetailType] = useState<string>(OPERATE_TYPE.list);
  const { infoFormConfig } = useFormConfig(detailType);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [drawerParams, setDrawerParams] = useState<{ input: any[] }>({ input: [] });
  const [listData, setListData] = useState<object[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [paginationConfig, setPaginationConfig] = useState<TablePaginationConfig | false>(false);
  // const [labelSettingData, setLabelSettingData] = useState<any>({});
  const [basicFormData, setBasicFormData] = useState<any>({}); // 添加基本表单数据状态

  // 副作用
  useEffect(() => {
    getTableData();
  }, []);

  // 逻辑处理
  const getTableData = async (searchValue = { keyValue: '', value: '' }, pagination = { ...DEFAULT_PAGINATION }) => {
    // setLoading(true);
    try {
      const param = {
        url: urlConstants.ADUIT_MANAGE.LIST,
        pagination,
        searchValue: { [searchValue.keyValue]: searchValue?.value },
      };
      const res = await commonServices.getTableListData(param);
      const { data, total } = res;
      Array.isArray(data) && setListData(data);
      total &&
        setPaginationConfig({
          showSizeChanger: true,
          showQuickJumper: true,
          total,
        });
    } catch (error) {
      setListData([]);
      setLoading(false);
    }
  };

  // 操作列事件
  const handleAction = (type: string, row: any) => {
    setDetailType(type);
    setDrawerOpen(true);
    setDrawerParams(row?.params || { input: [] });
    // 设置基本表单的初始数据
    setBasicFormData(row || {});
  };

  // 返回事件
  const handleCardBack = (): void => {
    setDetailType(OPERATE_TYPE.list);
    setDrawerOpen(false);
  };

  // search组件hooks
  const { searchValue, searchChildren } = useSearch({
    getTableData,
    handleAction,
  });

  // table组件hooks
  const { tableChildren } = useTable({
    listData,
    loading,
    searchValue,
    paginationConfig,
    getTableData,
    handleAction,
  });

  // formAction组件hooks
  const { renderFormAction, handleDrawerSubmit } = usePageFormAction({
    formRef,
    labelSettingRef,
    detailType,
    getTableData,
    handleCardBack,
    handleAction,
  });

  // 处理基本表单数据变化
  const handleBasicFormChange = (changedValues: any, allValues: any) => {
    setBasicFormData(allValues);
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: translate(prefix, 'baseInfo'),
      children: (
        <FormTemplate
          ref={formRef}
          config={infoFormConfig}
          initialData={basicFormData}
          loading={false}
          intlPrefix={prefix}
          canEdit={detailType !== OPERATE_TYPE.detail}
          showMaintenance={detailType === OPERATE_TYPE.detail}
          onChange={handleBasicFormChange}
        />
      ),
    },
    {
      key: '2',
      label: translate(prefix, 'baseInfo'),
      children: <LabelSetting formRef={labelSettingRef} params={drawerParams} />,
    },
  ];
  // 详情组件
  const formChildren = (): ReactNode => {
    return <Tabs defaultActiveKey="1" items={items} />;
  };

  return (
    <Suspense>
      <LayoutTemplate
        searchChildren={searchChildren()}
        tableChildren={tableChildren()}
        formChildren={formChildren()}
        drawerOpen={drawerOpen}
        type={detailType}
        intlPrefix={prefix}
        cardTitle={cardTitle}
        cardExtra={renderFormAction()}
        handleClose={handleCardBack}
        handleSubmit={handleDrawerSubmit}
      />
    </Suspense>
  );
};
export default React.memo(ProgramPage);
