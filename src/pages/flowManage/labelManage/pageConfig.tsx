import DICT_CONSTANTS from '@/constants/dictConstants';
import PERMISSION_CONSTANTS from '@/constants/permissionConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, OPERATE_TYPE, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';

const getRequestData = (postData, type: string, rowData): object => {
  switch (type) {
    case OPERATE_TYPE.edit:
    case OPERATE_TYPE.create:
      return {
        ...postData.formData,
        nodeAttriId: postData.formData?.nodeAttriId?.join(','),
        // 标签设置数据
        labelSetting: postData.labelSetting || {},
        // 可编辑表格数据
        editRuleTableData: postData.editRuleTableData || [],
      };
    default:
      return { ...postData };
  }
};

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.CALL_PARAM,
  cardTitle: 'labelManage',
  // 页面接口请求
  urlObj: {
    list: urlConstants.LABEL_MANAGE.LIST,
    create: urlConstants.LABEL_MANAGE.CREATE,
    edit: urlConstants.LABEL_MANAGE.EDIT,
    delete: urlConstants.LABEL_MANAGE.DELETE,
    getRequestData,
  },
  resetValue: {
    tagName: '',
    tagId: '',
    status: null,
  },
  // 搜索
  searchSource: [
    {
      value: 'tagName',
      label: 'tagName',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'tagId',
      label: 'tagId',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'status',
      label: 'status',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'tagId',
      dataIndex: 'tagId',
      key: 'tagId',
      width: 120,
    },
    {
      title: 'tagName',
      dataIndex: 'tagName',
      key: 'tagName',
      width: 220,
      render: (text) => <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>,
    },
    {
      title: 'tagType',
      dataIndex: 'tagType',
      key: 'tagType',
      width: 120,
      data: DICT_CONSTANTS.LABEL_TYPE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'LABEL_TYPE',
    },
    {
      title: 'tagSource',
      dataIndex: 'tagSource',
      key: 'tagSource',
      width: 120,
      data: DICT_CONSTANTS.LABEL_SOURCE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'LABEL_SOURCE',
    },
    {
      title: 'tagAttribute',
      dataIndex: 'tagAttribute',
      key: 'tagAttribute',
      width: 150,
      data: DICT_CONSTANTS.LABEL_ATTRIBUTE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'LABEL_ATTRIBUTE',
    },
    {
      width: 100,
      key: 'status',
      title: 'status',
      dataIndex: 'status',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      title: 'tagDescription',
      dataIndex: 'tagDescription',
      key: 'tagDescription',
      width: 260,
      render: (text) => <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>,
    },
    {
      key: 'updater',
      title: 'updater',
      dataIndex: 'updater',
      width: 120,
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      width: 160,
      valueType: RENDER_TYPE.DateTime,
    },
  ],
  // 操作列
  optionList: [
    { type: OPERATE_TYPE.detail, permissionId: PERMISSION_CONSTANTS.AUTH_ORIZATION_CONTROL.DETAIL },
    { type: OPERATE_TYPE.edit, permissionId: PERMISSION_CONSTANTS.AUTH_ORIZATION_CONTROL.EDIT },
    { type: OPERATE_TYPE.delete, permissionId: PERMISSION_CONSTANTS.AUTH_ORIZATION_CONTROL.DETAIL },
  ],
  // 新增按钮权限
  formActionPermissionObj: {
    create: PERMISSION_CONSTANTS.AUTH_ORIZATION_CONTROL.CREATE,
  },
};

// 需要查询的参数类型
export const dictEnum = {
  nodeAttriId: DICT_CONSTANTS.DICT_ENUM_MAP.nodeAttriId,
  nodeRule: DICT_CONSTANTS.DICT_ENUM_MAP.nodeRule,
};
