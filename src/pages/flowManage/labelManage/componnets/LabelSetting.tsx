import { EditTable } from '@/components';
import { I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import RuleParamTable from '@/materials/ruleParamTable';
import store from '@/store';
import { Col, Form, FormInstance, Row, Select } from 'antd';
import { MutableRefObject, useCallback, useEffect, useRef, useState } from 'react';

// 类型定义
interface ParamItem {
  name: string;
  desc: string;
  type: string;
  value: string;
  isEdit: boolean;
}

interface EditingRow {
  field: 'input' | 'output';
  idx: number;
}

interface LabelSettingProps {
  formRef?: MutableRefObject<FormInstance | null>;
  params?: { input: ParamItem[] };
}

const defaultParam: ParamItem = {
  name: '',
  desc: '',
  type: 'string',
  value: '',
  isEdit: false,
};

const LabelSetting: React.FC<LabelSettingProps> = ({ formRef, params }) => {
  const [form] = Form.useForm();
  const [dictState] = store.useModel('dict');
  const { translate } = useIntlCustom();

  // 状态管理
  const editableFormRef: any = useRef();
  const [inputData, setInputData] = useState<ParamItem[]>([]);
  const [editingRow, setEditingRow] = useState<EditingRow | null>(null);
  const [valueInputModes, setValueInputModes] = useState<{ input: boolean[] }>({ input: [] });

  // refs
  // 用于聚焦新行输入框
  const inputRefs = useRef<{ input: (HTMLInputElement | null)[] }>({
    input: [],
  });

  // 可编辑表格列配置
  const editRuleColumns = [
    {
      width: 120,
      key: 'priority',
      title: 'priority',
      dataIndex: 'priority',
      formItemProps: (_form: any, { rowIndex }: { rowIndex: number }) => ({
        rules: [{ required: !!rowIndex, message: '请输入优先级' }],
      }),
    },
    {
      title: 'nodeAttriId',
      dataIndex: 'nodeAttriId',
      valueType: 'select' as const,
      showKey: true,
      valueEnum: () => {
        const result: Record<string, { text: string }> = {};
        dictState.dictMap?.nodeAttriId?.forEach((item: any) => {
          const { key, value } = item;
          result[key] = { text: value };
        });
        return result;
      },
      formItemProps: (_form: any, { rowIndex }: { rowIndex: number }) => ({
        rules: [{ required: !!rowIndex, message: '请选择节点属性' }],
      }),
    },
  ];

  // 初始化数据
  useEffect(() => {
    if (params?.input) {
      const safeInput = Array.isArray(params.input)
        ? params.input.filter((item): item is ParamItem => item && typeof item === 'object' && 'name' in item)
        : [];

      const processedData = safeInput.map((item) => ({ ...item, isEdit: false }));
      setInputData(processedData);
      form.setFieldsValue({ input: processedData });

      // 重置refs和状态
      inputRefs.current = { input: [] };
      setEditingRow(null);
      setValueInputModes({ input: new Array(processedData.length).fill(true) });
    }
  }, [params, form]);

  // 同步表单数据
  useEffect(() => {
    form.setFieldsValue({ input: inputData });
  }, [inputData, form]);

  // 暴露表单实例和验证方法给父组件
  useEffect(() => {
    if (formRef) {
      formRef.current = {
        validateFields: async () => {
          try {
            // 验证基本表单
            const formData = await form.validateFields();
            // 验证可编辑表格
            const editTableData = await editableFormRef.current?.validateFields();

            return {
              ...formData,
              editRuleTableData: editTableData || [],
            };
          } catch (error) {
            throw error;
          }
        },
        getFieldsValue: () => {
          const formData = form.getFieldsValue();
          const editTableData = editableFormRef.current?.getFieldsValue?.() || [];
          return {
            ...formData,
            editRuleTableData: editTableData,
          };
        },
        // @ts-ignore
        form,
      };
    }
  }, [formRef, form]);

  // 处理输入数据变化
  const handleInputDataChange = useCallback((data: ParamItem[]) => {
    setInputData(data);
  }, []);

  // 处理输入模式变化
  const handleInputValueModesChange = useCallback((modes: boolean[]) => {
    setValueInputModes((prev) => ({ ...prev, input: modes }));
  }, []);

  return (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            layout="horizontal"
            label={translate(I18N_COMON_PAGENAME.CALL_PARAM, 'ruleType')}
            name="ruleType"
            rules={[{ required: true, message: translate(I18N_COMON_PAGENAME.CALL_PARAM, 'pleaseSelectRuleType') }]}
          >
            <Select placeholder={translate(I18N_COMON_PAGENAME.CALL_PARAM, 'pleaseSelectRuleType')} />
          </Form.Item>
        </Col>

        <Col span={12}>
          <Form.Item
            layout="horizontal"
            label={translate(I18N_COMON_PAGENAME.CALL_PARAM, 'rulePackageId')}
            name="rulePackageId"
            rules={[{ message: translate(I18N_COMON_PAGENAME.CALL_PARAM, 'pleaseSelectRulePackageId') }]}
          >
            <Select placeholder={translate(I18N_COMON_PAGENAME.CALL_PARAM, 'pleaseSelectRulePackageId')} />
          </Form.Item>
        </Col>

        <Col span={12}>
          <Form.Item
            layout="horizontal"
            label={translate(I18N_COMON_PAGENAME.CALL_PARAM, 'selectList')}
            name="selectList"
            rules={[{ message: translate(I18N_COMON_PAGENAME.CALL_PARAM, 'pleaseSelectList') }]}
          >
            <Select placeholder={translate(I18N_COMON_PAGENAME.CALL_PARAM, 'pleaseSelectList')} />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label="入参配置">
        <RuleParamTable
          field="input"
          data={inputData}
          valueModes={valueInputModes.input}
          onDataChange={handleInputDataChange}
          onValueModesChange={handleInputValueModesChange}
          onEditingRowChange={setEditingRow}
          editingRow={editingRow}
          inputRefs={inputRefs}
          form={form}
        />
      </Form.Item>

      <Form.Item label="命中程序配置">
        <EditTable
          intlPrefix="callParam"
          columns={editRuleColumns}
          rowKey="id"
          dataSource={[]}
          ref={editableFormRef}
          optionCount={2}
          canEdit={true}
          // @ts-ignore
          getOptionList={() => [
            {
              optionType: OPERATE_TYPE.edit,
            },
          ]}
        />
      </Form.Item>
    </Form>
  );
};

export default LabelSetting;
