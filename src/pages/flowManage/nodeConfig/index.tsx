import { OPERATE_TYPE } from '@/constants/publicConstant';
import React, { Suspense, useRef, useState } from 'react';
import ParamSettingDrawer from './components/ParamSettingDrawer';
import { data1, data2, data3 } from './mockData';
import { dictEnum, pageConfig } from './pageConfig';
import useFormConfig from './useFormConfig';
const PageTemplate = React.lazy(() => import('@/components/templates/PageTemplate'));
const DargeGraph = React.lazy(() => import('@/materials/dargeGraph'));

const NodeManagePage: React.FC = () => {
  const { urlObj, prefix, searchSource, columns, cardTitle, resetValue, formActionPermissionObj, optionList } =
    pageConfig;

  const [type, setType] = useState<string>(OPERATE_TYPE.list);
  const [detailData, setDetailData] = useState<any>({});
  const [flowData, setFlowData] = useState<any>({});
  const [paramDrawerOpen, setParamDrawerOpen] = useState(false);
  const [paramDrawerParams, setParamDrawerParams] = useState({ input: [], output: [] });
  const [paramDrawerRow, setParamDrawerRow] = useState<any>(null);
  const formRef = useRef<any>(null);

  // 打开参数设置弹层
  const openParamSettingDrawer = (row: any) => {
    // 这里假设row.input/outputParams为参数数据字段名，根据实际数据结构调整
    setParamDrawerParams({
      ...row,
      input: row.input || [],
      output: row.output || [],
    });
    setParamDrawerRow(row);
    setParamDrawerOpen(true);
  };

  // 传递openParamSettingDrawer到useFormConfig
  const formConfigResult = useFormConfig(type, { openParamSettingDrawer });
  const { infoFormConfig, editableFormRef, setEditTableData } = formConfigResult;

  // 参数设置保存
  const handleParamDrawerSave = (params: { input: any[]; output: any[] }) => {
    console.log('2222----params', params);
    // 这里可以将参数保存到row或发请求，视业务需求
    if (paramDrawerRow) {
      paramDrawerRow.input = params.input;
      paramDrawerRow.output = params.output;
      // 可选：setDetailData({...detailData}); // 如需刷新页面
      setDetailData({ ...params, ...paramDrawerRow }); // 强制触发一次数据更新，确保页面响应
    }
  };

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 表格配置
  const tableConfig = {
    columns,
    rowKey: 'paramIndex',
    showPagination: true,
    optionList,
  };

  // 表单配置
  const formConfig = {
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
    props: {
      editTableRefList: {
        nodeProgramList: editableFormRef,
        formRef,
      },
    },
    customChildren: {
      [OPERATE_TYPE.flowNode]: (
        <DargeGraph
          data={flowData}
          // @ts-ignore
          behaviors={['zoom-canvas', 'drag-element', 'drag-canvas']}
          canNodeClick={false}
        />
      ),
    },
  };

  // 列表按钮操作
  const handleAction = (editType: string, row: any) => {
    let flowData = {};
    if ([1, 2, 3].includes(row.paramIndex)) {
      flowData = data1;
    } else if (row.paramIndex === 4) {
      flowData = data2;
    } else if (row.paramIndex === 5) {
      flowData = data3;
    } else {
      flowData = data1;
    }
    setFlowData(flowData);

    setDetailData({ ...row, nodeAuth: row.nodeAuth?.split(',') });
    setType(editType);
    if (editType === OPERATE_TYPE.edit) {
      // const newModeManageList = row?.nodeProgramList.map((item, index) => ({
      //   ...item,
      //   id: index + 1,
      // }));
      // setEditTableData(newModeManageList as any[]);
    } else {
      setEditTableData([]);
    }
  };

  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        onAction={handleAction}
        formActionConfig={{
          permissionObj: formActionPermissionObj,
        }}
      />
      <ParamSettingDrawer
        formRef={formRef}
        open={paramDrawerOpen}
        onClose={() => setParamDrawerOpen(false)}
        params={paramDrawerParams}
        onSave={handleParamDrawerSave}
      />
    </Suspense>
  );
};
export default React.memo(NodeManagePage);
