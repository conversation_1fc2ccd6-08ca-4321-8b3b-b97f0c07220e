import useIntlCustom from '@/hooks/useIntlCustom';
import { Button, Drawer, Form, Input, Popconfirm, Select, Space, Table, message } from 'antd';
import { useEffect, useRef, useState } from 'react';

const PARAM_TYPES = [
  { label: 'string', value: 'string' },
  { label: 'number', value: 'number' },
  { label: 'boolean', value: 'boolean' },
  // 可根据需要扩展
];

const defaultParam = { name: '', desc: '', type: 'string', value: '', isEdit: false };
type ParamItem = typeof defaultParam;

const ParamSettingDrawer = ({ open, onClose, params = { input: [], output: [] }, onSave }) => {
  const [form] = Form.useForm();
  // 用于聚焦新行输入框
  const inputRefs = useRef<{ input: (HTMLInputElement | null)[]; output: (HTMLInputElement | null)[] }>({
    input: [],
    output: [],
  });
  // 新增：本地 state 管理 input/output 数据
  const [inputData, setInputData] = useState<ParamItem[]>([]);
  const [outputData, setOutputData] = useState<ParamItem[]>([]);
  // 当前正在编辑的行索引
  const [editingRow, setEditingRow] = useState<{ field: 'input' | 'output'; idx: number } | null>(null);

  const { translate } = useIntlCustom();
  const prefix = 'callParam';

  // 初始化和 params 变化时同步 state 和 form
  useEffect(() => {
    const safeInput = Array.isArray(params.input) ? params.input.filter((i) => i && typeof i === 'object') : [];
    const safeOutput = Array.isArray(params.output) ? params.output.filter((i) => i && typeof i === 'object') : [];
    setInputData(safeInput.map((item) => ({ ...(item as ParamItem), isEdit: false })));
    setOutputData(safeOutput.map((item) => ({ ...(item as ParamItem), isEdit: false })));
    form.setFieldsValue({ input: safeInput, output: safeOutput });
    inputRefs.current = { input: [], output: [] };
    setEditingRow(null);
  }, [params, form]);

  // 保证 form 数据和 state 同步
  useEffect(() => {
    form.setFieldsValue({ input: inputData });
  }, [inputData, form]);
  useEffect(() => {
    form.setFieldsValue({ output: outputData });
  }, [outputData, form]);

  // 新增时进入编辑状态
  const handleAdd = (field) => {
    const values = field === 'input' ? inputData : outputData;
    if (values.some((item) => item.isEdit || !item.name)) {
      message.warning(translate(prefix, 'pleaseFinishParamFirst'));
      return;
    }
    const newData = [...values, { ...defaultParam, isEdit: true }];
    if (field === 'input') {
      setInputData(newData);
    } else {
      setOutputData(newData);
    }
    setEditingRow({ field, idx: newData.length - 1 });
    setTimeout(() => {
      const refArr = inputRefs.current[field];
      if (refArr && refArr.length > 0) {
        const lastRef = refArr[refArr.length - 1];
        if (lastRef && lastRef.focus) lastRef.focus();
      }
    }, 100);
  };

  // 编辑按钮
  const handleEdit = (field, idx) => {
    if (editingRow) return;
    if (field === 'input') {
      setInputData(inputData.map((item, i) => ({ ...item, isEdit: i === idx })));
    } else {
      setOutputData(outputData.map((item, i) => ({ ...item, isEdit: i === idx })));
    }
    setEditingRow({ field, idx });
    setTimeout(() => {
      const refArr = inputRefs.current[field];
      if (refArr && refArr[idx] && refArr[idx].focus) refArr[idx].focus();
    }, 100);
  };

  // 保存按钮
  const handleSaveRow = (field, idx) => {
    form.validateFields().then((values) => {
      const list = values[field] || [];
      if (field === 'input') {
        setInputData(list.map((item, i) => ({ ...item, isEdit: false })));
      } else {
        setOutputData(list.map((item, i) => ({ ...item, isEdit: false })));
      }
      setEditingRow(null);
    });
  };

  // 取消按钮
  const handleCancelRow = (field, idx) => {
    if (field === 'input') {
      const newData = [...inputData];
      // 新增行直接删除，编辑行还原
      if (!newData[idx].name && !newData[idx].desc && !newData[idx].value) {
        newData.splice(idx, 1);
      } else {
        newData[idx].isEdit = false;
      }
      setInputData(newData);
    } else {
      const newData = [...outputData];
      if (!newData[idx].name && !newData[idx].desc && !newData[idx].value) {
        newData.splice(idx, 1);
      } else {
        newData[idx].isEdit = false;
      }
      setOutputData(newData);
    }
    setEditingRow(null);
  };

  const handleRemove = (field, idx) => {
    const values = field === 'input' ? inputData : outputData;
    const newData = values.filter((_, i) => i !== idx);
    if (field === 'input') {
      setInputData(newData);
    } else {
      setOutputData(newData);
    }
  };

  const handleSave = () => {
    form.validateFields().then((values) => {
      onSave && onSave(values);
      onClose();
    });
  };

  const renderTable = (field) => {
    const data = field === 'input' ? inputData : outputData;
    inputRefs.current[field] = inputRefs.current[field] || [];
    inputRefs.current[field].length = data.length;
    return (
      <Table
        dataSource={data}
        rowKey={(_, idx) => String(idx)}
        pagination={false}
        size="small"
        columns={[
          {
            title: translate(prefix, 'paramName'),
            dataIndex: 'name',
            render: (text, _, idx) => (
              <Form.Item name={[field, idx, 'name']} rules={[{ required: true, message: '必填' }]} noStyle>
                <Input
                  placeholder={translate(prefix, 'paramName')}
                  ref={(el) => (inputRefs.current[field][idx] = el && el.input)}
                  disabled={!data[idx].isEdit}
                />
              </Form.Item>
            ),
          },
          {
            title: translate(prefix, 'paramDesc'),
            dataIndex: 'desc',
            render: (text, _, idx) => (
              <Form.Item name={[field, idx, 'desc']} noStyle>
                <Input placeholder={translate(prefix, 'paramDesc')} disabled={!data[idx].isEdit} />
              </Form.Item>
            ),
          },
          {
            title: translate(prefix, 'type'),
            dataIndex: 'type',
            render: (text, _, idx) => (
              <Form.Item name={[field, idx, 'type']} noStyle>
                <Select options={PARAM_TYPES} style={{ width: 80 }} disabled={!data[idx].isEdit} />
              </Form.Item>
            ),
          },
          {
            title: translate(prefix, 'value'),
            dataIndex: 'value',
            render: (text, _, idx) => (
              <Form.Item name={[field, idx, 'value']} noStyle>
                <Input placeholder={translate(prefix, 'value')} disabled={!data[idx].isEdit} />
              </Form.Item>
            ),
          },
          {
            title: translate(prefix, 'action'),
            width: 80,
            dataIndex: 'action',
            render: (_, __, idx) => {
              if (data[idx].isEdit) {
                return (
                  <Space>
                    <a onClick={() => handleSaveRow(field, idx)}>{translate(prefix, 'save')}</a>
                    <a onClick={() => handleCancelRow(field, idx)}>{translate(prefix, 'cancel')}</a>
                  </Space>
                );
              }
              return (
                <Space>
                  <a onClick={() => handleEdit(field, idx)}>{translate(prefix, 'edit')}</a>
                  <Popconfirm title={translate(prefix, 'deleteConfirm')} onConfirm={() => handleRemove(field, idx)}>
                    <a>{translate(prefix, 'delete')}</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
        footer={() => (
          <Button type="dashed" block onClick={() => handleAdd(field)}>
            {translate(prefix, 'add')}
          </Button>
        )}
      />
    );
  };

  return (
    <Drawer
      title={translate(prefix, 'paramSettingTitle')}
      width={700}
      open={open}
      onClose={onClose}
      maskClosable={false}
      extra={
        <Space>
          <Button onClick={onClose}>{translate(prefix, 'cancel')}</Button>
          <Button type="primary" onClick={handleSave}>
            {translate(prefix, 'save')}
          </Button>
        </Space>
      }
    >
      <Form form={form} layout="vertical" initialValues={params}>
        <div style={{ marginBottom: 24 }}>
          <div style={{ fontWeight: 600, marginBottom: 8 }}>{translate(prefix, 'input')}</div>
          {renderTable('input')}
        </div>
        <div>
          <div style={{ fontWeight: 600, marginBottom: 8 }}>{translate(prefix, 'output')}</div>
          {renderTable('output')}
        </div>
      </Form>
    </Drawer>
  );
};

export default ParamSettingDrawer;
