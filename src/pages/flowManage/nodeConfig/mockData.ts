import { intlConst } from '@/hooks/useIntlCustom';

export const data1 = {
  nodes: [
    {
      id: '1',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'preProcess'),
      style: { x: 300, y: 300 },
    },
    {
      id: '2',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'decisionDataLoad'),
    },
    {
      id: '3',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'writeOffRules'),
    },
    {
      id: '4',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'writeOffQueue'),
    },
    {
      id: '5',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'customerTagProcess'),
    },
    {
      id: '6',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'customerCollectionMark'),
    },
    {
      id: '7',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'sectionRules'),
    },
    {
      id: '8',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'phoneCollectionQueue'),
    },
    {
      id: '9',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'outsourceQueue'),
    },
    {
      id: '10',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'litigationQueue'),
    },
    {
      id: '11',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'smsQueue'),
    },
    {
      id: '12',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'ivrQueue'),
    },
    {
      id: '13',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'phoneCollectionGroupA'),
    },
    {
      id: '14',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'phoneCollectionGroupB'),
    },
    {
      id: '15',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'phoneCollectionGroupC'),
    },
  ],
  edges: [
    {
      id: 'edge-1',
      source: '1',
      target: '2',
    },
    {
      id: 'edge-2',
      source: '2',
      target: '3',
    },
    {
      id: 'edge-3',
      source: '3',
      target: '4',
    },
    {
      id: 'edge-4',
      source: '3',
      target: '5',
    },
    {
      id: 'edge-5',
      source: '5',
      target: '6',
    },
    {
      id: 'edge-6',
      source: '6',
      target: '7',
    },
    {
      id: 'edge-7',
      source: '7',
      target: '8',
    },
    {
      id: 'edge-8',
      source: '7',
      target: '9',
    },
    {
      id: 'edge-9',
      source: '7',
      target: '10',
    },
    {
      id: 'edge-10',
      source: '8',
      target: '11',
    },
    {
      id: 'edge-11',
      source: '8',
      target: '12',
    },
    {
      id: 'edge-12',
      source: '8',
      target: '13',
    },
    {
      id: 'edge-13',
      source: '8',
      target: '14',
    },
    {
      id: 'edge-14',
      source: '8',
      target: '15',
    },
  ],
};

export const data2 = {
  nodes: [
    {
      id: '0',
      label: intlConst.formatMessage('callParam', 'start'),
    },
    {
      id: '1',
      color: '#4CAF50',
      label: intlConst.formatMessage('callParam', 'preProcess'),
      data: {
        nodeDialogName: intlConst.formatMessage('callParam', 'preProcess'),
        nodeExecStatus: intlConst.formatMessage('callParam', 'success'),
        nodeExecCount: 1,
        lastExecDuration: '111ms',
        lastExecTime: '2025-03-03  08:00:013:113456',
        nextNode: intlConst.formatMessage('callParam', 'decisionDataLoad'),
        nodeProgram: [
          intlConst.formatMessage('callParam', 'accountDataSync'),
          intlConst.formatMessage('callParam', 'cardDataSync'),
          intlConst.formatMessage('callParam', 'cardholderDataSync'),
          intlConst.formatMessage('callParam', 'transactionDataSync'),
          intlConst.formatMessage('callParam', 'operationCenterProcess'),
        ],
      },
    },
    {
      id: '2',
      color: '#FFEB3B',
      label: intlConst.formatMessage('callParam', 'decisionDataLoad'),
      data: {
        nodeDialogName: intlConst.formatMessage('callParam', 'decisionDataLoad'),
        nodeExecStatus: intlConst.formatMessage('callParam', 'success'),
        nodeExecCount: 1,
        lastExecDuration: '90ms',
        lastExecTime: '2025-03-03  08:00:013:113456',
        nextNode: intlConst.formatMessage('callParam', 'writeOffRules'),
        nodeProgram: [
          intlConst.formatMessage('callParam', 'creditScoreNode'),
          intlConst.formatMessage('callParam', 'overduePredictionNode'),
          intlConst.formatMessage('callParam', 'lostContactRepairNode'),
          intlConst.formatMessage('callParam', 'operationCenterProcess'),
        ],
      },
    },
    {
      id: '3',
      color: '#BBDEFB',
      label: intlConst.formatMessage('callParam', 'writeOffRules'),
      data: {
        nodeDialogName: intlConst.formatMessage('callParam', 'writeOffRules'),
        nodeExecStatus: intlConst.formatMessage('callParam', 'success'),
        nodeExecCount: 2,
        lastExecDuration: '18ms',
        lastExecTime: '2025-03-03  08:00:015:113456',
        nextNode: intlConst.formatMessage('callParam', 'customerTagProcess'),
        nodeProgram: ['核销规则', '运营中心处理'],
      },
    },
    {
      id: '4',
      color: '#FF9800',
      label: intlConst.formatMessage('callParam', 'customerTagProcess'),
      data: {
        nodeDialogName: intlConst.formatMessage('callParam', 'customerTagProcess'),
        nodeExecStatus: intlConst.formatMessage('callParam', 'success'),
        nodeExecCount: 1,
        lastExecDuration: '9ms',
        lastExecTime: '2025-03-03  08:00:016:156123',
        nextNode: intlConst.formatMessage('callParam', 'customerCollectionMark'),
        nodeProgram: ['上客户标签处理', '下客户标签处理', '运营中心处理'],
      },
    },
    {
      id: '5',
      color: '#388E3C',
      label: intlConst.formatMessage('callParam', 'phoneCollectionQueue'),
      data: {
        nodeDialogName: intlConst.formatMessage('callParam', 'customerCollectionMark'),
        nodeExecStatus: intlConst.formatMessage('callParam', 'success'),
        nodeExecCount: 1,
        lastExecDuration: '1ms',
        lastExecTime: '2025-03-03  08:00:016:156123',
        nextNode: intlConst.formatMessage('callParam', 'sectionRules'),
        nodeProgram: ['上客户催收标识处理', '下客户催收标识处理', '运营中心处理'],
      },
    },
    {
      id: '6',
      color: '#BDBDBD',
      label: intlConst.formatMessage('callParam', 'phoneCollectionWorkbench'),
      data: {
        nodeDialogName: intlConst.formatMessage('callParam', 'sectionRules'),
        nodeExecStatus: intlConst.formatMessage('callParam', 'success'),
        nodeExecCount: 1,
        lastExecDuration: '16ms',
        lastExecTime: '2025-03-03  08:00:018:218991',
        nextNode: intlConst.formatMessage('callParam', 'smsCollectionQueue'),
        nodeProgram: ['上客户催收标识处理', '下客户催收标识处理', '运营中心处理'],
      },
    },
    {
      id: '7',
      color: '#388E3C',
      label: intlConst.formatMessage('callParam', 'smsCollectionQueue'),
      data: {
        nodeDialogName: intlConst.formatMessage('callParam', 'smsCollectionQueue'),
        nodeExecStatus: intlConst.formatMessage('callParam', 'success'),
        nodeExecCount: 1,
        lastExecDuration: '16ms',
        lastExecTime: '2025-03-03  08:00:018:218991',
        nextNode: intlConst.formatMessage('callParam', 'smsCollectionQueue'),
        nodeProgram: ['短信催收队列处理', '运营中心处理'],
      },
    },
    {
      id: '8',
      color: '#BDBDBD',
      label: intlConst.formatMessage('callParam', 'smsCollectionWorkbench'),
      data: {
        nodeDialogName: intlConst.formatMessage('callParam', 'smsCollectionWorkbench'),
        nodeExecStatus: intlConst.formatMessage('callParam', 'executing'),
        nodeExecCount: 0,
        nodeProgram: [
          '查询卡账客信息处理',
          '查询行动历史处理',
          '查询联系人信息处理',
          '查询短信模板处理',
          '调用短信平台接口处理',
          '运营中心处理',
        ],
      },
    },
  ],
  edges: [
    {
      id: 'edge-1',
      source: '0',
      target: '1',
    },
    {
      id: 'edge-2',
      source: '1',
      target: '2',
    },
    {
      id: 'edge-3',
      source: '1',
      target: '3',
    },
    {
      id: 'edge-4',
      source: '3',
      target: '4',
    },
    {
      id: 'edge-5',
      source: '2',
      target: '5',
    },
    {
      id: 'edge-6',
      source: '5',
      target: '6',
    },
    {
      id: 'edge-7',
      source: '2',
      target: '7',
    },
    {
      id: 'edge-8',
      source: '7',
      target: '8',
    },
  ],
};

export const data3 = {
  nodes: [
    {
      id: '0',
      label: '开始',
    },
    {
      id: '1',
      color: '#4CAF50',
      label: '1.1-预处理',
      data: {
        nodeDialogName: '预处理',
        nodeExecStatus: '成功',
        nodeExecCount: 1,
        lastExecDuration: '111ms',
        lastExecTime: '2025-03-03  08:00:013:113456',
        nextNode: '2-决策数据加载',
        nodeProgram: [
          '账户数据同步批处理',
          '卡片数据同步批处理',
          '持卡人数据同步批处理',
          '交易信息同步批处理',
          '运营中心处理',
        ],
      },
    },
    {
      id: '2',
      color: '#FFEB3B',
      label: '决策数据加载',
      data: {
        nodeDialogName: '2-决策数据加载',
        nodeExecStatus: '成功',
        nodeExecCount: 1,
        lastExecDuration: '90ms',
        lastExecTime: '2025-03-03  08:00:013:113456',
        nextNode: '4-核销规则',
        nodeProgram: ['信用评分节点', '逾期风险预测节点', '失联修复文件节点', '运营中心处理'],
      },
    },
    {
      id: '3',
      color: '#BBDEFB',
      label: '核销规则',
      data: {
        nodeDialogName: '4-核销规则',
        nodeExecStatus: '成功',
        nodeExecCount: 2,
        lastExecDuration: '18ms',
        lastExecTime: '2025-03-03  08:00:015:113456',
        nextNode: '5.2-客户标签处理',
        nodeProgram: ['核销规则', '运营中心处理'],
      },
    },
    {
      id: '4',
      color: '#FF9800',
      label: '客户标签处理',
      data: {
        nodeDialogName: '5.2-客户标签处理',
        nodeExecStatus: '成功',
        nodeExecCount: 1,
        lastExecDuration: '9ms',
        lastExecTime: '2025-03-03  08:00:016:156123',
        nextNode: '6-客户催收标识处理',
        nodeProgram: ['上客户标签处理', '下客户标签处理', '运营中心处理'],
      },
    },
    {
      id: '5',
      color: '#9C27B0',
      label: '客户催收标识处理',
      data: {
        nodeDialogName: '6-客户催收标识处理',
        nodeExecStatus: '成功',
        nodeExecCount: 1,
        lastExecDuration: '1ms',
        lastExecTime: '2025-03-03  08:00:016:156123',
        nextNode: '7-分板块规则',
        nodeProgram: ['上客户催收标识处理', '下客户催收标识处理', '运营中心处理'],
      },
    },
    {
      id: '6',
      color: '#F44336',
      label: '分板块规则',
      data: {
        nodeDialogName: '7-分板块规则',
        nodeExecStatus: '成功',
        nodeExecCount: 1,
        lastExecDuration: '16ms',
        lastExecTime: '2025-03-03  08:00:018:218991',
        nextNode: '8.1-短信催收队列',
        nodeProgram: ['上客户催收标识处理', '下客户催收标识处理', '运营中心处理'],
      },
    },
    {
      id: '7',
      color: '#388E3C',
      label: '短信催收队列',
      data: {
        nodeDialogName: '8.1-短信催收队列',
        nodeExecStatus: '成功',
        nodeExecCount: 1,
        lastExecDuration: '16ms',
        lastExecTime: '2025-03-03  08:00:018:218991',
        nextNode: '8.1-短信催收队列',
        nodeProgram: ['短信催收队列处理', '运营中心处理'],
      },
    },
    {
      id: '8',
      color: '#BDBDBD',
      label: '短信催收工作台',
      data: {
        nodeDialogName: '短信催收工作台',
        nodeExecStatus: '执行中',
        nodeExecCount: 0,
        nodeProgram: [
          '查询卡账客信息处理',
          '查询行动历史处理',
          '查询联系人信息处理',
          '查询短信模板处理',
          '调用短信平台接口处理',
          '运营中心处理',
        ],
      },
    },
  ],
  edges: [
    {
      id: 'edge-1',
      source: '0',
      target: '1',
    },
    {
      id: 'edge-2',
      source: '1',
      target: '2',
    },
    {
      id: 'edge-3',
      source: '1',
      target: '3',
    },
    {
      id: 'edge-4',
      source: '2',
      target: '4',
    },
    {
      id: 'edge-5',
      source: '2',
      target: '5',
    },
    {
      id: 'edge-6',
      source: '5',
      target: '6',
    },
    {
      id: 'edge-7',
      source: '6',
      target: '7',
    },
    {
      id: 'edge-8',
      source: '6',
      target: '8',
    },
  ],
};
