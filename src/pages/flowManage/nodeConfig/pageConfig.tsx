import DICT_CONSTANTS from '@/constants/dictConstants';
import PERMISSION_CONSTANTS from '@/constants/permissionConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, OPERATE_TYPE, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import _ from 'lodash';
// import { getRequestData } from '@/utils/urlUtil';

const getRequestData = (postData, type: string, rowData): object => {
  switch (type) {
    case OPERATE_TYPE.edit:
    case OPERATE_TYPE.create:
      const nodeProgramList = _.map(postData.nodeProgramList, (item) =>
        _.omit(item, ['isAdd', 'index', 'paramIndex', 'id']),
      ).map((ite) => ({
        ...ite,
        nodeCode: postData.formData.nodeCode,
        nodeNodeName: postData.formData.nodeNodeName,
      }));
      return {
        ...postData.formData,
        nodeAuth: postData.formData?.nodeAuth.join(','),
        nodeProgramList,
      };
    default:
      return { ...postData };
  }
};
export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.CALL_PARAM,
  cardTitle: 'nodeConfig',
  // 页面接口请求
  urlObj: {
    list: urlConstants.ADUIT_MANAGE.LIST,
    create: urlConstants.NODE_MANAGE.CREATE,
    edit: urlConstants.NODE_MANAGE.EDIT,
    getRequestData,
  },
  resetValue: {
    nodeCode: '',
    nodeNodeName: '',
    nodeType: null,
    status: null,
  },
  // 搜索
  searchSource: [
    {
      value: 'nodeCode',
      label: 'nodeCode',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'nodeNodeName',
      label: 'nodeNodeName',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'nodeType',
      label: 'nodeType',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.NODE_TYPE,
    },
    {
      value: 'status',
      label: 'status',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.NODE_STATUS,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'nodeCode',
      dataIndex: 'nodeCode',
      key: 'nodeCode',
      width: 120,
    },
    {
      width: 120,
      title: 'nodeNodeName',
      dataIndex: 'nodeNodeName',
      key: 'nodeNodeName',
    },
    {
      width: 150,
      title: 'nodeFuncDesc',
      dataIndex: 'nodeFuncDesc',
      key: 'nodeFuncDesc',
      render: (text) => <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>,
    },
    {
      title: 'nodeType',
      dataIndex: 'nodeType',
      key: 'nodeType',
      data: DICT_CONSTANTS.NODE_TYPE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'NODE_TYPE',
      width: 120,
    },
    {
      width: 100,
      key: 'status',
      title: 'status',
      dataIndex: 'status',
      data: DICT_CONSTANTS.NODE_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'NODE_STATUS',
    },
    {
      width: 160,
      title: 'nodeAuth',
      dataIndex: 'nodeAuth',
      key: 'nodeAuth',
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      width: 160,
      valueType: RENDER_TYPE.DateTime,
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      width: 120,
    },
  ],
  // 操作列
  optionList: [
    { type: OPERATE_TYPE.detail, permissionId: PERMISSION_CONSTANTS.AUTH_ORIZATION_CONTROL.DETAIL },
    { type: OPERATE_TYPE.edit, permissionId: PERMISSION_CONSTANTS.AUTH_ORIZATION_CONTROL.EDIT },
    { type: OPERATE_TYPE.flowNode, permissionId: PERMISSION_CONSTANTS.AUTH_ORIZATION_CONTROL.EDIT },
  ],
  // 新增按钮权限
  formActionPermissionObj: {
    create: PERMISSION_CONSTANTS.AUTH_ORIZATION_CONTROL.CREATE,
  },
};

// 需要查询的参数类型
export const dictEnum = {
  nodeAttriId: DICT_CONSTANTS.DICT_ENUM_MAP.nodeAttriId,
  // nodeRule: DICT_CONSTANTS.DICT_ENUM_MAP.nodeRule,
  // nodeRuleFactor: DICT_CONSTANTS.DICT_ENUM_MAP.nodeRuleFactor,
};
