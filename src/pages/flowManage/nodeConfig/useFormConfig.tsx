import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import store from '@/store';
import { IFormConfig } from '@/types/IForm';
import { EditableFormInstance } from '@ant-design/pro-components';
import { useRef, useState } from 'react';

const useFormConfig = (type: string, options?: { openParamSettingDrawer?: (row: any) => void }) => {
  const { translate } = useIntlCustom();
  const [dictState] = store.useModel('dict');
  const [editType, setEditType] = useState<string>(type);
  const [editTableData, setEditTableData] = useState<any[]>([]);
  const editableFormRef = useRef<EditableFormInstance>();

  // 可编辑表格操作列
  const editColumns = [
    {
      width: 120,
      key: 'priority',
      title: 'priority',
      dataIndex: 'priority',
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: 'programCode',
      dataIndex: 'programCode',
      valueType: 'select',
      showKey: true,
      valueEnum: () => {
        const result = {};
        dictState.dictMap?.nodeAttriId?.map((item) => {
          const { key, value } = item;
          result[key] = { text: value };
        });
        return result;
      },
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: 'cycleStartFlag',
      dataIndex: 'cycleStartFlag',
      valueType: 'select',
      showKey: true,
    },
  ];
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'nodeCode',
          label: 'nodeCode',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'nodeNodeName',
          label: 'nodeNodeName',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'nodeAuth',
          label: 'nodeAuth',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.NODE_AUTH,
          mode: 'multiple',
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'nodeType',
          label: 'nodeType',
          data: DICT_CONSTANTS.NODE_TYPE,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'status',
          label: 'status',
          data: DICT_CONSTANTS.NODE_STATUS,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'nodeFuncDesc',
          label: 'nodeFuncDesc',
          rules: [{ required: true }],
        },
      ],
    },
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'nodeAttribute',
    },
    {
      type: FORMITEM_TYPE.EditTable,
      rowKey: 'id',
      columns: editColumns,
      dataSource: editTableData,
      canEdit: true,
      editableFormRef,
      optionCount: 1,
      getOptionList: (row) => [
        {
          optionType: OPERATE_TYPE.up,
          disabled: editType === OPERATE_TYPE.detail,
        },
        {
          optionType: OPERATE_TYPE.down,
          disabled: editType === OPERATE_TYPE.detail,
        },
        {
          optionType: OPERATE_TYPE.edit,
          disabled: editType === OPERATE_TYPE.detail,
        },
        {
          optionType: OPERATE_TYPE.detail,
          disabled: editType === OPERATE_TYPE.detail,
        },
        {
          optionType: OPERATE_TYPE.delete,
          disabled: editType === OPERATE_TYPE.detail,
        },
      ],
      onAction: (optionType, row) => {
        if (optionType === OPERATE_TYPE.detail && options?.openParamSettingDrawer) {
          options.openParamSettingDrawer(row);
        }
        // setShowRuleconfig(row.nodeAttriId === 'NodeFlowRules');
      },
      onCreate: (index) => ({ id: Date.now() + 1, isAdd: true }),
    },
  ];

  return {
    infoFormConfig,
    editableFormRef,
    setEditTableData,
  };
};

export default useFormConfig;
