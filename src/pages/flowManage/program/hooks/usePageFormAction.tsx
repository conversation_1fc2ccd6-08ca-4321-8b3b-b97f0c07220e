// formAction组件hooks
import { FormAction } from '@/components';
import { OPERATE_TYPE } from '@/constants/publicConstant';

const usePageFormAction = ({ formRef, getTableData, detailType, handleAction, handleCardBack }) => {
  // 表单提交事件
  const handleDrawerSubmit = async () => {
    formRef.current
      ?.validateFields()
      .then((values) => {
        console.log('2222-values--保存', values);
        if (values) {
          handleCardBack();
          getTableData();
        }
      })
      .catch((error) => {
        console.error(error);
      });
  };

  // 渲染formAction组件
  const renderFormAction = () => {
    return (
      <FormAction
        showCreate={detailType === OPERATE_TYPE.list}
        showSubmit={false}
        onCreate={() => handleAction(OPERATE_TYPE.create, {})}
      />
    );
  };
  return { renderFormAction, handleDrawerSubmit };
};

export default usePageFormAction;
