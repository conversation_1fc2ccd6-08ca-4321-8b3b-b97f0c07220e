// Search组件hooks
import { Search } from '@/components';
import { OPERATE_TYPE } from '@/constants/publicConstant';
import { useState } from 'react';
import { pageConfig } from '../pageConfig';

const useSearch = ({ getTableData, handleAction }) => {
  const { prefix, searchSource, resetValue } = pageConfig;
  const [searchValue, setSearchValue] = useState<any>({ ...resetValue });

  // 查询事件
  const handleSearch = (searchValue: any): void => {
    setSearchValue(searchValue);
    getTableData(searchValue);
  };

  // 渲染组件
  const searchChildren = () => {
    return (
      <Search
        searchValue={searchValue}
        resetValue={resetValue}
        searchSource={searchSource}
        intlPrefix={prefix}
        onSearch={handleSearch}
        onCreate={() => handleAction(OPERATE_TYPE.create, {})}
      />
    );
  };
  return {
    searchValue,
    searchChildren,
  };
};

export default useSearch;
