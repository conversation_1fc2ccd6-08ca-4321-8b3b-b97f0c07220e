import { LayoutTemplate } from '@/components';
import { DEFAULT_PAGINATION, OPERATE_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import commonServices from '@/services/common';
import { TablePaginationConfig } from 'antd';
import React, { ReactNode, Suspense, useEffect, useRef, useState } from 'react';
import ParamSettingDrawer from './components/ParamSettingDrawer';
import usePageFormAction from './hooks/usePageFormAction';
import useSearch from './hooks/useSearch';
import useTable from './hooks/useTable';
import { pageConfig } from './pageConfig';

const ProgramPage: React.FC = () => {
  const { prefix, cardTitle } = pageConfig;
  // 页面下方展示的组件类型
  const formRef = useRef<any>(null);

  const [drawerOpen, setDrawerOpen] = useState(false);
  const [drawerParams, setDrawerParams] = useState<{ input: any[]; output: any[] }>({ input: [], output: [] });
  const [listData, setListData] = useState<object[]>([]);
  const [detailType, setDetailType] = useState<string>(OPERATE_TYPE.list);
  const [loading, setLoading] = useState<boolean>(false);
  const [paginationConfig, setPaginationConfig] = useState<TablePaginationConfig | false>(false);

  // 副作用
  useEffect(() => {
    getTableData();
  }, []);

  // 逻辑处理
  const getTableData = async (searchValue = { keyValue: '', value: '' }, pagination = { ...DEFAULT_PAGINATION }) => {
    // setLoading(true);
    try {
      const param = {
        url: urlConstants.ADUIT_MANAGE.LIST,
        pagination,
        searchValue: { [searchValue.keyValue]: searchValue?.value },
      };
      const res = await commonServices.getTableListData(param);
      const { data, total } = res;
      Array.isArray(data) && setListData(data);
      total &&
        setPaginationConfig({
          showSizeChanger: true,
          showQuickJumper: true,
          total,
        });
    } catch (error) {
      setListData([]);
      setLoading(false);
    }
  };

  // 操作列事件
  const handleAction = (type: string, row: any) => {
    setDetailType(type);
    setDrawerParams(row?.params || { input: [], output: [] });
    setDrawerOpen(true);
  };

  // 返回事件
  const handleCardBack = (): void => {
    setDetailType(OPERATE_TYPE.list);
    setDrawerOpen(false);
  };

  // search组件hooks
  const { searchValue, searchChildren } = useSearch({
    getTableData,
    handleAction,
  });

  // table组件hooks
  const { tableChildren } = useTable({
    listData,
    loading,
    searchValue,
    paginationConfig,
    getTableData,
    handleAction,
  });

  // formAction组件hooks
  const { renderFormAction, handleDrawerSubmit } = usePageFormAction({
    formRef,
    detailType,
    getTableData,
    handleCardBack,
    handleAction,
  });
  // 详情组件
  const formChildren = (): ReactNode => {
    return <ParamSettingDrawer formRef={formRef} params={drawerParams} />;
  };

  return (
    <Suspense>
      <LayoutTemplate
        searchChildren={searchChildren()}
        tableChildren={tableChildren()}
        formChildren={formChildren()}
        drawerOpen={drawerOpen}
        type={detailType}
        intlPrefix={prefix}
        cardTitle={cardTitle}
        // cardExtra={renderFormAction()}
        handleClose={handleCardBack}
        handleSubmit={handleDrawerSubmit}
      />
    </Suspense>
  );
};
export default React.memo(ProgramPage);
