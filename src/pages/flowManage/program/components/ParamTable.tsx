import useIntlCustom from '@/hooks/useIntlCustom';
import { Button, Cascader, Form, Input, message, Popconfirm, Select, Space, Switch, Table } from 'antd';
import styles from './index.module.css';

const PARAM_TYPES = [
  { label: 'string', value: 'string' },
  { label: 'number', value: 'number' },
  { label: 'boolean', value: 'boolean' },
];

// 级联选择器的mock数据
const CASCADER_OPTIONS = [
  {
    value: 'dataLoadingProgram',
    label: '数据加载程序',
    children: [
      {
        value: 'repayment',
        label: '还款',
        children: [
          { value: 'serialNumber', label: '流水号' },
          { value: 'repaymentTime', label: '还款时间' },
          { value: 'repaymentAmount', label: '还款金额' },
          { value: 'cardNumber', label: '卡号' },
        ],
      },
    ],
  },
  {
    value: 'processingProgram',
    label: '处理程序',
    children: [
      {
        value: 'xxxProgram',
        label: 'XXX程序',
        children: [{ value: 'xxxxxProgram', label: 'XXXXX程序' }],
      },
    ],
  },
  {
    value: 'offlineDataTable',
    label: '离线数据表',
    children: [
      { value: 'table1', label: '数据表1' },
      { value: 'table2', label: '数据表2' },
    ],
  },
];

const defaultParam = { name: '', desc: '', type: 'string', value: '', isEdit: false };
type ParamItem = typeof defaultParam;

interface ParamTableProps {
  field: 'input' | 'output';
  data: ParamItem[];
  valueModes: boolean[];
  onDataChange: (data: ParamItem[]) => void;
  onValueModesChange: (modes: boolean[]) => void;
  onEditingRowChange: (row: { field: 'input' | 'output'; idx: number } | null) => void;
  editingRow: { field: 'input' | 'output'; idx: number } | null;
  inputRefs: React.MutableRefObject<{ input: (HTMLInputElement | null)[]; output: (HTMLInputElement | null)[] }>;
  form: any;
}

const ParamTable: React.FC<ParamTableProps> = ({
  field,
  data,
  valueModes,
  onDataChange,
  onValueModesChange,
  onEditingRowChange,
  editingRow,
  inputRefs,
  form,
}) => {
  const { translate } = useIntlCustom();
  const prefix = 'callParam';

  // 新增时进入编辑状态
  const handleAdd = () => {
    if (data.some((item) => item.isEdit || !item.name)) {
      message.warning(translate(prefix, 'pleaseFinishParamFirst'));
      return;
    }
    const newData = [...data, { ...defaultParam, isEdit: true }];
    onDataChange(newData);
    onValueModesChange([...valueModes, true]);
    onEditingRowChange({ field, idx: newData.length - 1 });

    setTimeout(() => {
      const refArr = inputRefs.current[field];
      if (refArr && refArr.length > 0) {
        const lastRef = refArr[refArr.length - 1];
        if (lastRef && lastRef.focus) lastRef.focus();
      }
    }, 100);
  };

  // 编辑按钮
  const handleEdit = (idx: number) => {
    if (editingRow) return;
    const newData = data.map((item, i) => ({ ...item, isEdit: i === idx }));
    onDataChange(newData);
    onEditingRowChange({ field, idx });

    setTimeout(() => {
      const refArr = inputRefs.current[field];
      if (refArr && refArr[idx] && typeof refArr[idx].focus === 'function') {
        refArr[idx].focus();
      }
    }, 100);
  };

  // 保存按钮
  const handleSaveRow = (idx: number) => {
    // 获取表单最新值
    const allValues = form.getFieldsValue();
    const list = allValues[field] || [];
    const newData = list.map((item, i) => ({ ...item, isEdit: false }));
    onDataChange(newData);
    onEditingRowChange(null);
  };

  // 取消按钮
  const handleCancelRow = (idx: number) => {
    const newData = [...data];
    if (!newData[idx].name && !newData[idx].desc && !newData[idx].value) {
      newData.splice(idx, 1);
      const newModes = valueModes.filter((_, i) => i !== idx);
      onValueModesChange(newModes);
    } else {
      newData[idx].isEdit = false;
    }
    onDataChange(newData);
    onEditingRowChange(null);
  };

  const handleRemove = (idx: number) => {
    const newData = data.filter((_, i) => i !== idx);
    const newModes = valueModes.filter((_, i) => i !== idx);
    onDataChange(newData);
    onValueModesChange(newModes);
  };

  // 切换value字段的输入模式
  const handleToggleValueInputMode = (idx: number) => {
    const newModes = valueModes.map((mode, i) => (i === idx ? !mode : mode));
    onValueModesChange(newModes);
  };

  inputRefs.current[field] = inputRefs.current[field] || [];
  inputRefs.current[field].length = data.length;
  // 动态生成columns
  const getColumns = () => {
    const baseColumns = [
      {
        title: translate(prefix, 'paramName'),
        dataIndex: 'name',
        render: (text, _, idx) => (
          <Form.Item name={[field, idx, 'name']} rules={[{ required: true }]} noStyle>
            <Input
              placeholder={translate(prefix, 'paramName')}
              ref={(el) => (inputRefs.current[field][idx] = el && el.input)}
              disabled={!data[idx].isEdit}
            />
          </Form.Item>
        ),
      },
      {
        title: translate(prefix, 'paramDesc'),
        dataIndex: 'desc',
        render: (text, _, idx) => (
          <Form.Item name={[field, idx, 'desc']} noStyle>
            <Input placeholder={translate(prefix, 'paramDesc')} disabled={!data[idx].isEdit} />
          </Form.Item>
        ),
      },
      {
        title: translate(prefix, 'type'),
        dataIndex: 'type',
        render: (text, _, idx) => (
          <Form.Item name={[field, idx, 'type']} noStyle>
            <Select options={PARAM_TYPES} className={styles.typeSelect} disabled={!data[idx].isEdit} />
          </Form.Item>
        ),
      },
    ];

    // 根据field类型决定是否添加value列
    if (field === 'input') {
      baseColumns.push({
        title: translate(prefix, 'value'),
        dataIndex: 'value',
        render: (text, _, idx) => {
          const isInputMode = valueModes[idx];
          return (
            <div className={styles.valueInputContainer}>
              <Form.Item name={[field, idx, 'value']} noStyle>
                {isInputMode ? (
                  <Input
                    placeholder={translate(prefix, 'value')}
                    disabled={!data[idx].isEdit}
                    className={styles.valueInput}
                  />
                ) : (
                  <Cascader
                    placeholder="请选择"
                    options={CASCADER_OPTIONS}
                    showSearch={{
                      filter: (inputValue, path) => {
                        return path.some(
                          (option) => (option.label as string).toLowerCase().indexOf(inputValue.toLowerCase()) > -1,
                        );
                      },
                    }}
                    disabled={!data[idx].isEdit}
                    className={styles.cascaderSelect}
                    changeOnSelect
                  />
                )}
              </Form.Item>
              {data[idx].isEdit && (
                <Switch
                  size="small"
                  checked={!isInputMode}
                  onChange={() => handleToggleValueInputMode(idx)}
                  checkedChildren="级联"
                  unCheckedChildren="输入"
                  className={styles.switchToggle}
                />
              )}
            </div>
          );
        },
      });
    }

    // 操作列
    baseColumns.push({
      title: translate(prefix, 'action'),
      dataIndex: 'action',
      render: (_, __, idx) => {
        if (data[idx].isEdit) {
          return (
            <Space>
              <a onClick={() => handleSaveRow(idx)}>{translate(prefix, 'save')}</a>
              <a onClick={() => handleCancelRow(idx)}>{translate(prefix, 'cancel')}</a>
            </Space>
          );
        }
        return (
          <Space>
            <a onClick={() => handleEdit(idx)}>{translate(prefix, 'edit')}</a>
            <Popconfirm title={translate(prefix, 'deleteConfirm')} onConfirm={() => handleRemove(idx)}>
              <a>{translate(prefix, 'delete')}</a>
            </Popconfirm>
          </Space>
        );
      },
    });

    return baseColumns;
  };

  return (
    <Table
      dataSource={data}
      rowKey={(_, idx) => String(idx)}
      pagination={false}
      size="small"
      columns={getColumns()}
      // 后续只能查看，不能新增
      footer={() => (
        <Button type="dashed" block onClick={handleAdd}>
          {translate(prefix, 'add')}
        </Button>
      )}
    />
  );
};

export default ParamTable;
