import { FormHearder } from '@/components';
import useIntlCustom from '@/hooks/useIntlCustom';
import { Col, Form, Input, Row, Select } from 'antd';
import { useEffect, useRef, useState } from 'react';
import styles from './index.module.css';
import ParamTable from './ParamTable';

const defaultParam = { name: '', desc: '', type: 'string', value: '', isEdit: false };
type ParamItem = typeof defaultParam;

interface ParamSettingDrawerProps {
  formRef: any;
  params?: { input: ParamItem[]; output: ParamItem[] };
}

const ParamSettingDrawer = ({ formRef, params = { input: [], output: [] } }: ParamSettingDrawerProps) => {
  const [form] = Form.useForm();
  const { translate } = useIntlCustom();
  const prefix = 'callParam';

  // 用于聚焦新行输入框
  const inputRefs = useRef<{ input: (HTMLInputElement | null)[]; output: (HTMLInputElement | null)[] }>({
    input: [],
    output: [],
  });

  // 本地 state 管理 input/output 数据
  const [inputData, setInputData] = useState<ParamItem[]>([]);
  const [outputData, setOutputData] = useState<ParamItem[]>([]);

  // 当前正在编辑的行索引
  const [editingRow, setEditingRow] = useState<{ field: 'input' | 'output'; idx: number } | null>(null);

  // value字段的输入模式状态
  const [valueInputModes, setValueInputModes] = useState<{ input: boolean[]; output: boolean[] }>({
    input: [],
    output: [],
  });

  // 初始化和 params 变化时同步 state 和 form
  useEffect(() => {
    const safeInput = Array.isArray(params.input) ? params.input.filter((i) => i && typeof i === 'object') : [];
    const safeOutput = Array.isArray(params.output) ? params.output.filter((i) => i && typeof i === 'object') : [];

    setInputData(safeInput.map((item) => ({ ...(item as ParamItem), isEdit: false })));
    setOutputData(safeOutput.map((item) => ({ ...(item as ParamItem), isEdit: false })));

    form.setFieldsValue({ input: safeInput, output: safeOutput });
    inputRefs.current = { input: [], output: [] };
    setEditingRow(null);

    // 初始化value输入模式状态
    setValueInputModes({
      input: new Array(safeInput.length).fill(true),
      output: new Array(safeOutput.length).fill(true),
    });
  }, [params, form]);

  // 保证 form 数据和 state 同步
  useEffect(() => {
    form.setFieldsValue({ input: inputData });
  }, [inputData, form]);

  useEffect(() => {
    form.setFieldsValue({ output: outputData });
  }, [outputData, form]);

  // 处理数据变化的回调函数
  const handleInputDataChange = (data: ParamItem[]) => {
    setInputData(data);
  };

  const handleOutputDataChange = (data: ParamItem[]) => {
    setOutputData(data);
  };

  const handleInputValueModesChange = (modes: boolean[]) => {
    setValueInputModes((prev) => ({ ...prev, input: modes }));
  };

  const handleOutputValueModesChange = (modes: boolean[]) => {
    setValueInputModes((prev) => ({ ...prev, output: modes }));
  };

  return (
    <div>
      <FormHearder title={translate(prefix, 'baseInfo')} />
      <Form
        form={form}
        ref={formRef}
        layout="horizontal"
        initialValues={params}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
      >
        <Row gutter={24} className={styles.formRow}>
          <Col span={12}>
            <Form.Item
              label={translate(prefix, 'programCode')}
              name="programCode"
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
            >
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={translate(prefix, 'programName')}
              name="programName"
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
            >
              <Input disabled />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24} className={styles.formRow}>
          <Col span={12}>
            <Form.Item
              label={translate(prefix, 'programType')}
              name="programType"
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
            >
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={translate(prefix, 'maxInstanceNum')}
              name="maxInstanceNum"
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
            >
              <Input disabled />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24} className={styles.formRowWithMargin}>
          <Col span={12}>
            <Form.Item
              label={translate(prefix, 'programFuncDesc')}
              name="programFuncDesc"
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
            >
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={translate(prefix, 'status')}
              name="status"
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
            >
              <Select
                disabled
                options={[
                  { label: translate(prefix, 'enabled'), value: 'enabled' },
                  { label: translate(prefix, 'disabled'), value: 'disabled' },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>
        <FormHearder title={'参数设置'} />
        <div className={styles.paramSection}>
          <div className={styles.paramSectionTitle}>{translate(prefix, 'input')}</div>
          <ParamTable
            field="input"
            data={inputData}
            valueModes={valueInputModes.input}
            onDataChange={handleInputDataChange}
            onValueModesChange={handleInputValueModesChange}
            onEditingRowChange={setEditingRow}
            editingRow={editingRow}
            inputRefs={inputRefs}
            form={form}
          />
        </div>
        <div>
          <div className={styles.paramSectionTitleLast}>{translate(prefix, 'output')}</div>
          <ParamTable
            field="output"
            data={outputData}
            valueModes={valueInputModes.output}
            onDataChange={handleOutputDataChange}
            onValueModesChange={handleOutputValueModesChange}
            onEditingRowChange={setEditingRow}
            editingRow={editingRow}
            inputRefs={inputRefs}
            form={form}
          />
        </div>
      </Form>
    </div>
  );
};

export default ParamSettingDrawer;
