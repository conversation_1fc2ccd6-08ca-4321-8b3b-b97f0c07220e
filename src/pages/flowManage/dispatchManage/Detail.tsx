// src/components/WeightStrategyForm.tsx
import { Button, Card, Collapse, Divider, Form, Input, InputNumber, Select, Space, Table, message } from 'antd';
import { useState } from 'react';

const FACTOR_TYPES = [
  { value: 'level', label: '催收员级别' },
  { value: 'performance', label: '催收员业绩系数' },
  { value: 'experience', label: '催收员经验' },
  { value: 'education', label: '催收员教育背景' },
  { value: 'training', label: '催收员培训经历' },
];

const FACTOR_DETAIL_COLUMNS = {
  level: [
    {
      title: '级别名称',
      dataIndex: 'level',
      render: (text, record, idx, onChange) => (
        <Input value={text} onChange={(e) => onChange(idx, 'level', e.target.value)} />
      ),
    },
    {
      title: '权重系数',
      dataIndex: 'weight',
      render: (text, record, idx, onChange) => (
        <InputNumber min={0} value={text} onChange={(val) => onChange(idx, 'weight', val)} />
      ),
    },
  ],
  performance: [
    {
      title: '业绩区间',
      dataIndex: 'range',
      render: (text, record, idx, onChange) => (
        <Space>
          <InputNumber min={0} value={record.min} onChange={(val) => onChange(idx, 'min', val)} placeholder="最小值" />
          <span> - </span>
          <InputNumber min={0} value={record.max} onChange={(val) => onChange(idx, 'max', val)} placeholder="最大值" />
        </Space>
      ),
    },
    {
      title: '权重系数',
      dataIndex: 'weight',
      render: (text, record, idx, onChange) => (
        <InputNumber min={0} value={text} onChange={(val) => onChange(idx, 'weight', val)} />
      ),
    },
  ],
};

export default function WeightStrategyForm({ onBack, initialValues }) {
  const [form] = Form.useForm();
  const [factors, setFactors] = useState([
    {
      type: 'level',
      label: '催收员级别',
      weight: 0.5,
      details: [{ level: '', weight: 1 }],
    },
    {
      type: 'performance',
      label: '催收员业绩系数',
      weight: 0.5,
      details: [{ min: 0, max: 100, weight: 1 }],
    },
  ]);

  // 新增权重因子
  const addFactor = () => {
    setFactors([
      ...factors,
      {
        type: 'level',
        label: '催收员级别',
        weight: 0.1,
        details: [{ level: '', weight: 1 }],
      },
    ]);
  };

  // 删除权重因子
  const removeFactor = (idx) => setFactors(factors.filter((_, i) => i !== idx));

  // 修改因子类型
  const changeFactorType = (idx, type) => {
    const label = FACTOR_TYPES.find((t) => t.value === type)?.label || '';
    const details = type === 'level' ? [{ level: '', weight: 1 }] : [{ min: 0, max: 100, weight: 1 }];
    const newFactors = [...factors];
    newFactors[idx] = { ...newFactors[idx], type, label, details };
    setFactors(newFactors);
  };

  // 修改加权系数
  const changeFactorWeight = (idx, weight) => {
    const newFactors = [...factors];
    newFactors[idx].weight = weight;
    setFactors(newFactors);
  };

  // 明细表格增删改
  const addDetail = (idx) => {
    const newFactors = [...factors];
    if (newFactors[idx].type === 'level') {
      newFactors[idx].details.push({ level: '', weight: 1 });
    } else {
      newFactors[idx].details.push({ min: 0, max: 100, weight: 1 });
    }
    setFactors(newFactors);
  };
  const removeDetail = (factorIdx, detailIdx) => {
    const newFactors = [...factors];
    newFactors[factorIdx].details.splice(detailIdx, 1);
    setFactors(newFactors);
  };
  const changeDetail = (factorIdx, detailIdx, key, value) => {
    const newFactors = [...factors];
    newFactors[factorIdx].details[detailIdx][key] = value;
    setFactors(newFactors);
  };

  // 提交
  const handleFinish = (values) => {
    // TODO: 校验加权系数之和为1
    const totalWeight = factors.reduce((sum, f) => sum + Number(f.weight), 0);
    if (Math.abs(totalWeight - 1) > 0.01) {
      message.error('所有权重因子的加权系数之和必须为1');
      return;
    }
    // TODO: 提交API
    message.success('保存成功（示例）');
    onBack();
  };

  return (
    <Card title="新增/编辑权重策略" extra={<Button onClick={onBack}>返回</Button>}>
      <Form form={form} layout="vertical" onFinish={handleFinish} initialValues={initialValues}>
        <Form.Item name="id" label="策略权重ID" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item name="name" label="策略权重名称" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item name="desc" label="策略权重说明">
          <Input.TextArea />
        </Form.Item>
        <Divider />
        <div style={{ marginBottom: 16 }}>
          <Button type="dashed" onClick={addFactor}>
            新增权重因子
          </Button>
        </div>
        <Collapse accordion>
          {factors.map((factor, idx) => (
            <Collapse.Panel
              key={idx}
              header={
                <Space>
                  <Select
                    value={factor.type}
                    style={{ width: 150 }}
                    onChange={(val) => changeFactorType(idx, val)}
                    options={FACTOR_TYPES}
                  />
                  <span>加权系数</span>
                  <InputNumber
                    min={0}
                    max={1}
                    step={0.01}
                    value={factor.weight}
                    onChange={(val) => changeFactorWeight(idx, val)}
                  />
                  <Button danger onClick={() => removeFactor(idx)} disabled={factors.length === 1}>
                    删除因子
                  </Button>
                </Space>
              }
            >
              <Table
                dataSource={factor.details}
                rowKey={(_, i) => i}
                pagination={false}
                columns={[
                  ...FACTOR_DETAIL_COLUMNS[factor.type].map((col) => ({
                    ...col,
                    render: (text, record, detailIdx) =>
                      col.render(text, record, detailIdx, (i, key, val) => changeDetail(idx, i, key, val)),
                  })),
                  {
                    title: '操作',
                    render: (_, __, detailIdx) => (
                      <Button
                        danger
                        onClick={() => removeDetail(idx, detailIdx)}
                        disabled={factor.details.length === 1}
                      >
                        删除
                      </Button>
                    ),
                  },
                ]}
                footer={() => (
                  <Button type="dashed" onClick={() => addDetail(idx)} style={{ width: '100%' }}>
                    新增{factor.label}明细
                  </Button>
                )}
              />
            </Collapse.Panel>
          ))}
        </Collapse>
        <Divider />
        <Form.Item>
          <Button type="primary" htmlType="submit">
            保存
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
}
