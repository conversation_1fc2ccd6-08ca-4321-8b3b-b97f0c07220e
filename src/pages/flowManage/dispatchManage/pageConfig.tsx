import { COMPONENT_TYPE, I18N_COMON_PAGENAME, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.CALL_PARAM,
  cardTitle: 'dispatchManage',
  // 页面接口请求
  urlObj: {
    list: urlConstants.NODE_MANAGE.LIST,
    create: urlConstants.CUTTOMER_MANAGE.CREATE,
    edit: urlConstants.CUTTOMER_MANAGE.EDIT,
    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'flowchartId',
      label: 'flowchartId',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'flowchartName',
      label: 'flowchartName',
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'flowchartId',
      dataIndex: 'flowchartId',
      key: 'flowchartId',
      width: 120,
    },
    {
      title: 'flowchartName',
      dataIndex: 'flowchartName',
      key: 'flowchartName',
      width: 120,
    },
    {
      width: 150,
      title: 'description',
      dataIndex: 'description',
      key: 'description',
      valueType: RENDER_TYPE.Ellipsis,
      align: 'left',
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      width: 160,
      valueType: RENDER_TYPE.DateTime,
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      width: 120,
    },
  ],
};
