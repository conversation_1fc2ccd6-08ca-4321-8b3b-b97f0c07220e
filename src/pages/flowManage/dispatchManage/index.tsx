// 催收管理
import { OPERATE_TYPE } from '@/constants/publicConstant';
import { useLocation } from 'ice';
import React, { Suspense, useState } from 'react';
import { pageConfig } from './pageConfig';
const PageTemplate = React.lazy(() => import('@/components/templates/PageTemplate'));
const Detail = React.lazy(() => import('./Detail'));

const DispatchManagePage: React.FC = () => {
  const location = useLocation();
  const { urlObj, prefix, searchSource, columns, cardTitle } = pageConfig;
  const resetValue = { custName: '', custPerson: '' };
  const [detailData, setDetailData] = useState<any>({});
  const [type, setType] = useState<string>(OPERATE_TYPE.list);

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 表格配置
  const tableConfig = {
    columns,
    rowKey: 'paramIndex',
    optionList: [{ type: OPERATE_TYPE.detail }, { type: OPERATE_TYPE.flowNode }],
    showPagination: true,
  };

  // 表单配置
  const formConfig = {
    intlPrefix: prefix,
    customChildren: {
      [OPERATE_TYPE.detail]: <Detail type={type} data={detailData} />,
      [OPERATE_TYPE.flowNode]: <Detail type={type} data={detailData} />,
    },
  };

  // 列表按钮操作
  const handleAction = (editType: string, row: any) => {
    setDetailData({ ...row });

    setType(editType);
    // if (editType === OPERATE_TYPE.edit) {
    //   //   window.open(location.pathname)const param = "exampleParameter";
    //   const param = 'exampleParameter';
    //   // 打开新页面，并将参数附加到 URL 中
    //   // window.open(`newpage.html?param=${encodeURIComponent(param)}`, '_blank');
    //   window.open(`#/form/board?preview=1`);
    // }
  };

  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        onAction={handleAction}
      />
    </Suspense>
  );
};
export default React.memo(DispatchManagePage);
