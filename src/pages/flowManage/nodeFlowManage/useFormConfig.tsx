import { COMPONENT_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import DICT_CONSTANTS from '@/constants/dictConstants';
import { IFormConfig } from '@/types/IForm';

const useFormConfig = (type: string) => {
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'nodeFlowId',
          label: 'nodeFlowId',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'nodeFlowName',
          label: 'nodeFlowName',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'nodeFlowCode',
          label: 'nodeFlowCode',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'nodeFlowUp',
          label: 'nodeFlowUp',
          rules: [{ required: true }],
        },
        // {
        //   type: COMPONENT_TYPE.SELECT,
        //   name: 'status',
        //   label: 'status',
        //   data: DICT_CONSTANTS.PARAM_STATUS,
        //   rules: [{ required: true }],
        //   disabled: type !== OPERATE_TYPE.create,
        // },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'nodeFlowDesc',
          label: 'nodeFlowDesc',
          rules: [{ required: true }],
        },
      ],
    },
  ];

  return {
    infoFormConfig,
  };
};

export default useFormConfig;
