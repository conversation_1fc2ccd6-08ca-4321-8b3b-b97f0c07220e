import { COMPONENT_TYPE, I18N_COMON_PAGENAME, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.CALL_PARAM,
  cardTitle: 'nodeFlowManage',
  // 页面接口请求
  urlObj: {
    list: urlConstants.NODE_FOW_MANAGE.LIST,
    create: urlConstants.NODE_FOW_MANAGE.CREATE,
    edit: urlConstants.NODE_FOW_MANAGE.EDIT,
    delete: urlConstants.NODE_FOW_MANAGE.EDIT,

    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'nodeFlowId',
      label: 'nodeFlowId',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'nodeFlowName',
      label: 'nodeFlowName',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'nodeFlowDesc',
      label: 'nodeFlowDesc',
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'nodeFlowId',
      dataIndex: 'nodeFlowId',
      key: 'nodeFlowId',
      width: 120,
    },
    {
      title: 'nodeFlowName',
      dataIndex: 'nodeFlowName',
      key: 'nodeFlowName',
      width: 120,
    },
    {
      width: 120,
      title: 'nodeFlowCode',
      dataIndex: 'nodeFlowCode',
      key: 'nodeFlowCode',
    },
    {
      width: 120,
      title: 'nodeFlowUp',
      dataIndex: 'nodeFlowUp',
      key: 'nodeFlowUp',
    },
    {
      width: 220,
      title: 'nodeFlowDesc',
      dataIndex: 'nodeFlowDesc',
      key: 'nodeFlowDesc',
      render: (text) => <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>,
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      width: 160,
      valueType: RENDER_TYPE.DateTime,
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      width: 120,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};
