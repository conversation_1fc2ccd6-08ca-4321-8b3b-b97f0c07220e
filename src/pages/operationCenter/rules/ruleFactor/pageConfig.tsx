import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, OPERATE_TYPE, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';

// 处理规则类型数据
const formatRuleTypeCa = (caseValue: any) => {
  let arr: Array<string> = [];
  caseValue.map((item) => {
    if (item.length === 1) {
      DICT_CONSTANTS.RULE_TYPE.find((ite) => ite.key === item[0])?.children?.map((it) => arr.push(it.key));
    } else {
      arr.push(item[1]);
    }
  });
  return arr.join(',');
};

// 接口请求
const getRequestData = (postData: any, type: string) => {
  let data = {};
  switch (type) {
    case OPERATE_TYPE.create:
    case OPERATE_TYPE.edit:
      data = {
        ...postData.formData,
        // ruleType: formatRuleTypeCa(postData.formData.ruleType),
      };
      break;
    default:
      data = { ...postData };
  }

  return { ...data };
};

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.RULES,
  cardTitle: 'ruleFacField',
  urlObj: {
    list: urlConstants.RULE_FACTOR.LIST,
    create: urlConstants.RULE_FACTOR.CREATE,
    edit: urlConstants.RULE_FACTOR.EDIT,
    getRequestData,
  },
  // 查询条件
  searchSource: [
    { value: 'name', label: 'name', type: COMPONENT_TYPE.INPUT },
    { value: 'key', label: 'key', type: COMPONENT_TYPE.INPUT },
    {
      value: 'ruleType',
      label: 'ruleType',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.RULE_TYPE,
    },
  ],
  // 列表展示字段
  columns: [
    {
      key: 'name',
      dataIndex: 'name',
      title: 'name',
      align: 'left',
    },
    { key: 'key', dataIndex: 'key', title: 'key', width: 200 },
    // {
    //   key: 'ruleType',
    //   dataIndex: 'ruleType',
    //   title: 'ruleType',
    //   data: DICT_CONSTANTS.RULE_TYPE,
    //   valueType: RENDER_TYPE.MockDictionary,
    //   dictType: 'RULE_TYPE',
    //   showKey: false,
    //   width: 200,
    //   align: 'center',
    // },
    {
      key: 'updateTime',
      dataIndex: 'updateTime',
      title: 'updateTime',
      width: 300,
      align: 'center',
      prefix: 'common',
      valueType: RENDER_TYPE.DateTime,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};
