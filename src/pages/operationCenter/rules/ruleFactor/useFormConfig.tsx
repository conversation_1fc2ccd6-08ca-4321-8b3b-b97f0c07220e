import { useState } from 'react';
import { COMPONENT_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import PATTERN_RULES from '@/constants/regularConstant';
import { IFormConfig } from '@/types/IForm';
import DICT_CONSTANTS from '@/constants/dictConstants';

const useFormConfig = () => {
  const [editType, setEditType] = useState<string>(OPERATE_TYPE.list);
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'ruleFacField',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'name',
          label: 'name',
          maxLength: 100,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'key',
          label: 'key',
          rules: [{ required: true }, { ...PATTERN_RULES.EN_NUM }],
          maxLength: 50,
        },
        {
          // type: COMPONENT_TYPE.CASCADER,
          type: COMPONENT_TYPE.SELECT,
          name: 'ruleType',
          label: 'ruleType',
          data: DICT_CONSTANTS.RULE_TYPE,
          rules: [{ required: true }],
          disabled: editType === OPERATE_TYPE.edit,
          showKey: false,
          // mode: 'multiple',
        },
        // {
        //   type: COMPONENT_TYPE.SELECT,
        //   name: 'ruleExecuteType',
        //   label: 'ruleExecuteType',
        //   data: DICT_CONSTANTS.RULE_OPTION_TYPE,
        //   showKey: false,
        //   mode: 'multiple',
        //   rules: [{ required: true }],
        // },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'valueType',
          label: 'valueType',
          rules: [{ required: true }],
          showKey: false,
          // formlayoutType: 'one',
          data: DICT_CONSTANTS.RULE_FACTORS_TYPE,
        },
        {
          type: COMPONENT_TYPE.EXTARA_BTN_INPUT,
          name: 'dictName',
          label: 'dictName',
          rules: [{ required: true }],
          enterButton: 'dictInfo',
          disabled: true,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'dictName1',
          label: 'dictName1',
          // rules: [{ required: true }],
        },
        // {
        //   type: COMPONENT_TYPE.INTERVAL_INPUT,
        //   min: 1,
        //   max: 16,
        //   name: 'dictName2',
        //   label: 'dictName2',
        //   minName: 'minLength',
        //   minLabel: 'minLength',
        //   maxName: 'maxLength',
        //   maxLabel: 'maxLength',
        //   rules: [{ required: true }],
        //   placeholder: ['最小长度', '最大长度'],
        // },
        {
          type: COMPONENT_TYPE.INPUT_NUMBER,
          min: 1,
          // max: 16,
          name: 'minLength',
          label: 'minLength',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT_NUMBER,
          min: 1,
          // max: 16,
          name: 'maxLength',
          label: 'maxLength',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT_NUMBER,
          min: 1,
          max: 16,
          name: 'digits',
          label: 'digits',
          rules: [{ required: true }],
        },
        // {
        //   type: COMPONENT_TYPE.SELECT,
        //   name: 'dictName5',
        //   label: 'dictName5',
        //   rules: [{ required: true }],
        // },
        // {
        //   type: COMPONENT_TYPE.SELECT,
        //   name: 'dictName6',
        //   label: 'dictName6',
        //   rules: [{ required: true }],
        // },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'dictName7',
          label: 'dictName7',
          rules: [{ required: true }],
        },
        // {
        //   type: COMPONENT_TYPE.TEXTAREA,
        //   name: 'remark',
        //   label: 'remark',
        //   rules: [{ required: true }],
        //   maxLength: 200,
        // },
      ],
    },
  ];

  return {
    setEditType,
    infoFormConfig,
  };
};

export default useFormConfig;
