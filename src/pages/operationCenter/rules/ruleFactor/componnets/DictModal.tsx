import { PageTemplate } from '@/components';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import { Modal } from 'antd';
import { FC, useState } from 'react';
import { FormattedMessage } from 'react-intl';

interface IDictModalProps {
  dictOpen?: boolean;
  onCancel?: () => void;
  onSubmit?: (data) => void;
}
// 国际化key
const prefix = I18N_COMON_PAGENAME.CALL_PARAM;
const pageId = 'auth_resp_code_param';
const urlObj = {
  list: 'limit.getLimitUnitList',
  create: 'limit.getLimitUnitList',
  delete: 'limit.getLimitUnitList',
  detail: '',
  edit: 'limit.getLimitUnitList',
};

const DictModal: FC<IDictModalProps> = ({ dictOpen, onCancel, onSubmit }) => {
  const [loading, setLoading] = useState(false);
  const resetValue = { rejRsnCode: '', sysInnerRespCode: '', paramSts: null };
  const { translate } = useIntlCustom();
  const [actionType, setActionType] = useState<string>('');
  const [detailData, setDetailData] = useState<any>({});
  const searchSource = [
    {
      value: 'rejRsnCode',
      label: 'rejRsnCode',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'sysInnerRespCode',
      label: 'sysInnerRespCode',
      type: COMPONENT_TYPE.INPUT,
    },
  ];

  // 列表
  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: 'crcdOrgNo',
      dataIndex: 'crcdOrgNo',
      key: 'crcdOrgNo',
      width: 160,
    },
    {
      title: 'rejRsnCode',
      dataIndex: 'rejRsnCode',
      key: 'rejRsnCode',
      width: 160,
    },
    {
      title: 'rejRsnCodePri',
      dataIndex: 'rejRsnCodePri',
      key: 'rejRsnCodePri',
    },
  ];

  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
    defaultParam: { pageId },
  };

  const tableConfig = {
    columns,
    rowKey: 'id',
    showPagination: true,
    optionList: [],
    props: {
      scroll: 500,
      rowSelection: {
        type: 'radio',
        onChange: (selectedRowKeys: string, selectedRows: any) => {
          // console.log('33333--selectedRows--table', selectedRowKeys, selectedRows[0]);
        },
      },
    },
  };

  const formConfig = {
    config: [],
    data: {},
    intlPrefix: prefix,
    onChange: () => {},
  };

  const handleOk = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 3000);
    onSubmit?.('data');
  };

  return (
    <Modal
      width="70%"
      style={{ height: 300 }}
      maskClosable={false}
      open={dictOpen}
      title={<FormattedMessage id="rules.dictInfo" />}
      // title={translate('rules', 'dictInfo')}
      onOk={handleOk}
      onCancel={() => onCancel?.()}
      okText={translate('common', 'confirm')}
      cancelText={translate('common', 'cancel')}
      confirmLoading={loading}
    >
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        // cardTitle="ruleFacField"
        intlPrefix={prefix}
      />
    </Modal>
  );
};

export default DictModal;
