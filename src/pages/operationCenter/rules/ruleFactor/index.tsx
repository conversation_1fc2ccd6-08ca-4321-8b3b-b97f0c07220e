// 业务參數/規則/規則因數管理
import React, { FC, useState, useMemo } from 'react';
import { PageTemplate } from '@/components';
import { updateDisabledProperty } from '@/utils/comUtil';
import { pageConfig } from './pageConfig';
import { convertToArray } from './utill';
import useFormConfig from './useFormConfig';
import { OPERATE_TYPE } from '@/constants/publicConstant';

const DictModalCom = React.lazy(() => import('./componnets/DictModal'));

const RuleFactor: FC = () => {
  const resetValue = { name: '', key: '', ruleType: null };
  const { urlObj, prefix, searchSource, columns, cardTitle } = pageConfig;
  // hook变量
  const { setEditType, infoFormConfig } = useFormConfig();
  const [detailData, setDetailData] = useState<any>({});
  const [formConfig, setFormConfig] = useState<any>({
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
    onChange: (data, allValues) => handleOnChange(data, allValues),
  });
  const [dictOpen, setDictOpen] = useState<boolean>(false);

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 表格配置
  const tableConfig = {
    columns,
    rowKey: 'key',
    showPagination: true,
    optionList: [OPERATE_TYPE.detail, OPERATE_TYPE.edit],
  };

  // 处理表单联动逻辑
  const handleRuleSelected = (selectValue: any, data?: any) => {
    const arr: any = infoFormConfig[1].data;
    const ruleTypeObj = {
      DIC: ['dictName2', 'minLength', 'maxLength', 'digits', 'dictName5', 'dictName6', 'dictName7'], // 字典型
      INT_X: ['dictName', 'dictName1', 'digits', 'dictName5', 'dictName6', 'dictName7'], // 整数型
      DOUBLE_X: ['dictName', 'dictName1', 'dictName5', 'dictName6', 'dictName7'], // 浮点型
      AMOUNT: ['dictName', 'dictName1', 'dictName5', 'dictName6', 'dictName7'], // 金额型
      CHARACTER_X: ['dictName', 'dictName1', 'digits', 'dictName5', 'dictName6', 'dictName7'], // 字符型
      PARAMETER: ['dictName', 'dictName1', 'dictName2', 'minLength', 'maxLength', 'digits'], // 参数型
      LONG_X: [
        'dictName',
        'dictName1',
        'dictName2',
        'minLength',
        'maxLength',
        'digits',
        'dictName5',
        'dictName6',
        'dictName7',
      ], // 布尔型，数组型，长整型
      BOOLEAN_X: [
        'dictName',
        'dictName1',
        'dictName2',
        'minLength',
        'maxLength',
        'digits',
        'dictName5',
        'dictName6',
        'dictName7',
      ], // 布尔型，数组型，长整型
      ARRAY: [
        'dictName',
        'dictName1',
        'dictName2',
        'minLength',
        'maxLength',
        'digits',
        'dictName5',
        'dictName6',
        'dictName7',
      ], // 布尔型，数组型，长整型
    };
    for (let key in ruleTypeObj) {
      switch (selectValue) {
        case key:
          const newArr = arr?.filter((item) => !ruleTypeObj[key].includes(item.name)) || [];
          setFormConfig({
            ...formConfig,
            data,
            config: [infoFormConfig[0], { ...infoFormConfig[1], data: newArr }],
          });
          break;
        default:
          break;
      }
    }
  };

  // 监听表单值的变化
  const handleOnChange = (data: object, allValues: object): void => {
    let valueObj = { key: '', value: '' };
    for (let key in data) {
      valueObj.key = key;
      valueObj.value = data[key];
    }
    switch (valueObj.key) {
      case 'dictName':
        setDictOpen(true);
        break;
      case 'valueType':
        handleRuleSelected(valueObj.value);
        break;
      default:
        break;
    }
  };

  // 字段弹窗提交事件
  const handleSubmit = (data) => {
    setDictOpen(false);
  };

  // 事件处理
  const handleAction = (type: string, data: any) => {
    setDetailData(data);
    const formData = {
      ...data,
      // ruleType: convertToArray(data?.ruleType),
    };
    switch (type) {
      case 'detail':
      case 'edit':
        if (data.valueType) {
          handleRuleSelected(data.valueType, formData);
        }
        updateDisabledProperty(infoFormConfig, 'name,key', true);
        break;
      case 'create':
        updateDisabledProperty(infoFormConfig, 'name,key', false);
        handleRuleSelected('ARRAY');
        break;
    }
  };

  // 字典弹窗组件
  const dictModalNode = useMemo(() => {
    return <DictModalCom dictOpen={dictOpen} onCancel={() => setDictOpen(false)} onSubmit={handleSubmit} />;
  }, [dictOpen]);

  return (
    <>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        onAction={handleAction}
      />
      {dictModalNode}
    </>
  );
};

export default React.memo(RuleFactor);
