// formAction组件hooks
import { FormAction } from '@/components';
import { OPERATE_TYPE } from '@/constants/publicConstant';

const usePageFormAction = ({
  detailRef,
  extra,
  detailType,
  formActionPermissionObj,
  getTableData,
  handleCardBack,
  handleAction,
}) => {
  // 表单提交事件
  const handleDrawerSubmit = async () => {
    if (detailRef.current) {
      const res = await detailRef.current.onSubmit();
      if (res) {
        handleCardBack();
        getTableData();
      }
    }
  };

  // 渲染formAction组件
  const renderFormAction = () => {
    return (
      <FormAction
        showCreate={detailType === OPERATE_TYPE.list}
        showSubmit={false}
        extra={extra()}
        permissionObj={formActionPermissionObj}
        onCreate={() => handleAction(OPERATE_TYPE.create, {})}
      />
    );
  };

  return { renderFormAction, handleDrawerSubmit };
};

export default usePageFormAction;
