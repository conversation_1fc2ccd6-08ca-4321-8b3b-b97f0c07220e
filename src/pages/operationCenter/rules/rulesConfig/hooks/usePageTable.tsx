// table组件hooks
import { CommonTable } from '@/components';
import { I18N_COMON_PAGENAME, RENDER_TYPE } from '@/constants/publicConstant';
import { IColumns } from '@/types/ICommon';
import { prefix, tableProps } from '../pageConfig';

const usePageTable = ({ listData, loading, paginationConfig, optionList, getTableData, handleAction }) => {
  // 表格列表字段
  const columns: IColumns[] = [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 80,
    },
    { title: 'ruleId', dataIndex: 'ruleId', key: 'ruleId', width: 150 },
    {
      title: 'ruleName',
      dataIndex: 'ruleName',
      key: 'ruleName',
      width: 150,
      valueType: RENDER_TYPE.Ellipsis,
      align: 'left',
    },
    {
      title: 'paramSts',
      dataIndex: 'paramSts',
      key: 'paramSts',
      width: 120,
      prefix: I18N_COMON_PAGENAME.COMMON,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      title: 'updateTime',
      dataIndex: 'updateTime',
      key: 'updateTime',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 150,
      valueType: RENDER_TYPE.DateTime,
    },
  ];
  // 改变分页事件
  const handleTableChange = (pagination) => {
    const { current: currentPage, pageSize } = pagination;
    getTableData({ pagination: { currentPage, pageSize } });
  };

  // 渲染table组件
  const tableChildren = () => {
    return (
      <CommonTable
        rowKey="paramIndex"
        dataSource={listData}
        columns={columns}
        loading={loading}
        optionList={optionList}
        paginationConfig={paginationConfig}
        intlPrefix={prefix}
        props={tableProps}
        onAction={handleAction}
        onChange={handleTableChange}
      />
    );
  };

  return { tableChildren };
};

export default usePageTable;
