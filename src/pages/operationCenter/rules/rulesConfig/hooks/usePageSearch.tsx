// search组件hooks
import { Search } from '@/components';
import { COMPONENT_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import { useState } from 'react';
import { prefix, resetValue } from '../pageConfig';

const usePageSearch = ({ getTableData, handleAction }) => {
  // hooks变量
  const [searchParam, setSearchParam] = useState<any>({ ...resetValue });
  // 查询条件字段
  const searchSource = [
    {
      value: 'ruleName',
      label: 'ruleName',
      type: COMPONENT_TYPE.INPUT,
      showCount: true,
      maxLength: 25,
    },
    {
      value: 'ruleId',
      label: 'ruleId',
      type: COMPONENT_TYPE.INPUT,
      showCount: true,
      maxLength: 20,
    },
  ];

  // 查询事件
  const handleSearch = (searchValue: object): void => {
    setSearchParam(searchValue);
    getTableData({ searchValue });
  };

  // 渲染search组件
  const searchChildren = () => {
    return (
      <Search
        searchValue={searchParam}
        resetValue={resetValue}
        searchSource={searchSource}
        intlPrefix={prefix}
        onSearch={handleSearch}
        onCreate={() => handleAction(OPERATE_TYPE.create, {})}
      />
    );
  };

  return {
    searchParam,
    searchChildren,
  };
};

export default usePageSearch;
