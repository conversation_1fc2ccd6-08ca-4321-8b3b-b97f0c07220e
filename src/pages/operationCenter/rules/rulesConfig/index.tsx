// 规则引擎-规则管理
import { GradientButton, LayoutTemplate } from '@/components';
import CommonModal from '@/components/CommonModal';
import { DEFAULT_PAGINATION, OPERATE_TYPE } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import { IDetailData } from '@/materials/rules/types/IRules';
import { setRulesDetailData } from '@/materials/rules/util/rulesUtil';
import common from '@/services/common';
import { Space, type TablePaginationConfig } from 'antd';
import { FC, ReactNode, lazy, useEffect, useRef, useState } from 'react';
import AiContent from './components/AiContent';
import { IRulesPageProps } from './components/IRules';
import usePageFormAction from './hooks/usePageFormAction';
import usePageSearch from './hooks/usePageSearch';
import usePageTable from './hooks/usePageTable';
import { cardTitle, defOptionList, formActionPermissionObj, prefix, urlObj } from './pageConfig';

const Detail = lazy(() => import('@/materials/rules'));

const RulesPage: FC<IRulesPageProps> = () => {
  const { translate } = useIntlCustom();
  // hooks变量
  const [listData, setListData] = useState<IDetailData[]>([]);
  const [detailType, setDetailType] = useState<string>(OPERATE_TYPE.list);
  const [detail, setDetail] = useState<IDetailData | object>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [paginationConfig, setPaginationConfig] = useState<TablePaginationConfig | false>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [isShowAiform, setIsShowAiform] = useState<boolean>(false);
  // 是否打开弹层
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const detailRef = useRef<any>(null);

  // @ts-ignore
  const optionList = defOptionList.concat({
    type: OPERATE_TYPE.aiHelp,
    onCustomize: (val) => {
      (setIsShowAiform(false), setOpen(val));
    },
  });

  useEffect(() => {
    getTableData();
  }, []);

  // 获取表格数据
  const getTableData = async ({
    searchValue = searchParam,
    pagination = { ...DEFAULT_PAGINATION },
  } = {}): Promise<void> => {
    setLoading(true);
    try {
      const res = await common.getTableListData({
        searchValue,
        pagination,
        url: urlObj.list,
      });
      const { data, total } = res;
      setListData(data);
      total &&
        setPaginationConfig({
          showSizeChanger: true,
          showQuickJumper: true,
          total,
        });
    } catch (error) {
      setListData([]);
    } finally {
      setLoading(false);
    }
  };

  // 事件处理
  const handleAction = (type: string, row: IDetailData): void => {
    // 是否展示自定义操作按钮内容
    const isOnCustomize: any = optionList?.find((item: any) => item?.type === type);
    if (isOnCustomize?.onCustomize) {
      return isOnCustomize.onCustomize(true);
    }
    setDrawerOpen(true);
    setDetailType(type);
    const detailData = setRulesDetailData(row);
    setDetail(detailData);
  };

  // 返回事件
  const handleCardBack = (): void => {
    setDetailType(OPERATE_TYPE.list);
    setDrawerOpen(false);
  };

  // search组件hooks
  const { searchParam, searchChildren } = usePageSearch({
    getTableData,
    handleAction,
  });

  // table组件hooks
  const { tableChildren } = usePageTable({
    listData,
    loading,
    paginationConfig,
    optionList,
    getTableData,
    handleAction,
  });

  // 渲染按钮
  const extra = () => {
    return (
      <Space style={{ margin: '0 12px' }}>
        <GradientButton
          onClick={() => {
            (setIsShowAiform(true), setOpen(true));
          }}
        >
          {translate('callParam', 'aiRulesCheck')}
        </GradientButton>
      </Space>
    );
  };

  // formAction组件hooks
  const { renderFormAction, handleDrawerSubmit } = usePageFormAction({
    detailRef,
    extra,
    detailType,
    formActionPermissionObj,
    getTableData,
    handleCardBack,
    handleAction,
  });

  // 详情组件
  const formChildren = (): ReactNode => {
    return <Detail ref={detailRef} data={detail as IDetailData} ruleType={'auth'} type={detailType} urlObj={urlObj} />;
  };

  return (
    <>
      <LayoutTemplate
        searchChildren={searchChildren()}
        tableChildren={tableChildren()}
        formChildren={formChildren()}
        drawerOpen={drawerOpen}
        type={detailType}
        intlPrefix={prefix}
        cardTitle={cardTitle}
        cardExtra={renderFormAction()}
        handleClose={handleCardBack}
        handleSubmit={handleDrawerSubmit}
      />
      {open && (
        <CommonModal
          type="AI_HELP_MODAL"
          open={open}
          content={() => <AiContent isShowAiform={isShowAiform} />}
          onClose={() => setOpen(false)}
          onSubmit={() => setOpen(false)}
        />
      )}
    </>
  );
};

export default RulesPage;
