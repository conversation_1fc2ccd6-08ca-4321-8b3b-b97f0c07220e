import { I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { ICommonTableActionItem } from '@/types/ICommon';
import { getRequestData } from '@/utils/urlUtil';

// 国际化前缀
export const prefix = I18N_COMON_PAGENAME.RULES;
export const cardTitle = 'ruleManage';
export const formActionPermissionObj = I18N_COMON_PAGENAME.RULES;
export const urlObj = {
  list: urlConstants.RULES_AUTH.LIST,
  create: urlConstants.RULES_AUTH.CREATE,
  edit: urlConstants.RULES_AUTH.EDIT,
  // delete: urlConstants.RULES_AUTH.DELETE,
  getRequestData,
};

// 重置查询条件
export const resetValue = { ruleType: null, ruleId: '', ruleName: '' };

// 传递给antv/table的参数
export const tableProps = {
  scroll: {
    x: 1500,
    y: 2000,
  },
};

// 操作列配置
export const defOptionList: ICommonTableActionItem[] = [
  { type: OPERATE_TYPE.detail },
  { type: OPERATE_TYPE.copy },
  { type: OPERATE_TYPE.edit },
];
