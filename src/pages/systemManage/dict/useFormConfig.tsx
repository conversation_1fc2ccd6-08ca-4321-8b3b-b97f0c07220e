import { COMPONENT_TYPE, FORMITEM_TYPE, I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';
import { IFormConfig } from '@/types/IForm';
import { EditableFormInstance } from '@ant-design/pro-components';
import { useRef, useState } from 'react';

const useFormConfig = (type: string) => {
  const [editType, setEditType] = useState<string>(type);
  const [editTableData, setEditTableData] = useState<any[]>([]);
  const editableFormRef = useRef<EditableFormInstance>();
  // 可编辑表格操作列
  const editColumns = [
    {
      width: 120,
      key: 'dictCode',
      title: 'dictCode',
      dataIndex: 'dictCode',
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: 'itemCode',
      dataIndex: 'itemCode',
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: 'itemName',
      dataIndex: 'itemName',
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: 'itemValue',
      dataIndex: 'itemValue',
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: 'sortOrder',
      dataIndex: 'sortOrder',
      valueType: 'select',
      showKey: true,
    },
  ];
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'dictCode',
          label: 'dictCode',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'dictName',
          label: 'dictName',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'dictType',
          label: 'dictType',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'sortOrder',
          label: 'sortOrder',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'description',
          label: 'itemDesc',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
      ],
    },
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'dictItem',
    },
    {
      type: FORMITEM_TYPE.EditTable,
      rowKey: 'id',
      columns: editColumns,
      dataSource: editTableData,
      canEdit: true,
      editableFormRef,
      optionCount: 1,
      getOptionList: (row) => [
        {
          optionType: OPERATE_TYPE.edit,
          disabled: editType === OPERATE_TYPE.detail,
        },
        {
          optionType: OPERATE_TYPE.delete,
          disabled: editType === OPERATE_TYPE.detail,
        },
      ],
      onCreate: (index) => ({ id: Date.now() + 1, isAdd: true }),
    },
  ];

  return {
    infoFormConfig,
    editableFormRef,
    setEditTableData,
  };
};

export default useFormConfig;
