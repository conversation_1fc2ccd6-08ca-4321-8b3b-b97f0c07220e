import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.SYSTEM_MANAGE,
  cardTitle: 'dictManage',
  // 页面接口请求
  urlObj: {
    list: urlConstants.STATION_MANAGEMENT.LIST,
    create: urlConstants.STATION_MANAGEMENT.CREATE,
    edit: urlConstants.STATION_MANAGEMENT.EDIT,
    delete: urlConstants.STATION_MANAGEMENT.DELETE,

    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'dictName',
      label: 'dictName',
      type: COMPONENT_TYPE.INPUT,
      data: DICT_CONSTANTS.STATION,
    },
    {
      value: 'dictType',
      label: 'dictType',
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 80,
    },
    {
      title: 'dictCode',
      dataIndex: 'dictCode',
      key: 'dictCode',
    },
    {
      title: 'dictName',
      dataIndex: 'dictName',
      key: 'dictName',
    },
    {
      title: 'dictType',
      dataIndex: 'dictType',
      key: 'dictType',
      width: 120,
    },
    {
      title: 'sortOrder',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 120,
    },
    {
      title: 'status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
    },
    {
      title: 'dictDesc',
      dataIndex: 'description',
      key: 'description',
      width: 120,
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 150,
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 160,
      valueType: RENDER_TYPE.DateTime,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};
