export const ORG_TYPE_OPTIONS = [
  // 这里的 translate 需在主组件中处理后传入
  { label: '部门', value: 'department' },
  { label: '公司', value: 'company' },
  { label: '分公司', value: 'branch' },
  { label: '子公司', value: 'subsidiary' },
];

export const PARENT_ORG_OPTIONS = [
  { label: '运营部', value: '0-0-0' },
  { label: '决策部', value: 'decision' },
  { label: '总公司', value: 'head' },
  { label: '系统部', value: 'system' },
];

export const ORG_DETAIL_MAP = {
  '0-0-0-0': {
    id: '000001',
    name: '决策部',
    parent: '运营部',
    type: '部门',
    count: 9,
  },
  '0-0-0-1': {
    id: '000002',
    name: '运营部',
    parent: '运营部',
    type: '部门',
    count: 7,
  },
  // ... 其他节点
};

// 在文件末尾添加
// 国际化key常量，便于统一管理
const I18N_KEYS = {
  ORG_ID_REQUIRED: 'orgIdRequired',
  ORG_NAME_REQUIRED: 'orgNameRequired',
  PARENT_ORG_REQUIRED: 'parentOrgRequired',
  ORG_TYPE_REQUIRED: 'orgTypeRequired',
  ORG_ID_PLACEHOLDER: 'orgIdPlaceholder',
  ORG_NAME_PLACEHOLDER: 'orgNamePlaceholder',
  PARENT_ORG_PLACEHOLDER: 'parentOrgPlaceholder',
  ORG_TYPE_PLACEHOLDER: 'orgTypePlaceholder',
};

export default I18N_KEYS;
