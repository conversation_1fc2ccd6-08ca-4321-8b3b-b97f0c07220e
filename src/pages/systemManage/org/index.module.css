.container {
  display: flex;
  height: 100vh;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.routerBar {
  width: 100%;
}

.content {
  flex: 1;
  width: 100%;
  background: #fff;
  border-radius: 16px;
  display: flex;
  overflow: hidden;
  min-height: 0;
  height: 100%;
}

.sidebar {
  width: 40%;
  min-width: 320px;
  border-right: 1px solid #e8e8e8;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  position: relative;
}

.treeHeader {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.treeTitle {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 16px;
  color: #262626;
  display: flex;
  align-items: center;
}

.treeTitle::before {
  content: '';
  width: 4px;
  height: 18px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border-radius: 2px;
  margin-right: 8px;
}

.searchInput {
  width: 100%;
  border-radius: 8px;
}

.orgTree {
  flex: 1;
  padding: 16px;
  background: #fff;
  overflow-y: auto;
}

/* 树节点样式增强 */
.orgTree .ant-tree-node-content-wrapper {
  border-radius: 6px;
  transition: all 0.2s ease;
  padding: 4px 8px;
  position: relative;
}

.orgTree .custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 24px;
}

.orgTree .add-icon {
  opacity: 0;
  transition: all 0.2s ease;
  margin-left: 8px;
  color: #8c8c8c;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
}

.orgTree .custom-tree-node:hover .add-icon {
  opacity: 1;
  color: #1890ff;
}

.orgTree .add-icon:hover {
  background-color: #e6f7ff;
  transform: scale(1.1);
}

/* 确保选中状态下的样式不影响加号按钮 */
.orgTree .ant-tree-node-selected .custom-tree-node {
  color: #fff;
}

.orgTree .ant-tree-node-selected .add-icon {
  color: rgba(255, 255, 255, 0.85);
}

.orgTree .ant-tree-node-selected .add-icon:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
}

.orgTree .ant-tree-node-content-wrapper:hover {
  background-color: #e6f7ff;
}

.orgTree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: #1890ff;
  color: #fff;
}

.orgTree .ant-tree-title {
  font-size: 14px;
}

.detail {
  width: 60%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  height: 100%;
  min-height: 0;
  overflow: hidden;
}

.card {
  width: 380px;
  min-height: 340px;
  border-radius: 10px;
  border: none;
  background: #fff;
}

.cardContent {
  font-size: 18px;
  margin-bottom: 24px;
  font-weight: 500;
}

.cardContentItem {
  margin-bottom: 16px;
}

.actionBtn {
  width: 120px;
}
