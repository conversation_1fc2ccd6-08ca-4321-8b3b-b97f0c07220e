import useIntlCustom from '@/hooks/useIntlCustom';
import RouterBar from '@/layouts/header/RouterBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Button, Divider, Form, Input, message, Modal } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import AddOrgModal from './components/AddOrgModal';
import OrgDetailCard from './components/OrgDetailCard';
import OrgTree from './components/OrgTree';
import { ORG_DETAIL_MAP, ORG_TYPE_OPTIONS, PARENT_ORG_OPTIONS } from './constants';
import styles from './index.module.css';

// 定义组织节点类型
interface OrgNode {
  title: string;
  key: string;
  children?: OrgNode[];
}

const OrgPage: React.FC = () => {
  const { translate } = useIntlCustom();
  const prefix = 'systemManage';
  // 状态管理
  const [selectedKey, setSelectedKey] = useState<string>('0-0-0-0');
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [orgParentDetail, setOrgParentDetail] = useState({});
  const [detail, setDetail] = useState(ORG_DETAIL_MAP['0-0-0-0']);
  const [editForm] = Form.useForm();

  // 动态国际化 options
  const orgTypeOptions = ORG_TYPE_OPTIONS.map((opt) => ({ ...opt, label: translate(prefix, opt.label) }));
  const parentOrgOptions = PARENT_ORG_OPTIONS.map((opt) => ({ ...opt, label: translate(prefix, opt.label) }));

  // 使用 useMemo 优化树形数据的生成
  const treeData = useMemo<OrgNode[]>(
    () => [
      {
        title: translate(prefix, 'orgTreeRoot'),
        key: '0',
        children: [
          {
            title: translate(prefix, 'orgTreeCreditCard'),
            key: '0-0',
            children: [
              {
                title: translate(prefix, 'orgTreeOps'),
                key: '0-0-0',
                children: [
                  {
                    title: translate(prefix, 'orgTreeDecision'),
                    key: '0-0-0-0',
                  },
                  {
                    title: translate(prefix, 'orgTreeOperation'),
                    key: '0-0-0-1',
                  },
                ],
              },
            ],
          },
          { title: translate(prefix, 'orgTreeSystem'), key: '0-1' },
          { title: translate(prefix, 'orgTreeHead'), key: '0-2' },
          { title: translate(prefix, 'orgTreeItem13'), key: '0-3' },
        ],
      },
    ],
    [translate, prefix],
  );

  // 搜索树节点
  const searchInTree = useCallback((nodes: OrgNode[], searchText: string): string[] => {
    const matchedKeys: string[] = [];
    const traverse = (nodeList: OrgNode[]) => {
      nodeList.forEach((node) => {
        if (node.title?.toLowerCase().includes(searchText?.toLowerCase())) {
          matchedKeys.push(node.key);
        }
        if (node.children) {
          traverse(node.children);
        }
      });
    };
    traverse(nodes);
    return matchedKeys;
  }, []);

  // 处理搜索
  const handleSearch = useCallback(
    (value: string) => {
      if (value) {
        const matchedKeys = searchInTree(treeData, value);
        setExpandedKeys(matchedKeys);
        setAutoExpandParent(true);
        if (matchedKeys.length > 0) {
          setSelectedKey(matchedKeys[0]);
        }
      } else {
        setExpandedKeys(['0', '0-0', '0-0-0']);
        setAutoExpandParent(false);
        setSelectedKey('0-0-0-0');
      }
    },
    [treeData, searchInTree],
  );

  // 处理树节点选择
  const handleTreeSelect = useCallback(
    (keys: React.Key[]) => {
      setSelectedKey(keys[0]?.toString() || '');
      setIsEditing(false); // 切换节点时退出编辑
      const newDetail = ORG_DETAIL_MAP[keys[0]?.toString()] || {};
      setDetail(newDetail);
      editForm.setFieldsValue(newDetail); // 若在编辑态也同步表单
    },
    [editForm],
  );

  // 处理树节点展开
  const handleTreeExpand = useCallback((keys: React.Key[]) => {
    setExpandedKeys(keys.map((key) => key.toString()));
    setAutoExpandParent(false);
  }, []);

  // 处理编辑操作
  const handleEdit = () => {
    setIsEditing(true);
    editForm.setFieldsValue(detail);
  };

  // 处理保存操作
  const handleSave = () => {
    editForm.validateFields().then((values) => {
      setDetail({ ...detail, ...values });
      setIsEditing(false);
      message.success('保存成功');
    });
  };

  // 处理取消操作
  const handleCancel = () => {
    setIsEditing(false);
    editForm.setFieldsValue(detail);
  };

  // 在组件内部添加处理加号按钮点击的函数
  const handleAddClick = (node) => {
    setAddModalVisible(true);
    setOrgParentDetail(node);
  };

  // 处理删除操作
  const handleDelete = useCallback(() => {
    Modal.confirm({
      title: translate('common', 'confirmDelete'),
      icon: <ExclamationCircleOutlined />,
      content: translate('common', 'deleteConfirmContent'),
      onOk() {
        setLoading(true);
        // 模拟API调用
        setTimeout(() => {
          message.success(translate('common', 'deleteSuccess'));
          setLoading(false);
        }, 1000);
      },
    });
  }, [translate]);

  // 新增弹窗取消
  const handleAddCancel = () => {
    setOrgParentDetail({});
    setAddModalVisible(false);
  };

  // 新增弹窗提交 - 修改为async函数
  const handleAddOrg = useCallback(async (values) => {
    setLoading(true);
    setOrgParentDetail({});
    // 模拟API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        message.success('新增成功');
        setLoading(false);
        setAddModalVisible(false);
        resolve(undefined);
      }, 1000);
    });
  }, []);

  // 初始化展开的节点
  useEffect(() => {
    setExpandedKeys(['0', '0-0', '0-0-0']);
    // 初始化右侧卡片
    setDetail(ORG_DETAIL_MAP['0-0-0-0']);
    editForm.setFieldsValue(ORG_DETAIL_MAP['0-0-0-0']);
  }, []);

  return (
    <div className={styles.container}>
      <div className={`app-block m-b ${styles.routerBar}`}>
        <RouterBar />
        <Divider className="m-tb-0" />
      </div>
      <div className={styles.content}>
        {/* 左侧树形菜单 */}
        <div className={styles.sidebar}>
          <div className={styles.treeHeader}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div className={styles.treeTitle}>{translate(prefix, 'orgTree')}</div>
              <Button type="primary" onClick={() => setAddModalVisible(true)}>
                {translate(prefix, 'addOrg')}
              </Button>
            </div>
            <Input.Search
              className={styles.searchInput}
              placeholder={translate(prefix, 'searchOrgPlaceholder')}
              allowClear
              onSearch={handleSearch}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ marginTop: 12 }}
            />
          </div>

          <OrgTree
            treeData={treeData}
            selectedKey={selectedKey}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onSelect={handleTreeSelect}
            onExpand={handleTreeExpand}
            onAddClick={handleAddClick}
          />
        </div>
        {/* 新增机构弹窗 */}
        <AddOrgModal
          orgParentDetail={orgParentDetail}
          visible={addModalVisible}
          onCancel={handleAddCancel}
          onOk={handleAddOrg}
          loading={loading}
          prefix={prefix}
        />
        {/* 右侧详情 */}
        <div className={styles.detail}>
          <OrgDetailCard
            detail={detail}
            isEditing={isEditing}
            editForm={editForm}
            orgTypeOptions={orgTypeOptions}
            parentOrgOptions={parentOrgOptions}
            onEdit={handleEdit}
            onSave={handleSave}
            onCancel={handleCancel}
            onDelete={handleDelete}
            loading={loading}
            translate={translate}
            prefix={prefix}
          />
        </div>
      </div>
    </div>
  );
};

export default OrgPage;
