import { PlusOutlined } from '@ant-design/icons';
import { Tree } from 'antd';
import React, { useState } from 'react';
import styles from './../index.module.css';

interface OrgTreeProps {
  treeData: any[];
  selectedKey: string;
  expandedKeys: string[];
  autoExpandParent: boolean;
  onSelect: (keys: React.Key[]) => void;
  onExpand: (keys: React.Key[]) => void;
  onAddClick?: (node: any) => void;
}

const OrgTree: React.FC<OrgTreeProps> = ({
  treeData,
  selectedKey,
  expandedKeys,
  autoExpandParent,
  onSelect,
  onExpand,
  onAddClick,
}) => {
  // 存储当前悬浮的节点ID
  const [hoveredNodeId, setHoveredNodeId] = useState<string | null>(null);

  // 自定义节点渲染
  const renderTreeNode = (node, parent) => {
    return (
      <div
        className={styles['custom-tree-node']}
        onMouseEnter={() => setHoveredNodeId(node.key)}
        onMouseLeave={() => setHoveredNodeId(null)}
      >
        <span>{node.title}</span>
        {hoveredNodeId === node.key && onAddClick && (
          <PlusOutlined
            className={styles['add-icon']}
            onClick={(e) => {
              e.stopPropagation(); // 阻止事件冒泡，避免触发节点选择
              onAddClick(parent);
            }}
          />
        )}
      </div>
    );
  };

  // 递归处理树数据，添加自定义渲染函数
  const processTreeData = (data, parent) => {
    return data.map((node) => ({
      ...node,
      title: renderTreeNode(node, parent),
      children: node.children ? processTreeData(node.children, node) : undefined,
    }));
  };

  return (
    <div className={styles.orgTree}>
      <Tree
        treeData={processTreeData(treeData, null)}
        selectedKeys={[selectedKey]}
        expandedKeys={expandedKeys}
        autoExpandParent={autoExpandParent}
        onSelect={onSelect}
        onExpand={onExpand}
        className={styles['custom-tree']}
        showLine={{ showLeafIcon: false }}
      />
    </div>
  );
};

export default OrgTree;
