import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Button, Form, Input, Select } from 'antd';
import React from 'react';

const { Option } = Select;

interface OrgDetailCardProps {
  detail: any;
  isEditing: boolean;
  editForm: any;
  orgTypeOptions: { label: string; value: string }[];
  parentOrgOptions: { label: string; value: string }[];
  onEdit: () => void;
  onSave: () => void;
  onCancel: () => void;
  onDelete: () => void;
  loading: boolean;
  translate: (prefix: string, key: string) => string;
  prefix: string;
}

const OrgDetailCard: React.FC<OrgDetailCardProps> = ({
  detail,
  isEditing,
  editForm,
  orgTypeOptions,
  parentOrgOptions,
  onEdit,
  onSave,
  onCancel,
  onDelete,
  loading,
  translate,
  prefix,
}) => (
  <div style={{ width: '380px' }}>
    <div style={{ fontSize: 18, marginBottom: 24, fontWeight: 500 }}>
      <Form form={editForm} layout="vertical" initialValues={detail}>
        <div style={{ marginBottom: 16 }}>
          <span>{translate(prefix, 'orgId')}：</span>
          <span>{detail.id}</span>
        </div>
        <div style={{ marginBottom: 16 }}>
          <span>{translate(prefix, 'orgName')}：</span>
          {isEditing ? (
            <Form.Item name="name" noStyle>
              <Input style={{ width: 140 }} />
            </Form.Item>
          ) : (
            <span>{detail.name}</span>
          )}
        </div>
        <div style={{ marginBottom: 16 }}>
          <span>{translate(prefix, 'parentOrg')}：</span>
          {isEditing ? (
            <Form.Item name="parent" noStyle>
              <Select style={{ minWidth: 140 }}>
                {parentOrgOptions.map((opt) => (
                  <Option key={opt.value} value={opt.value}>
                    {opt.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          ) : (
            <span>{detail.parent}</span>
          )}
        </div>
        <div style={{ marginBottom: 16 }}>
          <span>{translate(prefix, 'orgCount')}：</span>
          <span>{detail.count}</span>
        </div>
      </Form>
    </div>
    <div style={{ display: 'flex', gap: 16, marginTop: 32, justifyContent: 'center' }}>
      {isEditing ? (
        <>
          <Button type="primary" icon={<EditOutlined />} onClick={onSave} loading={loading} style={{ marginRight: 16 }}>
            {translate(prefix, 'save')}
          </Button>
          <Button danger onClick={onCancel}>
            {translate(prefix, 'cancel')}
          </Button>
        </>
      ) : (
        <>
          <Button type="primary" icon={<EditOutlined />} onClick={onEdit} loading={loading} style={{ marginRight: 16 }}>
            {translate(prefix, 'edit')}
          </Button>
          <Button type="primary" danger icon={<DeleteOutlined />} onClick={onDelete} loading={loading}>
            {translate(prefix, 'delete')}
          </Button>
        </>
      )}
    </div>
  </div>
);

export default OrgDetailCard;
