import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, FORMITEM_TYPE, I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import { IFormConfig } from '@/types/IForm';

const useFormConfig = (type: string, detailData) => {
  // 表单字段
  const InfoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'userId',
          label: 'userId',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'userName',
          label: 'userName',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'originalPassword',
          label: 'originalPassword',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'orgName',
          label: 'orgName',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.PARAM_STATUS,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'role',
          label: 'role',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.PARAM_STATUS,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'position',
          label: 'position',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.PARAM_STATUS,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'subPositionPermission',
          label: 'subPositionPermission',
          data: DICT_CONSTANTS.PARAM_STATUS,
          rules: [{ required: true }],
        },
      ],
    },
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'gender',
          label: 'gender',
          data: DICT_CONSTANTS.GENDER,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'language',
          label: 'language',
          data: DICT_CONSTANTS.LANGUAGE,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'level',
          label: 'level',
          data: DICT_CONSTANTS.LEVEL,
          rules: [{ required: true }],
        },
      ],
    },
  ];

  return {
    InfoFormConfig,
  };
};

export default useFormConfig;
