import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

// const getRequestData = (postData, type: string, rowData): object => {
//   switch (type) {
//     case OPERATE_TYPE.edit:
//     case OPERATE_TYPE.create:
//       return {
//         ...postData.formData,
//       };
//     default:
//       return { ...postData };
//   }
// };

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.SYSTEM_MANAGE,
  cardTitle: 'userManage',
  // 页面接口请求
  urlObj: {
    list: urlConstants.USER_MANAGEMENT.LIST,
    create: urlConstants.USER_MANAGEMENT.CREATE,
    edit: urlConstants.USER_MANAGEMENT.EDIT,
    delete: urlConstants.USER_MANAGEMENT.DELETE,
    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'userId',
      label: 'userId',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'userName',
      label: 'userName',
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 80,
    },
    {
      title: 'userId',
      dataIndex: 'userId',
      key: 'userId',
      width: 120,
    },
    {
      title: 'userName',
      dataIndex: 'userName',
      key: 'userName',
      width: 220,
      render: (text) => <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>,
    },
    {
      title: 'orgName',
      dataIndex: 'orgName',
      key: 'orgName',
      width: 120,
      data: DICT_CONSTANTS.LABEL_TYPE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'LABEL_TYPE',
    },
    {
      title: 'role',
      dataIndex: 'role',
      key: 'role',
      width: 120,
      data: DICT_CONSTANTS.LABEL_SOURCE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'LABEL_SOURCE',
    },
    {
      title: 'position',
      dataIndex: 'position',
      key: 'position',
      width: 150,
      data: DICT_CONSTANTS.LABEL_ATTRIBUTE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'LABEL_ATTRIBUTE',
    },
    {
      width: 100,
      key: 'gender',
      title: 'gender',
      dataIndex: 'gender',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      width: 100,
      key: 'language',
      title: 'language',
      dataIndex: 'language',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      width: 100,
      key: 'level',
      title: 'level',
      dataIndex: 'level',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      width: 120,
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      width: 160,
      valueType: RENDER_TYPE.DateTime,
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {};
