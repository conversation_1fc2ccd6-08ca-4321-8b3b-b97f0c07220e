// 业务參數/用户管理
import { OPERATE_TYPE } from '@/constants/publicConstant';
import React, { Suspense, useCallback, useState } from 'react';
import { dictEnum, pageConfig } from './pageConfig';
import useFormConfig from './useFormConfig';

const PageTemplate = React.lazy(() => import('@/components/templates/PageTemplate'));

interface DetailData {
  userName?: string;
  userId?: string;
  [key: string]: any;
}

const resetValue: DetailData = { userName: '', userId: '' };

const UserManagePage: React.FC = () => {
  const { urlObj, prefix, searchSource, columns, cardTitle } = pageConfig;
  const [detailData, setDetailData] = useState<DetailData>({});
  const [type, setType] = useState<string>(OPERATE_TYPE.list);
  const { InfoFormConfig } = useFormConfig(type, detailData);

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 表格配置
  const tableConfig = {
    columns,
    rowKey: 'paramIndex',
    showPagination: true,
  };

  // 表单配置
  const formConfig = {
    config: InfoFormConfig,
    data: { ...detailData },
    intlPrefix: prefix,
  };

  // 列表按钮操作
  const handleAction = useCallback((editType: string, row: DetailData) => {
    setDetailData({ ...row });
    setType(editType);
  }, []);

  return (
    <Suspense fallback={<div>加载中...</div>}>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        onAction={handleAction}
      />
    </Suspense>
  );
};

export default React.memo(UserManagePage);
