import { COMPONENT_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import { IFormConfig } from '@/types/IForm';
// 角色状态枚举
export const roleStatusEunm = [
  { key: 'Y', label: 'enable' },
  { key: 'N', label: 'unEnable' },
];

const useFormConfig = (type: string) => {
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'role',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'roleId',
          label: 'roleId',
          rules: [{ required: true, max: 32 }],
          maxLength: 32,
          disabled: type === OPERATE_TYPE.edit,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'roleName',
          label: 'roleName',
          rules: [{ required: true, max: 32 }],
          maxLength: 32,
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'roleDesc',
          label: 'roleDesc',
          rules: [{ required: true, max: 32 }],
          maxLength: 32,
        },
        {
          type: COMPONENT_TYPE.RADIO,
          name: 'roleStatus',
          label: 'roleStatus',
          rules: [{ required: true }],
          data: roleStatusEunm.map((item) => ({
            value: item.label,
            key: item.key,
          })),
          showKey: false,
        },
      ],
    },
  ];

  return {
    infoFormConfig,
  };
};

export default useFormConfig;
