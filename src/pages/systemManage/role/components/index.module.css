.drawerBody {
  display: flex;
  height: calc(100vh - 200px);
}

.leftPane {
  flex: 1;
  border-right: 1px solid #f0f0f0;
  padding-right: 16px;
}

.searchBar {
  margin-bottom: 16px;
}

.searchInput {
  margin-bottom: 8px;
}

.between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tree {
  height: calc(100% - 80px);
  overflow: auto;
}

.rightPane {
  flex: 1;
  padding-left: 16px;
}

.listItem {
  padding: 8px 0;
}

.list {
  height: calc(100% - 60px);
  overflow: auto;
}
