import { I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import { TSysMenuItem } from '@/types/TCommon';
import { DownOutlined } from '@ant-design/icons';
import { Button, Drawer, Input, List, Space, Tree, Typography } from 'antd';
import { FC, memo, useEffect, useMemo, useState } from 'react';
import styles from './index.module.css';

const { Search } = Input;
const { Text } = Typography;

interface MenuAuthComType {
  open: boolean;
  onSubmit: (selectedPermissions: string[]) => void;
  onCancel: (val: boolean) => void;
  initialSelected?: string[]; // 初始选中的权限ID
}

interface TreeNode extends TSysMenuItem {
  title: string;
  key: string;
  children?: TreeNode[];
  checkable?: boolean;
  createTime?: string;
  updateTime?: string;
}

// 写死的假数据
const mockTreeData: TreeNode[] = [
  {
    menuId: '1',
    parentMenuId: '0',
    menuName: '商机列表',
    menuType: '0',
    iconId: 'TeamOutlined',
    orderSeq: '1',
    menuStatus: 'Y',
    createTime: '2024-6-20 12:12:12',
    updateTime: '2024-6-21 13:13:13',
    createUser: 'testUser1',
    updateUser: 'testUser2',
    permissionId: 'opportunity-list',
    title: '商机列表',
    key: '1',
    checkable: true,
    children: [
      {
        menuId: '1001',
        parentMenuId: '1',
        menuName: '商机详情',
        menuType: '1',
        iconId: '',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'opportunity-detail',
        title: '商机详情',
        key: '1001',
        checkable: true,
      },
      {
        menuId: '1002',
        parentMenuId: '1',
        menuName: '新增',
        menuType: '1',
        iconId: '',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'opportunity-add',
        title: '新增',
        key: '1002',
        checkable: true,
      },
      {
        menuId: '1003',
        parentMenuId: '1',
        menuName: '删除',
        menuType: '1',
        iconId: '',
        orderSeq: '3',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'opportunity-delete',
        title: '删除',
        key: '1003',
        checkable: true,
      },
    ],
  },
  {
    menuId: '2',
    parentMenuId: '0',
    menuName: '采购管理',
    menuType: '0',
    iconId: 'TeamOutlined',
    orderSeq: '2',
    menuStatus: 'Y',
    createTime: '2024-6-20 12:12:12',
    updateTime: '2024-6-21 13:13:13',
    createUser: 'testUser1',
    updateUser: 'testUser2',
    permissionId: 'procurement-manage',
    title: '采购管理',
    key: '2',
    checkable: true,
    children: [
      {
        menuId: '2001',
        parentMenuId: '2',
        menuName: '新增采购订单',
        menuType: '1',
        iconId: '',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'procurement-add',
        title: '新增采购订单',
        key: '2001',
        checkable: true,
      },
      {
        menuId: '2002',
        parentMenuId: '2',
        menuName: '查看采购订单',
        menuType: '1',
        iconId: '',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'procurement-view',
        title: '查看采购订单',
        key: '2002',
        checkable: true,
      },
    ],
  },
  {
    menuId: '3',
    parentMenuId: '0',
    menuName: '项目列表',
    menuType: '0',
    iconId: 'TeamOutlined',
    orderSeq: '3',
    menuStatus: 'Y',
    createTime: '2024-6-20 12:12:12',
    updateTime: '2024-6-21 13:13:13',
    createUser: 'testUser1',
    updateUser: 'testUser2',
    permissionId: 'project-list',
    title: '项目列表',
    key: '3',
    checkable: true,
    children: [
      {
        menuId: '3001',
        parentMenuId: '3',
        menuName: '新增',
        menuType: '1',
        iconId: '',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'project-add',
        title: '新增',
        key: '3001',
        checkable: true,
      },
      {
        menuId: '3002',
        parentMenuId: '3',
        menuName: '编辑',
        menuType: '1',
        iconId: '',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'project-edit',
        title: '编辑',
        key: '3002',
        checkable: true,
      },
      {
        menuId: '3003',
        parentMenuId: '3',
        menuName: '删除',
        menuType: '1',
        iconId: '',
        orderSeq: '3',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'project-delete',
        title: '删除',
        key: '3003',
        checkable: true,
      },
      {
        menuId: '3004',
        parentMenuId: '3',
        menuName: '复制',
        menuType: '1',
        iconId: '',
        orderSeq: '4',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'project-copy',
        title: '复制',
        key: '3004',
        checkable: true,
      },
    ],
  },
  {
    menuId: '4',
    parentMenuId: '0',
    menuName: '系统管理',
    menuType: '0',
    iconId: 'TeamOutlined',
    orderSeq: '4',
    menuStatus: 'Y',
    createTime: '2024-6-20 12:12:12',
    updateTime: '2024-6-21 13:13:13',
    createUser: 'testUser1',
    updateUser: 'testUser2',
    permissionId: 'system-manage',
    title: '系统管理',
    key: '4',
    checkable: true,
    children: [
      {
        menuId: '4001',
        parentMenuId: '4',
        menuName: '用户管理',
        menuType: '0',
        iconId: 'SettingOutlined',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'system-user-manage',
        title: '用户管理',
        key: '4001',
        checkable: true,
        children: [
          {
            menuId: '4001001',
            parentMenuId: '4001',
            menuName: '用户查看',
            menuType: '1',
            iconId: '',
            orderSeq: '1',
            menuStatus: 'Y',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissionId: 'system-user-view',
            title: '用户查看',
            key: '4001001',
            checkable: true,
          },
          {
            menuId: '4001002',
            parentMenuId: '4001',
            menuName: '用户编辑',
            menuType: '1',
            iconId: '',
            orderSeq: '2',
            menuStatus: 'Y',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissionId: 'system-user-edit',
            title: '用户编辑',
            key: '4001002',
            checkable: true,
          },
        ],
      },
      {
        menuId: '4002',
        parentMenuId: '4',
        menuName: '角色管理',
        menuType: '0',
        iconId: 'SettingOutlined',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'system-role-manage',
        title: '角色管理',
        key: '4002',
        checkable: true,
        children: [
          {
            menuId: '4002001',
            parentMenuId: '4002',
            menuName: '角色查看',
            menuType: '1',
            iconId: '',
            orderSeq: '1',
            menuStatus: 'Y',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissionId: 'system-role-view',
            title: '角色查看',
            key: '4002001',
            checkable: true,
          },
          {
            menuId: '4002002',
            parentMenuId: '4002',
            menuName: '角色编辑',
            menuType: '1',
            iconId: '',
            orderSeq: '2',
            menuStatus: 'Y',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissionId: 'system-role-edit',
            title: '角色编辑',
            key: '4002002',
            checkable: true,
          },
        ],
      },
    ],
  },
  {
    menuId: '5',
    parentMenuId: '0',
    menuName: '一级选项2',
    menuType: '0',
    iconId: 'TeamOutlined',
    orderSeq: '5',
    menuStatus: 'Y',
    createTime: '2024-6-20 12:12:12',
    updateTime: '2024-6-21 13:13:13',
    createUser: 'testUser1',
    updateUser: 'testUser2',
    permissionId: 'level1-option2',
    title: '一级选项2',
    key: '5',
    checkable: true,
  },
  {
    menuId: '6',
    parentMenuId: '0',
    menuName: '一级选项2',
    menuType: '0',
    iconId: 'TeamOutlined',
    orderSeq: '6',
    menuStatus: 'Y',
    createTime: '2024-6-20 12:12:12',
    updateTime: '2024-6-21 13:13:13',
    createUser: 'testUser1',
    updateUser: 'testUser2',
    permissionId: 'level1-option2-2',
    title: '一级选项2',
    key: '6',
    checkable: true,
  },
];

const MenuAuthCom: FC<MenuAuthComType> = ({ open, onSubmit, onCancel, initialSelected = [] }) => {
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>(initialSelected);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const { translate } = useIntlCustom();

  // 初始化数据
  useEffect(() => {
    if (open) {
      // 使用写死的假数据
      setTreeData(mockTreeData);
      // 默认展开第一级
      const firstLevelKeys = mockTreeData.map((item) => item.key);
      setExpandedKeys(firstLevelKeys);
    }
  }, [open]);

  // 搜索过滤
  const filteredTreeData = useMemo(() => {
    if (!searchValue) return treeData;

    const filterTree = (nodes: TreeNode[]): TreeNode[] => {
      return nodes
        .filter((node) => {
          const titleMatch = node.title.toLowerCase().includes(searchValue.toLowerCase());
          const childrenMatch = node.children && filterTree(node.children).length > 0;
          return titleMatch || childrenMatch;
        })
        .map((node) => ({
          ...node,
          children: node.children ? filterTree(node.children) : undefined,
        }));
    };

    return filterTree(treeData);
  }, [treeData, searchValue]);

  // 获取所有权限节点（扁平化）
  const getAllPermissionNodes = (nodes: TreeNode[]): TreeNode[] => {
    const result: TreeNode[] = [];
    const traverse = (nodeList: TreeNode[]) => {
      nodeList.forEach((node) => {
        result.push(node);
        if (node.children) {
          traverse(node.children);
        }
      });
    };
    traverse(nodes);
    return result;
  };

  // 获取选中节点的完整路径
  const getSelectedPaths = (): string[] => {
    const allNodes = getAllPermissionNodes(treeData);
    const selectedNodes = allNodes.filter((node) => selectedKeys.includes(String(node.key)));

    const getNodePath = (node: TreeNode): string => {
      const path: string[] = [node.title];
      let current = node;

      while (current.parentMenuId && current.parentMenuId !== '0') {
        const parent = allNodes.find((n) => n.menuId.toString() === current.parentMenuId);
        if (parent) {
          path.unshift(parent.title);
          current = parent;
        } else {
          break;
        }
      }

      return path.join('/');
    };

    return selectedNodes.map(getNodePath);
  };

  // 全选
  const handleSelectAll = () => {
    const allNodes = getAllPermissionNodes(treeData);
    const allKeys = allNodes.map((node) => String(node.key));
    setSelectedKeys(allKeys);
  };

  // 清空
  const handleClear = () => {
    setSelectedKeys([]);
  };

  // 树节点选择变化
  const handleTreeSelect = (checkedKeys: any, info: any) => {
    // Tree组件的onCheck回调返回的是数组格式，确保所有key都是字符串
    const stringKeys = Array.isArray(checkedKeys) ? checkedKeys.map(String) : [];
    setSelectedKeys(stringKeys);
  };

  // 树节点展开变化
  const handleTreeExpand = (expandedKeys: string[]) => {
    setExpandedKeys(expandedKeys);
  };

  // 提交
  const handleSubmit = () => {
    onSubmit(selectedKeys);
    onCancel(false);
  };

  // 关闭
  const handleClose = () => {
    onCancel(false);
  };

  // 更新初始选中值 - 只在组件打开时设置初始值
  // useEffect(() => {
  //   if (open) {
  //     setSelectedKeys(initialSelected);
  //   }
  // }, [open, initialSelected]);

  const selectedPaths = getSelectedPaths();
  return (
    <Drawer
      open={open}
      title={translate(I18N_COMON_PAGENAME.SYSTEM_MANAGE, 'permissionConfig')}
      onClose={handleClose}
      width={800}
      extra={
        <Space>
          <Button onClick={handleClose}>{translate(I18N_COMON_PAGENAME.COMMON, 'cancel')}</Button>
          <Button type="primary" onClick={handleSubmit}>
            {translate(I18N_COMON_PAGENAME.COMMON, 'confirm')}
          </Button>
        </Space>
      }
    >
      <div className={styles.drawerBody}>
        {/* 左侧权限树 */}
        <div className={styles.leftPane}>
          <div className={styles.searchBar}>
            <Search
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              className={styles.searchInput}
            />
            <div className={styles.between}>
              <Text type="secondary">
                {translate(I18N_COMON_PAGENAME.SYSTEM_MANAGE, 'totalItems', {
                  count: getAllPermissionNodes(treeData).length,
                })}
              </Text>
              <Button type="link" size="small" onClick={handleSelectAll}>
                {translate(I18N_COMON_PAGENAME.COMMON, 'selectAll')}
              </Button>
            </div>
          </div>

          <Tree
            checkable
            checkedKeys={selectedKeys}
            expandedKeys={expandedKeys}
            onCheck={handleTreeSelect}
            onExpand={handleTreeExpand}
            treeData={filteredTreeData}
            showLine
            showIcon={false}
            switcherIcon={<DownOutlined />}
            className={styles.tree}
          />
        </div>

        {/* 右侧已选权限 */}
        <div className={styles.rightPane}>
          <div className={styles.searchBar}>
            <div className={styles.between}>
              <Text type="secondary">
                {translate(I18N_COMON_PAGENAME.SYSTEM_MANAGE, 'selectItems', { count: selectedKeys.length })}
              </Text>
              <Button type="link" size="small" onClick={handleClear}>
                {translate(I18N_COMON_PAGENAME.COMMON, 'clear')}
              </Button>
            </div>
          </div>

          <List
            size="small"
            dataSource={selectedPaths}
            renderItem={(item) => (
              <List.Item className={styles.listItem}>
                <Text>{item}</Text>
              </List.Item>
            )}
            className={styles.list}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default memo(MenuAuthCom);
