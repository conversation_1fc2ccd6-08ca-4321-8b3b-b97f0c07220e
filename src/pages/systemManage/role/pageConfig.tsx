import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import services from '@/services';
import { getRequestData } from '@/utils/urlUtil';

// 角色状态枚举
export const roleStatusEunm = [
  { key: 'Y', label: 'enable' },
  { key: 'N', label: 'unEnable' },
];

// 挂载状态枚举
export const mountstatusEunm = [
  { key: 'Y', label: 'mounted' },
  { key: 'N', label: 'unmounted' },
];

export const pageConfig = {
  // 国际化前缀
  prefix: I18N_COMON_PAGENAME.SYSTEM_MANAGE,
  urlObj: {
    list: urlConstants.RULES_AUTH.LIST,
    edit: urlConstants.RULES_AUTH.EDIT,
    create: urlConstants.RULES_AUTH.CREATE,
    // editMount: urlConstants.RULES_AUTH.EDIT_MOUNT,
    getRequestData,
    customFunc: { editMount: services.common.getEditPostBiz },
  },
  // 查询条件字段
  searchSource: [
    {
      value: 'roleName',
      label: 'roleName',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'roleStatus',
      label: 'roleStatus',
      type: COMPONENT_TYPE.SELECT,
      data: roleStatusEunm.map((item) => ({
        key: item.key,
        value: item.label,
      })),
    },
    {
      value: 'mountStatus',
      label: 'mountStatus',
      type: COMPONENT_TYPE.SELECT,
      data: mountstatusEunm.map((item) => ({
        key: item.key,
        value: item.label,
      })),
    },
  ],
  resetValue: {
    roleName: '',
    roleStatus: '',
    mountStatus: '',
  },
  // 页面标题
  cardTitle: 'role',
  // 列表字段
  columns: [
    {
      title: 'roleId',
      dataIndex: 'roleId',
      key: 'roleId',
      width: 60,
    },
    {
      title: 'roleName',
      dataIndex: 'roleName',
      key: 'roleName',
      width: 200,
    },
    {
      key: 'roleStatus',
      dataIndex: 'roleStatus',
      title: 'roleStatus',
      width: 80,
      valueType: RENDER_TYPE.MockDictionary,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
    {
      key: 'mountStatus',
      dataIndex: 'mountStatus',
      title: 'mountStatus',
      width: 100,
      valueType: RENDER_TYPE.MockDictionary,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
  ],
  // 新增按钮权限
  formActionPermissionObj: {},
};
export const userPageConfig = {
  // 国际化前缀
  prefix: I18N_COMON_PAGENAME.SYSTEM_MANAGE,
  urlObj: {
    list: urlConstants.USER_MANAGEMENT.LIST,
  },
  // 查询条件字段
  userSearchSource: [
    {
      value: 'userId',
      label: 'userId',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'userName',
      label: 'userName',
      type: COMPONENT_TYPE.INPUT,
    },
  ],
  userResetValue: { userId: '', userName: '' },
  // 页面标题
  userCardTitle: 'role',
  // 列表字段
  userColumns: [
    {
      title: 'userId',
      dataIndex: 'userId',
      key: 'userId',
      width: 120,
    },
    {
      title: 'userName',
      dataIndex: 'userName',
      key: 'userName',
      width: 220,
      render: (text) => <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>,
    },
    {
      title: 'orgName',
      dataIndex: 'orgName',
      key: 'orgName',
      width: 120,
      data: DICT_CONSTANTS.LABEL_TYPE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'LABEL_TYPE',
    },
    {
      title: 'role',
      dataIndex: 'role',
      key: 'role',
      width: 120,
      data: DICT_CONSTANTS.LABEL_SOURCE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'LABEL_SOURCE',
    },
    {
      title: 'position',
      dataIndex: 'position',
      key: 'position',
      width: 150,
      data: DICT_CONSTANTS.LABEL_ATTRIBUTE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'LABEL_ATTRIBUTE',
    },
    {
      width: 100,
      key: 'gender',
      title: 'gender',
      dataIndex: 'gender',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      width: 100,
      key: 'language',
      title: 'language',
      dataIndex: 'language',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      width: 100,
      key: 'level',
      title: 'level',
      dataIndex: 'level',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
  ],
  // 新增按钮权限
  formActionPermissionObj: {},
};
