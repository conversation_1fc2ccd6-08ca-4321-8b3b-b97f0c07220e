import PageTemplate from '@/components/templates/PageTemplate';
import { I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import { FC, memo, useState } from 'react';
import MenuAuthCom from './components/MenuAuthCom';
import { pageConfig, userPageConfig } from './pageConfig';
import useFormConfig from './useFormConfig';

const Role: FC = () => {
  // hook变量
  const { translate } = useIntlCustom();
  const [detailType, setDetailType] = useState<string>('');
  const [detailData, setDetailData] = useState<any>({});
  const { infoFormConfig } = useFormConfig(detailType);
  const { prefix, formActionPermissionObj, searchSource, columns, resetValue, urlObj } = pageConfig;
  const { userSearchSource, userColumns, userCardTitle, userResetValue, urlObj: userUrlObj } = userPageConfig;
  const [open, setOpen] = useState(false);

  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  const userSearchConfig = {
    searchSource: userSearchSource,
    searchValue: { ...userResetValue },
    resetValue: userResetValue,
  };

  const tableConfig = {
    columns,
    rowKey: 'roleId',
    optionList: [
      { type: OPERATE_TYPE.copy },
      { type: OPERATE_TYPE.edit },
      {
        type: OPERATE_TYPE.menuAuth,
        onCustomize: (val) => {
          setOpen(val);
        },
      },
      { type: OPERATE_TYPE.userDetail },
      { type: OPERATE_TYPE.delete },
    ],
    showPagination: true,
  };

  const formConfig = {
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
    customChildren: {
      [OPERATE_TYPE.userDetail]: (
        <>
          <div style={{ paddingBottom: 12 }}>{translate(I18N_COMON_PAGENAME.SYSTEM_MANAGE, 'userDetailDesc')}</div>
          <PageTemplate
            searchConfig={userSearchConfig}
            tableConfig={{ columns: userColumns, rowKey: 'roleId', optionList: [] }}
            urlObj={userUrlObj}
            cardTitle={userCardTitle}
            intlPrefix={prefix}
            formActionConfig={{ showCreate: false }}
            isShowRouterBar={false}
          />
        </>
      ),
    },
  };

  const handleAction = (type, data) => {
    setDetailType(type);
    setDetailData(data);
  };

  const handleSubmit = () => {};

  return (
    <>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        cardTitle="roleManage"
        intlPrefix={prefix}
        onAction={handleAction}
      />
      {open && <MenuAuthCom open={open} onSubmit={handleSubmit} onCancel={() => setOpen(false)} />}
    </>
  );
};
export default memo(Role);
