// 參數/額度/額度節點
import { GradientButton, LayoutTemplate } from '@/components';
import { DEFAULT_PAGINATION, I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import useDictStore from '@/hooks/useDictStore';
import useIntlCustom from '@/hooks/useIntlCustom';
import commonServices from '@/services/common';
import { Form, Modal, Space, type TablePaginationConfig } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { FC, useEffect, useState } from 'react';
import useSearch from './hooks/useSearch';
import useTable from './hooks/useTable';
import { dictEnum, pageConfig } from './pageConfig';

const { prefix, cardTitle } = pageConfig;

const LimitNode: FC = () => {
  // hooks变量
  const { translate } = useIntlCustom();
  const [listData, setListData] = useState<object[]>([]);
  const [detailType, setDetailType] = useState<string>(OPERATE_TYPE.list);

  const [loading, setLoading] = useState<boolean>(false);
  const [paginationConfig, setPaginationConfig] = useState<TablePaginationConfig | false>(false);
  useDictStore(dictEnum);

  // 副作用
  useEffect(() => {
    getTableData();
  }, []);

  // 逻辑处理
  const getTableData = async (searchValue = { keyValue: '', value: '' }, pagination = { ...DEFAULT_PAGINATION }) => {
    // setLoading(true);
    try {
      const param = {
        url: urlConstants.ADUIT_MANAGE.LIST,
        pagination,
        searchValue: { [searchValue.keyValue]: searchValue?.value },
      };
      const res = await commonServices.getTableListData(param);
      const { data, total } = res;
      Array.isArray(data) && setListData(data);
      total &&
        setPaginationConfig({
          showSizeChanger: true,
          showQuickJumper: true,
          total,
        });
    } catch (error) {
      setListData([]);
      setLoading(false);
    }
  };

  // 操作列事件
  const handleAction = (type: string, row: any) => {
    setDetailType(type);
  };

  // search组件hooks
  const { searchValue, searchChildren } = useSearch({
    getTableData,
    handleAction,
  });

  // table组件hooks
  const { tableChildren } = useTable({
    listData,
    loading,
    searchValue,
    paginationConfig,
    getTableData,
    handleAction,
  });

  // 渲染按钮
  const renderFormAction = () => {
    return (
      <Space>
        <GradientButton type="primary" size="middle" onClick={() => handleSubmit('reject')}>
          {translate(I18N_COMON_PAGENAME.COMMON, 'reject')}
        </GradientButton>
        <GradientButton type="primary" size="middle" onClick={() => handleSubmit('pass')}>
          {translate(I18N_COMON_PAGENAME.COMMON, 'pass')}
        </GradientButton>
      </Space>
    );
  };

  // 提交
  const handleSubmit = async (subType: string) => {
    Modal.confirm({
      title: subType === 'pass' ? '审批通过' : '审批拒绝',
      content: (
        <Form
          name="basic"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          style={{ maxWidth: 240 }}
          initialValues={{ remember: true }}
          // onFinish={onFinish}
          // onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item label="审批意见" name="aduitDesc" rules={[{ required: true }]}>
            <TextArea />
          </Form.Item>
        </Form>
      ),
    });
  };

  return (
    <>
      <LayoutTemplate
        searchChildren={searchChildren()}
        tableChildren={tableChildren()}
        formChildren={null}
        type={detailType}
        intlPrefix={prefix}
        cardTitle={cardTitle}
        cardExtra={renderFormAction()}
      />
    </>
  );
};

export default LimitNode;
