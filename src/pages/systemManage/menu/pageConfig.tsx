import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.SYSTEM_MANAGE,
  cardTitle: 'menuManage',
  // 页面接口请求
  urlObj: {
    list: urlConstants.ADUIT_MANAGE.LIST,
    create: urlConstants.ADUIT_MANAGE.CREATE,
    edit: urlConstants.ADUIT_MANAGE.EDIT,
    // delete: urlConstants.ADUIT_MANAGE.DELETE,

    getRequestData,
  },
  resetValue: { menuName: '', menuStatus: null },
  // 搜索
  searchSource: [
    {
      value: 'menuName',
      label: 'menuName',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'menuStatus',
      label: 'menuStatus',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.PARAM_STATUS,
      // prefix: I18N_COMON_PAGENAME.COMMON,
    },
  ],

  // 列表
  columns: [
    { key: 'menuName', title: 'menuName', dataIndex: 'menuName', width: 280 },
    { key: 'orderSeq', title: 'sort', dataIndex: 'orderSeq', width: 80 },
    {
      key: 'menuStatus',
      title: 'menuStatus',
      dataIndex: 'menuStatus',
      width: 80,
      // render: (_, { menuStatus }) => (menuStatus === 'Y' ? translate(prefix, 'open') : translate(prefix, 'close')),
    },
    { key: 'permissionId', title: 'permissionId', width: 220, dataIndex: 'permissionId' },
    { key: 'iconId', title: 'iconId', width: 110, dataIndex: 'iconId' },
    {
      key: 'createTime',
      title: 'createTs',
      dataIndex: 'createTime',
      width: 180,
      align: 'center',
      valueType: RENDER_TYPE.DateTime,
    },
    {
      key: 'updateTime',
      title: 'updateTs',
      dataIndex: 'updateTime',
      width: 180,
      align: 'center',
      valueType: RENDER_TYPE.DateTime,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // nodeRule: DICT_CONSTANTS.DICT_ENUM_MAP.nodeRule
};
