import { FormTemplate } from '@/components';
import LayoutTemplate from '@/components/templates/LayoutTemplate';
import { NOTIFICATION_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import { getTreeList, updateData } from '@/services/menu';
import * as iconList from '@ant-design/icons';
import { Button, Modal, Row, TablePaginationConfig, Tabs, TabsProps } from 'antd';
import _ from 'lodash';
import React, { Suspense, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import usePageFormAction from './hooks/usePageFormAction';
import useSearch from './hooks/useSearch';
import useTable from './hooks/useTable';
import {
  dataOutlined,
  directionOutlined,
  editorOutlined,
  logoOutlined,
  otherOutlined,
  suggestionOutlined,
} from './iconData';
import { pageConfig } from './pageConfig';
import useFormConfig from './useFormConfig';
import { tabTypeEunm } from './util';

const MenuPage: React.FC = () => {
  const { prefix, cardTitle, resetValue } = pageConfig;
  const [type, setType] = useState(OPERATE_TYPE.list);
  // 上级菜单下拉数据
  const [optionData, setOptionData] = useState<any>([]);
  const { InfoFormConfig } = useFormConfig(type, optionData);
  const [loading, setLoading] = useState<boolean>(false);
  const [paginationConfig, setPaginationConfig] = useState<TablePaginationConfig | false>(false);
  // hook变量
  const { translate, openNotificationTip } = useIntlCustom();
  const [listData, setListData] = useState([]);
  const [searchData, setSearchData] = useState(resetValue);
  const [detailData, setDetailData] = useState<any>({});
  const formRef = useRef<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  // 选中icon值
  const [iconValue, setIconValue] = useState(null);
  // 表单数据
  const [formData, setFormData] = useState<any>(null);
  // 未过滤树状菜单数据
  const [tableData, setTableData] = useState<any[]>([]);

  // 内部变量 - 使用useMemo优化
  const iconMap = useMemo(
    () => ({
      direction: directionOutlined,
      suggestion: suggestionOutlined,
      editor: editorOutlined,
      data: dataOutlined,
      logo: logoOutlined,
      other: otherOutlined,
    }),
    [],
  );

  // 副作用
  useEffect(() => {
    getTableData(resetValue);
    handleSearch(resetValue);
  }, []);

  // 逻辑处理 - 使用useCallback优化
  const getTableData = useCallback(async (val) => {
    try {
      setLoading(true);
      const res: any = await getTreeList(val);
      setTableData(res);
      if (res) {
        setListData(res);
      }
    } catch (e) {
      setListData([]);
      console.log(e);
    } finally {
      setLoading(false);
    }
  }, []);

  // 上级菜单数据处理 - 使用useCallback优化
  const traverseTree = useCallback((data) => {
    // 筛选需要保留并转换的属性，同时剔除不需要的属性
    const transformed = {
      title: data.menuName,
      value: data.menuId,
      // 仅保留children（如果存在），其他属性通过omit剔除
      ..._.omit(data, [
        'menuId',
        'menuName',
        'createTime',
        'createUser',
        'updateTime',
        'updateUser',
        'iconId',
        'type',
        'menuStatus',
        'permissionId',
        'parentMenuId',
        'orderSeq',
        'level',
        'key',
        'menuType',
      ]),
    };

    // 递归处理子节点（如果有）
    if (transformed.children && transformed.children.length > 0) {
      transformed.children = _.map(transformed.children, traverseTree);
    }

    return transformed;
  }, []);

  // 列表过滤数据处理 - 使用useCallback优化
  const filterTreeData = useCallback((data, conditions, type) => {
    const key = Object.keys(conditions)[0];
    // 如果没有任何过滤条件，返回原数据
    if (!conditions[key]) return data;

    // 菜单状态和菜单名称根据type分别进行精准匹配和模糊过滤
    const result: any[] = [];
    data.forEach((node: any) => {
      const newNode = { ...node };
      // 如果节点有子节点，则递归搜索子节点
      if (newNode.children) {
        newNode.children = filterTreeData(newNode.children, conditions, type);
        // 检查子节点中是否有匹配的
        const hasMatchingChild = newNode.children.some((child) =>
          type === 'eunm'
            ? child[key] === conditions[key] || (child.children && child.children.length > 0)
            : child[key].toLowerCase().includes(conditions[key].toLowerCase()) ||
              (child.children && child.children.length > 0),
        );
        // 如果当前节点本身匹配或者其子节点中有匹配的，则保留该节点
        if (
          type === 'eunm'
            ? newNode[key] === conditions[key] || hasMatchingChild
            : newNode[key].toLowerCase().includes(conditions[key].toLowerCase()) || hasMatchingChild
        ) {
          // 移除空的子节点数组（如果所有子节点都不匹配并被移除）
          if (newNode.children && newNode.children.length === 0) {
            delete newNode.children;
          }
          // 添加节点到结果中
          result.push(newNode);
        }
      } else {
        // 没有子节点的叶子节点：仅当节点本身匹配时才保留
        if (
          type === 'eunm'
            ? newNode[key] === conditions[key]
            : newNode[key].toLowerCase().includes(conditions[key].toLowerCase())
        ) {
          result.push(newNode);
        }
      }
    });
    return result;
  }, []);

  const renderTab = useCallback(
    (key) => {
      const iconArray = iconMap[key];
      return (
        <>
          {iconArray.map((item) => {
            return (
              <Button key={item} onClick={() => handleIconChange(item)} type="text">
                {iconList[item].render()}
              </Button>
            );
          })}
        </>
      );
    },
    [iconMap],
  );

  const getTabData = useCallback(() => {
    const res: any = [];
    tabTypeEunm.map((item) => {
      const obj = {
        key: item,
        label: translate(prefix, item),
        children: renderTab(item),
      };
      res.push(obj);
    });
    return res;
  }, [translate, renderTab]);

  // 使用useMemo缓存tabsItems
  const tabsItems: TabsProps['items'] = useMemo(() => getTabData(), [getTabData]);

  const formChildren = useCallback(() => {
    return (
      <>
        <FormTemplate
          ref={formRef}
          config={InfoFormConfig}
          initialData={detailData}
          loading={false}
          intlPrefix={prefix}
          canEdit={type !== OPERATE_TYPE.detail}
          showMaintenance={type === OPERATE_TYPE.detail}
          onSearch={handleIconSearch}
        />
        <Modal
          title={translate(prefix, 'iconSelect')}
          cancelText={translate('common', 'cancel')}
          okText={translate('common', 'confirm')}
          width={1000}
          open={isModalOpen}
          onOk={handleModalSubmit}
          onCancel={() => setIsModalOpen(false)}
        >
          <Tabs type="card" items={tabsItems} />
          <Row style={{ paddingTop: '30px' }}>
            {translate(prefix, 'messTip')}
            {iconValue}
          </Row>
        </Modal>
      </>
    );
  }, [InfoFormConfig, detailData, type, translate, isModalOpen, tabsItems, iconValue]);

  // 事件处理 - 使用useCallback优化
  const handleSearch = useCallback(
    (e) => {
      setSearchData(e);
      // 前端编写数据过滤逻辑
      const { menuName, menuStatus } = e;
      let data = JSON.parse(JSON.stringify(tableData));
      const nameCondition = { menuName: menuName };
      const statusCondition = { menuStatus: menuStatus };
      const firstRes = filterTreeData(data, nameCondition, 'string');
      const res = filterTreeData(firstRes, statusCondition, 'eunm');
      // 处理菜单状态/菜单名称过滤逻辑
      setListData(res);
    },
    [tableData, filterTreeData],
  );

  const handleCardBack = useCallback(() => {
    setType(OPERATE_TYPE.list);
    setDrawerOpen(false);
  }, []);

  const handleCardSubmit = useCallback(async () => {
    try {
      const data = await formRef?.current?.onSubmit();
      const { formData } = data;
      const param = { body: { ...formData, menuId: detailData.menuId } };
      if (data) {
        const res: any = await updateData(param);
        if (res) {
          getTableData(searchData);
          handleCardBack();
          openNotificationTip('common', NOTIFICATION_TYPE.SUCCESS, 'editSuccess', 1);
        }
      }
    } catch (e) {
      openNotificationTip('common', NOTIFICATION_TYPE.ERROR, 'checkMsg');
    }
  }, []);

  const handleIconSearch = (key, form) => {
    setFormData(form);
    setIsModalOpen(true);
  };

  const handleModalSubmit = useCallback(() => {
    if (iconValue) {
      formData?.setFieldsValue({ iconId: iconValue });
    }
    setIsModalOpen(false);
  }, [iconValue]);

  const handleIconChange = useCallback((iconName) => {
    setIconValue(iconName);
  }, []);

  const handleAction = useCallback((type, row) => {
    setDrawerOpen(true);
    // 当执行编辑操作时，处理封装上级菜单树形数据
    if (type === OPERATE_TYPE.edit) {
      const data = JSON.parse(JSON.stringify(tableData));
      data.forEach((rootNode) => traverseTree(rootNode));
      setOptionData(data);
    }
    setType(type);
    setDetailData(row);
  }, []);

  // search组件hooks
  const { searchValue, searchChildren } = useSearch({
    getTableData,
    handleAction,
  });

  // table组件hooks
  const { tableChildren } = useTable({
    listData,
    loading,
    searchValue,
    paginationConfig,
    getTableData,
    handleAction,
  });

  // formAction组件hooks
  const { renderFormAction, handleDrawerSubmit } = usePageFormAction({
    formRef,
    type,
    getTableData,
    handleCardBack,
    handleAction,
  });

  return (
    <Suspense>
      <LayoutTemplate
        searchChildren={searchChildren()}
        tableChildren={tableChildren()}
        formChildren={formChildren()}
        drawerOpen={drawerOpen}
        type={type}
        intlPrefix={prefix}
        cardTitle={cardTitle}
        cardExtra={renderFormAction()}
        handleClose={handleCardBack}
        handleSubmit={handleCardSubmit}
      />
    </Suspense>
  );
};

export default MenuPage;
