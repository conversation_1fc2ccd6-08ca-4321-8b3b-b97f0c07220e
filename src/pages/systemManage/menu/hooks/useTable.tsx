// table组件hooks
import { TreeTable } from '@/components';
import { OPERATE_TYPE } from '@/constants/publicConstant';
import { IPaniation } from '@/types/ICommon';
import { pageConfig } from '../pageConfig';

const useTable = ({ searchValue, listData, loading, paginationConfig, getTableData, handleAction }) => {
  const { prefix, columns } = pageConfig;
  // 页码改变事件
  const handleTableChange = (pagination: IPaniation): void => {
    const { current: currentPage, pageSize } = pagination;
    getTableData(searchValue, { currentPage, pageSize });
  };

  // 渲染组件
  const tableChildren = () => {
    return (
      // <CommonTable
      //   rowKey="paramIndex"
      //   dataSource={listData}
      //   columns={columns}
      //   loading={loading}
      //   optionList={[{ type: OPERATE_TYPE.detail }, { type: OPERATE_TYPE.edit }]}
      //   paginationConfig={paginationConfig}
      //   intlPrefix={prefix}
      //   onAction={handleAction}
      //   onChange={handleTableChange}
      // />
      <TreeTable
        rowKey="menuId"
        intlPrefix={prefix}
        dataSource={listData}
        loading={loading}
        optionList={[{ type: OPERATE_TYPE.detail }, { type: OPERATE_TYPE.edit }]}
        columns={columns}
        onAction={handleAction}
      />
    );
  };

  return { tableChildren };
};

export default useTable;
