import { COMPONENT_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import { IFormConfig } from '@/types/IForm';
import { useCallback } from 'react';
import { statusEunm } from './util';

const useFormConfig = (type: string, optionData: any) => {
  const filterTreeNode = useCallback((inputValue, treeNode) => treeNode.title.indexOf(inputValue) > -1, []);
  // 表单字段
  const InfoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'menuInfo',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          name: 'parentMenuId',
          label: 'supMenu',
          type: COMPONENT_TYPE.TREE_SELECT,
          data: optionData,
          showSearch: true,
          allowClear: true,
          placement: 'bottomLeft',
          popupMatchSelectWidth: false,
          disabled: type === OPERATE_TYPE.detail,
          filterTreeNode: filterTreeNode,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'menuName',
          label: 'menuName',
          rules: [{ required: true, max: 16 }],
          maxLength: 16,
          disabled: type === OPERATE_TYPE.detail,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'menuStatus',
          label: 'menuStatus',
          rules: [{ required: true }],
          data: statusEunm.map((item) => ({
            value: item.label,
            key: item.key,
          })),
          showKey: false,
          disabled: type === OPERATE_TYPE.detail,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'orderSeq',
          label: 'displaySorting',
          rules: [{ required: true }],
          maxLength: 3,
          disabled: type === OPERATE_TYPE.detail,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'menuRouteUrl',
          label: 'routeUrl',
          // rules: [{ required: !disType, max: 150 }],
          // disabled: disType,
          rules: [{ max: 200 }],
          maxLength: 200,
          disabled: true,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'permissionId',
          label: 'permissionId',
          rules: [{ required: true }],
          maxLength: 64,
          disabled: true,
        },
        {
          type: COMPONENT_TYPE.SEARCH,
          name: 'iconId',
          label: 'iconId',
          readOnly: true,
          disabled: type === OPERATE_TYPE.detail,
          enterButton: 'addIcon',
        },
      ],
    },
  ];

  return {
    InfoFormConfig,
  };
};

export default useFormConfig;
