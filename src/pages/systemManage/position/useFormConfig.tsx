import { COMPONENT_TYPE, FORMITEM_TYPE, I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import { IFormConfig } from '@/types/IForm';

const useFormConfig = (type: string, detailData) => {
  // 表单字段
  const InfoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'positionId',
          label: 'positionId',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'positionName',
          label: 'positionName',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'parentName',
          label: 'parentName',
          rules: [{ required: true }],
        },
      ],
    },
  ];

  return {
    InfoFormConfig,
  };
};

export default useFormConfig;
