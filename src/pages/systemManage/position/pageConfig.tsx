import { COMPONENT_TYPE, I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

// const getRequestData = (postData, type: string, rowData): object => {
//   switch (type) {
//     case OPERATE_TYPE.edit:
//     case OPERATE_TYPE.create:
//       return {
//         ...postData.formData,
//       };
//     default:
//       return { ...postData };
//   }
// };

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.SYSTEM_MANAGE,
  cardTitle: 'positionManage',
  // 页面接口请求
  urlObj: {
    list: urlConstants.POSITION_MANAGEMENT.LIST,
    create: urlConstants.POSITION_MANAGEMENT.CREATE,
    edit: urlConstants.POSITION_MANAGEMENT.EDIT,
    delete: urlConstants.POSITION_MANAGEMENT.DELETE,
    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'positionId',
      label: 'positionId',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'positionName',
      label: 'positionName',
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 80,
    },
    {
      title: 'positionId',
      dataIndex: 'positionId',
      key: 'positionId',
      width: 100,
    },
    {
      title: 'positionName',
      dataIndex: 'positionName',
      key: 'positionName',
      width: 150,
    },
    {
      title: 'parentName',
      dataIndex: 'parentName',
      key: 'parentName',
      width: 150,
    },
    {
      title: 'description',
      dataIndex: 'description',
      key: 'description',
      width: 250,
      render: (text) => <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {};
