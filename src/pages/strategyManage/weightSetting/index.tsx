import { OPERATE_TYPE } from '@/constants/publicConstant';
import { Modal, Select } from 'antd';
import React, { Suspense, useState } from 'react';
import { dictEnum, pageConfig } from './pageConfig';
import useFormConfig from './useFormConfig';

const PageTemplate = React.lazy(() => import('@/components/templates/PageTemplate'));

const WeightSetting: React.FC = () => {
  const { urlObj, prefix, searchSource, columns, cardTitle } = pageConfig;
  const resetValue = { weightCoefficient: '', nodeAttriType: null };
  const [detailData, setDetailData] = useState<any>({});
  const [type, setType] = useState<string>(OPERATE_TYPE.list);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedFactor, setSelectedFactor] = useState<string>('');
  const { infoFormConfig, editableCoefficientFormRef, editablePerformanceFormRef, setEditTableData } =
    useFormConfig(type);

  // 权重因子选项
  const factorOptions = [
    { label: '级别', value: 'level' },
    { label: '业绩系数', value: 'performance' },
  ];

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 表格配置
  const tableConfig = {
    columns,
    rowKey: 'paramIndex',
    showPagination: true,
    optionList: [OPERATE_TYPE.detail, OPERATE_TYPE.edit],
  };

  // 表单配置
  const formConfig = {
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
    props: {
      editTableRefList: {
        nodeProgramList: editableCoefficientFormRef,
        performanceList: editablePerformanceFormRef,
      },
    },
  };

  // 列表按钮操作
  const handleAction = (editType: string, row: any) => {
    setDetailData({ ...row });
    setType(editType);
    setEditTableData([]);
  };

  // 处理新增按钮点击
  const handleAddClick = () => {
    setModalVisible(true);
    setSelectedFactor('');
  };

  // 处理弹窗确定
  const handleModalOk = () => {
    if (selectedFactor) {
      // 这里可以添加新增逻辑
      console.log('选择的权重因子:', selectedFactor);
      setModalVisible(false);
      setSelectedFactor('');
    }
  };

  // 处理弹窗取消
  const handleModalCancel = () => {
    setModalVisible(false);
    setSelectedFactor('');
  };

  // const formExtra = () => {
  //   return (
  //     <Button type="primary" onClick={handleAddClick}>新增</Button>
  //   );
  // };

  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        formExtra={null}
        onAction={handleAction}
      />

      <Modal title="新增权重因子" open={modalVisible} onOk={handleModalOk} onCancel={handleModalCancel}>
        <div style={{ padding: '20px 0' }}>
          <div style={{ marginBottom: 16 }}>
            <span style={{ marginRight: 8 }}>权重因子:</span>
            <Select
              style={{ width: 400 }}
              placeholder="请选择权重因子"
              value={selectedFactor}
              onChange={setSelectedFactor}
              options={factorOptions}
            />
          </div>
        </div>
      </Modal>
    </Suspense>
  );
};
export default React.memo(WeightSetting);
