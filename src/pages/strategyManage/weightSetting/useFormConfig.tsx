import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, FORMITEM_TYPE, I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';
import store from '@/store';
import { IFormConfig } from '@/types/IForm';
import { EditableFormInstance } from '@ant-design/pro-components';
import { useRef, useState } from 'react';

const useFormConfig = (type: string) => {
  const [dictState] = store.useModel('dict');
  const [editType, setEditType] = useState<string>(type);
  const [editTableData, setEditTableData] = useState<any[]>([]);
  const editableCoefficientFormRef = useRef<EditableFormInstance>();
  const editablePerformanceFormRef = useRef<EditableFormInstance>();
  // 可编辑表格操作列
  const editCoefficientColumns = [
    {
      title: 'factorLevel',
      dataIndex: 'factorLevel',
      valueType: 'select',
      showKey: true,
      valueEnum: () => {
        const result = {};
        dictState.dictMap?.nodeAttriId?.map((item) => {
          const { key, value } = item;
          result[key] = { text: value };
        });
        return result;
      },
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: 'weightRatio',
      dataIndex: 'weightRatio',
    },
  ];
  const editPerformanceColumns = [
    {
      width: 120,
      key: 'performanceMin',
      title: 'performanceMin',
      dataIndex: 'performanceMin',
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: 'performanceMax',
      dataIndex: 'performanceMax',
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: 'weightRatio',
      dataIndex: 'weightRatio',
    },
  ];
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'weightStrategyId',
          label: 'weightStrategyId',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
          data: DICT_CONSTANTS.STATION,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'weightStrategyName',
          label: 'weightStrategyName',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'description',
          label: 'description',
          rules: [{ required: true }],
        },
      ],
    },
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'coefficient',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'weightingFactor',
          label: 'weightingFactor',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
      ],
    },
    {
      type: FORMITEM_TYPE.EditTable,
      rowKey: 'id',
      columns: editCoefficientColumns,
      dataSource: editTableData,
      canEdit: true,
      editableCoefficientFormRef,
      optionCount: 1,
      getOptionList: (row) => [
        {
          optionType: OPERATE_TYPE.edit,
          disabled: editType === OPERATE_TYPE.detail,
        },
        {
          optionType: OPERATE_TYPE.delete,
          disabled: editType === OPERATE_TYPE.detail,
        },
      ],
      onCreate: (index) => ({ id: Date.now() + 1, isAdd: true }),
    },
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'performance',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'weightingFactor',
          label: 'weightingFactor',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
      ],
    },
    {
      type: FORMITEM_TYPE.EditTable,
      rowKey: 'id',
      columns: editPerformanceColumns,
      dataSource: editTableData,
      canEdit: true,
      editablePerformanceFormRef,
      optionCount: 1,
      getOptionList: (row) => [
        {
          optionType: OPERATE_TYPE.edit,
          disabled: editType === OPERATE_TYPE.detail,
        },
        {
          optionType: OPERATE_TYPE.delete,
          disabled: editType === OPERATE_TYPE.detail,
        },
      ],
      onCreate: (index) => ({ id: Date.now() + 1, isAdd: true }),
    },
  ];

  return {
    infoFormConfig,
    editableCoefficientFormRef,
    editablePerformanceFormRef,
    setEditTableData,
  };
};

export default useFormConfig;
