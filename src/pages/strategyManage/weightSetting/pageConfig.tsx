import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.STRATEGY,
  cardTitle: 'strategySetting',
  // 页面接口请求
  urlObj: {
    list: urlConstants.STATION_MANAGEMENT.LIST,
    create: urlConstants.STATION_MANAGEMENT.CREATE,
    edit: urlConstants.STATION_MANAGEMENT.EDIT,
    delete: urlConstants.STATION_MANAGEMENT.DELETE,

    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'weightStrategyId',
      label: 'weightStrategyId',
      type: COMPONENT_TYPE.INPUT,
      data: DICT_CONSTANTS.STATION,
    },
    {
      value: 'weightStrategyName',
      label: 'weightStrategyName',
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
    {
      title: 'weightStrategyId',
      dataIndex: 'weightStrategyId',
      key: 'weightStrategyId',
    },
    {
      title: 'weightStrategyName',
      dataIndex: 'weightStrategyName',
      key: 'weightStrategyName',
      width: 120,
    },
    {
      title: 'description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      width: 150,
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      width: 160,
      valueType: RENDER_TYPE.DateTime,
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};
