import { COMPONENT_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import DICT_CONSTANTS from '@/constants/dictConstants';
import { IFormConfig } from '@/types/IForm';

const useFormConfig = (type: string) => {
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'stationId',
          label: 'stationId',
          disabled: true,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'position',
          label: 'position',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
          data: DICT_CONSTANTS.STATION,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'weightCoefficient',
          label: 'weightCoefficient',
          rules: [{ required: true }],
        },
      ],
    },
  ];

  return {
    infoFormConfig,
  };
};

export default useFormConfig;
