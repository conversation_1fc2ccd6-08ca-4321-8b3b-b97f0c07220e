import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, FORMITEM_TYPE, I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';
import { IFormConfig } from '@/types/IForm';
import { EditableFormInstance } from '@ant-design/pro-components';
import { useRef, useState } from 'react';

const useFormConfig = (type: string) => {
  const [editType, setEditType] = useState<string>(type);
  const [editTableData, setEditTableData] = useState<any[]>([]);
  const editableFormRef = useRef<EditableFormInstance>();
  // 可编辑表格操作列
  const editColumns = [
    {
      width: 120,
      key: 'priority',
      title: 'priority',
      dataIndex: 'priority',
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: 'fieldName',
      dataIndex: 'fieldName',
      valueType: 'select',
      showKey: true,
      valueEnum: () => {
        const result = {};
        // dictState.dictMap?.nodeAttriId?.map((item) => {
        //   const { key, value } = item;
        //   result[key] = { text: value };
        // });
        return result;
      },
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: 'sortOrder',
      dataIndex: 'sortOrder',
      valueType: 'select',
      showKey: true,
    },
  ];
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'templateName',
          label: 'templateName',
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
          data: DICT_CONSTANTS.STATION,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'positionName',
          label: 'positionName',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'description',
          label: 'description',
          rules: [{ required: true }],
        },
      ],
    },
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'sortSetting',
    },
    {
      type: FORMITEM_TYPE.EditTable,
      rowKey: 'id',
      columns: editColumns,
      dataSource: editTableData,
      canEdit: true,
      editableFormRef,
      optionCount: 1,
      getOptionList: (row) => [
        {
          optionType: OPERATE_TYPE.edit,
          disabled: editType === OPERATE_TYPE.detail,
        },
        {
          optionType: OPERATE_TYPE.delete,
          disabled: editType === OPERATE_TYPE.detail,
        },
      ],
      onCreate: (index) => ({ id: Date.now() + 1, isAdd: true }),
    },
  ];

  return {
    infoFormConfig,
    editableFormRef,
    setEditTableData,
  };
};

export default useFormConfig;
