import { OPERATE_TYPE } from '@/constants/publicConstant';
import React, { Suspense, useState } from 'react';
import { dictEnum, pageConfig } from './pageConfig';
import useFormConfig from './useFormConfig';
const PageTemplate = React.lazy(() => import('@/components/templates/PageTemplate'));

const AuthorizationResPage: React.FC = () => {
  const { urlObj, prefix, searchSource, columns, cardTitle } = pageConfig;
  const resetValue = {
    nodeCode: '',
    nodeNodeName: '',
    nodeType: null,
    status: null,
  };
  const [detailData, setDetailData] = useState<any>({
    callFields: ['caseCode', 'orgCustNbr', 'custName', 'icType', 'custIc'],
  });
  const [type, setType] = useState<string>(OPERATE_TYPE.list);
  const { infoFormConfig } = useFormConfig(type);

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 表格配置
  const tableConfig = {
    columns,
    rowKey: 'paramIndex',
    showPagination: true,
    optionList: [{ type: OPERATE_TYPE.detail }, { type: OPERATE_TYPE.edit }],
  };

  // 表单配置
  const formConfig = {
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
  };

  // 操作按钮配置
  const formActionConfig = {
    // showCreate: false,
  };

  // 列表按钮操作
  const handleAction = (editType: string, row: any) => {
    setDetailData({
      ...row,
      workBenchModel: row?.workBenchModel?.split(','),
      auxiliaryFunctions: row?.auxiliaryFunctions?.split(','),
      callFields: row?.callFields?.split(','),
    });
    setType(editType);
  };

  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        formActionConfig={formActionConfig}
        onAction={handleAction}
      />
    </Suspense>
  );
};
export default React.memo(AuthorizationResPage);
