import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, OPERATE_TYPE, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';

const getRequestData = (postData, type: string, rowData): object => {
  switch (type) {
    case OPERATE_TYPE.edit:
    case OPERATE_TYPE.create:
      return {
        ...postData.formData,
        updateUser: '张俊锋',
        workBenchModel: postData.formData?.workBenchModel?.join(','),
        auxiliaryFunctions: postData.formData?.auxiliaryFunctions?.join(','),
        callFields: postData.formData?.callFields?.join(','),
      };
    default:
      return { ...postData };
  }
};
export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.CALL_PARAM,
  cardTitle: 'integrateManageWorkBench',
  // 页面接口请求
  urlObj: {
    list: urlConstants.WORKBENCH_CONFIG.LIST,
    create: urlConstants.WORKBENCH_CONFIG.CREATE,
    edit: urlConstants.WORKBENCH_CONFIG.EDIT,

    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'workBenchName',
      label: 'workBenchName',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.AUTH_PAGE,
    },
    // {
    //   value: "userName",
    //   label: "userName",
    //   type: COMPONENT_TYPE.INPUT,
    // },
    // {
    //   value: 'status',
    //   label: 'status',
    //   type: COMPONENT_TYPE.SELECT,
    //   data: DICT_CONSTANTS.PARAM_STATUS,
    // },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    // {
    //   title: "userId",
    //   dataIndex: "userId",
    //   key: "userId",
    //   width: 120,
    // },
    // {
    //   width: 120,
    //   title: "userName",
    //   dataIndex: "userName",
    //   key: "userName",
    // },
    {
      title: 'workBenchName',
      dataIndex: 'workBenchName',
      key: 'workBenchName',
      data: DICT_CONSTANTS.AUTH_PAGE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'AUTH_PAGE',
      width: 120,
    },
    {
      width: 280,
      key: 'workBenchModel',
      title: 'workBenchModel',
      dataIndex: 'workBenchModel',
      render: (text) => (
        <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>
          {DICT_CONSTANTS.RIGHT_ACTION.filter((item) => text.split(',').includes(item.key))
            .map((ite) => ite.label)
            ?.join('，')}
        </div>
      ),
    },
    {
      width: 280,
      title: 'auxiliaryFunctions',
      dataIndex: 'auxiliaryFunctions',
      key: 'auxiliaryFunctions',
      render: (text) => (
        <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>
          {DICT_CONSTANTS.LEFT_ACTION.filter((item) => text.split(',').includes(item.key))
            .map((ite) => ite.label)
            ?.join('，')}
        </div>
      ),
    },
    // {
    //   width: 100,
    //   key: 'status',
    //   title: 'status',
    //   dataIndex: 'status',
    //   data: DICT_CONSTANTS.PARAM_STATUS,
    //   valueType: RENDER_TYPE.MockDictionary,
    //   dictType: 'PARAM_STATUS',
    // },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      width: 160,
      valueType: RENDER_TYPE.DateTime,
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      width: 120,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};
