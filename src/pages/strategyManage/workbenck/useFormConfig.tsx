import { COMPONENT_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import DICT_CONSTANTS from '@/constants/dictConstants';
import { IFormConfig } from '@/types/IForm';
import { Cascader } from 'antd';

const useFormConfig = (type: string) => {
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        // {
        //   type: COMPONENT_TYPE.INPUT,
        //   name: "userId",
        //   label: "userId",
        //   rules: [{ required: true }],
        //   disabled: type !== OPERATE_TYPE.create,
        // },
        // {
        //   type: COMPONENT_TYPE.INPUT,
        //   name: "userName",
        //   label: "userName",
        //   rules: [{ required: true }],
        //   disabled: type !== OPERATE_TYPE.create,
        // },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'workBenchName',
          label: 'workBenchName',
          showKey: false,
          rules: [{ required: true }],
          data: DICT_CONSTANTS.AUTH_PAGE,
          disabled: type !== OPERATE_TYPE.create,
          // mode: 'multiple',
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'workBenchModel',
          label: 'workBenchModel',
          showKey: false,
          rules: [{ required: true }],
          data: DICT_CONSTANTS.RIGHT_ACTION,
          mode: 'multiple',
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'auxiliaryFunctions',
          label: 'auxiliaryFunctions',
          showKey: false,
          rules: [{ required: true }],
          data: DICT_CONSTANTS.LEFT_ACTION,
          mode: 'multiple',
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'callFields',
          label: 'callFields',
          showKey: false,
          rules: [{ required: true }],
          data: DICT_CONSTANTS.CENTER_FILES,
          mode: 'multiple',
          defaultValue: ['caseCode', 'orgCustNbr', 'custName', 'icType', 'custIc'],
        },
        // {
        //   type: COMPONENT_TYPE.SELECT,
        //   name: 'status',
        //   label: 'status',
        //   data: DICT_CONSTANTS.PARAM_STATUS,
        //   rules: [{ required: true }],
        //   disabled: type !== OPERATE_TYPE.create,
        // },
      ],
    },
  ];

  return {
    infoFormConfig,
  };
};

export default useFormConfig;
