/**
 * Created by ca<PERSON><PERSON> on 2025/6/6.
 */

import Logo from '@/assets/images/logo2.png';
import { SESSION } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import { EyeInvisibleOutlined, EyeTwoTone, LockOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Checkbox, Form, Input, Modal, notification, Select } from 'antd';
import Cookies from 'js-cookie';
import _ from 'lodash';
import { useCallback, useEffect, useState } from 'react';
import styles from './index.module.css';

const cname = Cookies.get('username') || '';
const crole = Cookies.get('role') || '';
const cstation = Cookies.get('station') || '';
const inputStyle = { width: '320px' };

export default function Login() {
  const { translate } = useIntlCustom();
  const prefix = 'login';

  // 内部变量
  const [loginData, setLoginData] = useState({
    username: cname,
    password: '',
    role: crole,
    station: cstation,
    remember: !!cname,
  });

  const [loading, setLoading] = useState(false);
  const [nextLoading, setNextLoading] = useState(false);
  const [step, setStep] = useState(1); // 1: 用户名密码, 2: 选择角色岗位
  const [roleOptions, setRoleOptions] = useState([]);
  const [stationOptions, setStationOptions] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();

  // 初始化时同步 cookie 到表单
  useEffect(() => {
    form.setFieldsValue({
      username: cname,
      role: crole,
      station: cstation,
      remember: !!cname,
    });
  }, [form]);

  // 事件处理
  const handleValuesChange = useCallback(
    _.debounce((changedValues, allValues) => {
      setLoginData(allValues);
    }, 200),
    [],
  );

  // 第一步：验证用户名密码
  const handleNext = useCallback(() => {
    form
      .validateFields(['username', 'password'])
      .then(async (values) => {
        setNextLoading(true);
        try {
          // 调用后端接口获取角色和岗位信息
          // const result = await postData('/getUserRoleStation', { username: values.username, password: values.password });
          const result = {
            data: {
              roles: [
                { label: 'Admin', value: '1' },
                { label: 'User', value: '2' },
                { label: 'Manager', value: '3' },
              ],
              stations: [
                { label: 'Station A', value: '1' },
                { label: 'Station B', value: '2' },
                { label: 'Station C', value: '3' },
              ],
            },
          };

          if (result?.data) {
            setRoleOptions(result.data.roles || []);
            setStationOptions(result.data.stations || []);
            setModalVisible(true);
            // 设置默认值
            modalForm.setFieldsValue({
              role: crole,
              station: cstation,
            });
          } else {
            notification.error({ message: translate(prefix, 'error.invalid') });
          }
        } catch (e) {
          notification.error({ message: translate(prefix, 'error.invalid') });
        } finally {
          setNextLoading(false);
        }
      })
      .catch((err) => {
        setNextLoading(false);
        console.log('Validation failed', err);
      });
  }, [form, translate, prefix, modalForm]);

  // 第二步：真正的登录
  const handleLogin = useCallback(() => {
    modalForm
      .validateFields()
      .then(async (modalValues) => {
        const formValues = form.getFieldsValue();
        const finalValues = { ...formValues, ...modalValues };

        setLoading(true);
        try {
          // const result = await postData('/login', finalValues);
          const result = { data: '123' };
          const token = result?.data;
          if (token) {
            sessionStorage.setItem(SESSION.token, token);
            sessionStorage.setItem(SESSION.codeIndex, 'user');
            // 记住我逻辑
            if (finalValues.remember) {
              Cookies.set('username', finalValues.username);
              Cookies.set('role', finalValues.role);
              Cookies.set('station', finalValues.station);
            } else {
              Cookies.remove('username');
              Cookies.remove('role');
              Cookies.remove('station');
            }
            window.location.href = '/workBench/collectorBench';
          } else {
            sessionStorage.removeItem(SESSION.token);
            sessionStorage.removeItem(SESSION.codeIndex);
            notification.error({ message: translate(prefix, 'error.invalid') });
          }
        } catch (e) {
          sessionStorage.removeItem(SESSION.token);
          sessionStorage.removeItem(SESSION.codeIndex);
          notification.error({ message: translate(prefix, 'error.invalid') });
        } finally {
          setLoading(false);
        }
      })
      .catch((err) => {
        setLoading(false);
        console.log('Modal validation failed', err);
      });
  }, [modalForm, form, translate, prefix]);

  // 支持回车登录
  const handleEntryPress = useCallback(
    (e) => {
      if (e.key === 'Enter') {
        handleLogin();
      }
    },
    [handleLogin],
  );

  return (
    <div className={styles.login}>
      <div className={styles.left}>
        <div className={styles.logo}>
          <img alt="logo" src={Logo} />
        </div>
        <div className={styles.desc}>
          <h1>{translate(prefix, 'title')}</h1>
          <p>{translate(prefix, 'description')}</p>
        </div>
        <div className={styles.copy}>
          <span>{translate(prefix, 'copyright')}</span>
        </div>
      </div>
      <div className={styles.right}>
        <h2>{translate(prefix, 'title')}</h2>
        <Form form={form} initialValues={loginData} layout="vertical" onValuesChange={handleValuesChange}>
          <Form.Item
            name="username"
            label={translate(prefix, 'username')}
            rules={[{ required: true }, { max: 30, message: translate(prefix, 'validation.username.max') }]}
          >
            <Input
              allowClear
              placeholder={translate(prefix, 'username.placeholder')}
              prefix={<UserOutlined />}
              style={inputStyle}
              onKeyDown={handleEntryPress}
            />
          </Form.Item>
          <Form.Item
            name="password"
            label={translate(prefix, 'password')}
            rules={[
              { required: true },
              { max: 30, message: translate(prefix, 'validation.password.max') },
              { min: 6, message: translate(prefix, 'validation.password.min') },
            ]}
          >
            <Input.Password
              placeholder={translate(prefix, 'password.placeholder')}
              prefix={<LockOutlined />}
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              style={inputStyle}
              onKeyDown={handleEntryPress}
            />
          </Form.Item>

          <div className="flex-row flex-align-center m-b">
            <Form.Item name="remember" valuePropName="checked" noStyle>
              <Checkbox className="primary-color">{translate(prefix, 'remember')}</Checkbox>
            </Form.Item>
          </div>

          <Button
            type="primary"
            shape="round"
            loading={nextLoading}
            style={inputStyle}
            className="btn-color"
            onClick={handleNext}
            disabled={nextLoading}
          >
            {nextLoading ? translate(prefix, 'button.validating') : translate(prefix, 'button.next')}
          </Button>
        </Form>

        {/* 角色岗位选择弹窗 */}
        <Modal
          title={translate(prefix, 'modal.title')}
          open={modalVisible}
          maskClosable={false}
          onCancel={() => setModalVisible(false)}
          footer={[
            <Button key="cancel" onClick={() => setModalVisible(false)}>
              {translate('common', 'cancel')}
            </Button>,
            <Button key="login" type="primary" loading={loading} onClick={handleLogin} disabled={loading}>
              {loading ? translate(prefix, 'button.loading') : translate(prefix, 'button')}
            </Button>,
          ]}
        >
          <Form form={modalForm} layout="horizontal" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
            <Form.Item name="role" label={translate(prefix, 'role')} rules={[{ required: true }]}>
              <Select
                allowClear
                style={inputStyle}
                placeholder={translate(prefix, 'role.placeholder')}
                options={roleOptions}
              />
            </Form.Item>
            <Form.Item name="station" label={translate(prefix, 'station')} rules={[{ required: true }]}>
              <Select
                allowClear
                style={inputStyle}
                placeholder={translate(prefix, 'station.placeholder')}
                options={stationOptions}
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
}
