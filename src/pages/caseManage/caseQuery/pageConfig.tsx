import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.CALL_PARAM,
  cardTitle: 'caseQuery',
  // 页面接口请求
  urlObj: {
    list: urlConstants.ADUIT_MANAGE.LIST,
    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'caseCode',
      label: 'caseCode',
      type: COMPONENT_TYPE.INPUT,
      data: DICT_CONSTANTS.STATION,
    },
    {
      value: 'caseType',
      label: 'caseType',
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 80,
    },
    {
      title: 'caseCode',
      dataIndex: 'caseCode',
      key: 'caseCode',
    },
    {
      title: 'caseType',
      dataIndex: 'caseType',
      key: 'caseType',
      width: 120,
    },
    {
      title: 'orgCustNbr',
      dataIndex: 'orgCustNbr',
      key: 'orgCustNbr',
    },
    {
      title: 'custName',
      dataIndex: 'custName',
      key: 'custName',
    },
    {
      title: 'caseAmount',
      dataIndex: 'caseAmount',
      key: 'caseAmount',
      width: 120,
    },
    {
      title: 'delayDays',
      dataIndex: 'delayDays',
      key: 'delayDays',
    },
    {
      title: 'caseStatus',
      dataIndex: 'caseStatus',
      key: 'caseStatus',
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 150,
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 160,
      valueType: RENDER_TYPE.DateTime,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};
