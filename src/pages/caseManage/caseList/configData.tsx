import { DATE_FORMATE, TIME_FORMATE } from "@/constants/publicConstant";
import { FormattedMessage } from "react-intl";
import dayjs from "dayjs";
import { intlConst } from "@/hooks/useIntlCustom";

export default {
  tableData: {
    CALL_PERRSON_MODAL: [
      { id: "1", name: "张三", department: "电催一部", groups: "一组" },
      { id: "2", name: "李三", department: "电催一部", groups: "二组" },
      { id: "3", name: "王五", department: "委派一部", groups: "三组" },
    ],
  },
  columns: {
    CALL_PERRSON_MODAL: [
      {
        key: "name",
        title: <FormattedMessage id="callParam.name" />,
        dataIndex: "name",
      },
      {
        key: "department",
        title: <FormattedMessage id="callParam.department" />,
        dataIndex: "department",
      },
      {
        key: "groups",
        title: <FormattedMessage id="callParam.groups" />,
        dataIndex: "groups",
      },
    ],
    PHONE_CALL_COLUMNS: [
      {
        key: "caseCode",
        title: <FormattedMessage id="callParam.caseCode" />,
        dataIndex: "caseCode",
      },
      {
        key: "caseResultFlag",
        title: <FormattedMessage id="callParam.caseResultFlag" />,
        dataIndex: "caseResultFlag",
      },
      {
        key: "contactName",
        title: <FormattedMessage id="callParam.contactName" />,
        dataIndex: "contactName",
      },
      {
        key: "phoneNmbr",
        title: <FormattedMessage id="callParam.phoneNmbr" />,
        dataIndex: "phoneNmbr",
      },
      {
        key: "relation",
        title: <FormattedMessage id="callParam.relation" />,
        dataIndex: "relation",
      },
      {
        key: "actionCode",
        title: <FormattedMessage id="callParam.contactCode" />,
        dataIndex: "actionCode",
      },
      {
        key: "contactRemarks",
        title: <FormattedMessage id="callParam.contactRemarks" />,
        dataIndex: "contactRemarks",
      },
      {
        key: "reviewDate",
        title: <FormattedMessage id="callParam.reviewDate" />,
        dataIndex: "reviewDate",
        render: (text) => dayjs(text).format(TIME_FORMATE),
      },
      {
        key: "commitmentAmount",
        title: <FormattedMessage id="callParam.commitmentAmount" />,
        dataIndex: "commitmentAmount",
      },
      {
        key: "commitmentDate",
        title: <FormattedMessage id="callParam.commitmentDate" />,
        dataIndex: "commitmentDate",
        render: (text) => dayjs(text).format(TIME_FORMATE),
      },
      {
        key: "dailFlag",
        title: <FormattedMessage id="callParam.dailFlag" />,
        dataIndex: "dailFlag",
      },
      {
        key: "callDuration",
        title: <FormattedMessage id="callParam.callDuration" />,
        dataIndex: "callDuration",
      },
      {
        key: "startTime",
        title: <FormattedMessage id="callParam.startTime" />,
        dataIndex: "startTime",
        render: (text) => dayjs(text).format(TIME_FORMATE),
      },
      {
        key: "endTime",
        title: <FormattedMessage id="callParam.endTime" />,
        dataIndex: "endTime",
        render: (text) => dayjs(text).format(TIME_FORMATE),
      },
      {
        key: "audioCode",
        title: <FormattedMessage id="callParam.audioCode" />,
        dataIndex: "audioCode",
      },
      {
        key: "actionDate",
        title: <FormattedMessage id="callParam.actionDate" />,
        dataIndex: "actionDate",
        render: (text) => dayjs(text).format(DATE_FORMATE),
      },
      {
        key: "obType",
        title: <FormattedMessage id="callParam.obType" />,
        dataIndex: "obType",
        render: (text) =>
          `${text}-${intlConst.formatMessage("callParam", "decisionDataLoad")}`,
      },
      {
        key: "telType",
        title: <FormattedMessage id="callParam.telType" />,
        dataIndex: "telType",
        render: (text) =>
          `${text}-${intlConst.formatMessage("callParam", "mobile")}`,
      },
      {
        key: "callType",
        title: <FormattedMessage id="callParam.callType" />,
        dataIndex: "callType",
        render: (text) =>
          `${text}-${intlConst.formatMessage("callParam", "callOut")}`,
      },
    ],
    MSG_CALL_COLUMNS: [
      {
        key: "id",
        title: <FormattedMessage id="callParam.id" />,
        dataIndex: "id",
      },
      {
        key: "orgCustNbr",
        title: <FormattedMessage id="callParam.customerNo" />,
        dataIndex: "orgCustNbr",
      },
      {
        key: "caseCode",
        title: <FormattedMessage id="callParam.caseCode" />,
        dataIndex: "caseCode",
      },
      {
        key: "templateCode",
        title: <FormattedMessage id="callParam.templateCode" />,
        dataIndex: "templateCode",
      },
      {
        key: "templateName",
        title: <FormattedMessage id="callParam.templateName" />,
        dataIndex: "templateName",
      },
      {
        key: "telNum",
        title: <FormattedMessage id="callParam.mobileNumber" />,
        dataIndex: "telNum",
      },
      {
        key: "sendContent",
        title: <FormattedMessage id="callParam.sendContent" />,
        dataIndex: "sendContent",
        render: (text) => (
          <div style={{ wordWrap: "break-word", whiteSpace: "normal" }}>
            {text}
          </div>
        ),
      },
      {
        key: "receiver",
        title: <FormattedMessage id="callParam.receiverName" />,
        dataIndex: "receiver",
      },
      {
        key: "sender",
        title: <FormattedMessage id="callParam.senderCollector" />,
        dataIndex: "sender",
      },
      {
        key: "sendTime",
        title: <FormattedMessage id="callParam.sendTime" />,
        dataIndex: "sendTime",
        render: (text) => dayjs(text).format(TIME_FORMATE),
      },
      {
        key: "actionDate",
        title: <FormattedMessage id="callParam.actionDate" />,
        dataIndex: "actionDate",
        render: (text) => dayjs(text).format(DATE_FORMATE),
      },
      {
        key: "jobDay",
        title: <FormattedMessage id="callParam.taskDate" />,
        dataIndex: "jobDay",
        render: (text) => dayjs(text).format(DATE_FORMATE),
      },
      {
        key: "createUser",
        title: <FormattedMessage id="callParam.creator" />,
        dataIndex: "createUser",
      },
      {
        key: "createDate",
        title: <FormattedMessage id="callParam.createTime" />,
        dataIndex: "createDate",
        render: (text) => dayjs(text).format(TIME_FORMATE),
      },
      {
        key: "updateUser",
        title: <FormattedMessage id="callParam.updater" />,
        dataIndex: "updateUser",
      },
      {
        key: "updateDate",
        title: <FormattedMessage id="callParam.updateTime" />,
        dataIndex: "updateDate",
        render: (text) => dayjs(text).format(TIME_FORMATE),
      },
    ],
    REPAYMENT_COLUMNS: [
      {
        key: "id",
        title: <FormattedMessage id="callParam.id" />,
        dataIndex: "id",
      },
      {
        key: "caseCode",
        title: <FormattedMessage id="callParam.caseCode" />,
        dataIndex: "caseCode",
      },
      {
        key: "cardNbr",
        title: <FormattedMessage id="callParam.cardNumber" />,
        dataIndex: "cardNbr",
      },
      {
        key: "transCode",
        title: <FormattedMessage id="callParam.transactionCode" />,
        dataIndex: "transCode",
      },
      {
        key: "txnCurrCode",
        title: <FormattedMessage id="callParam.transactionCurrency" />,
        dataIndex: "txnCurrCode",
      },
      {
        key: "txnCurrAmt",
        title: <FormattedMessage id="callParam.transactionAmount" />,
        dataIndex: "txnCurrAmt",
      },
      {
        key: "postCurr",
        title: <FormattedMessage id="callParam.postingCurrency" />,
        dataIndex: "postCurr",
      },
      {
        key: "amount",
        title: <FormattedMessage id="callParam.postingAmount" />,
        dataIndex: "amount",
      },
      {
        key: "purchaseDte",
        title: <FormattedMessage id="callParam.transactionDate" />,
        dataIndex: "purchaseDte",
        render: (text) => dayjs(text).format(DATE_FORMATE),
      },
      {
        key: "postingDte",
        title: <FormattedMessage id="callParam.postingDate" />,
        dataIndex: "postingDte",
        render: (text) => dayjs(text).format(DATE_FORMATE),
      },
      {
        key: "dbaDescription",
        title: <FormattedMessage id="callParam.transactionDescription" />,
        dataIndex: "dbaDescription",
      },
    ],
  },
  message: {
    CALL_PERRSON_MODAL: <FormattedMessage id="callParam.sendMessageSuccess" />,
  },
  options: [
    { label: <FormattedMessage id="callParam.connected" />, value: "1" },
    { label: <FormattedMessage id="callParam.rejected" />, value: "2" },
  ],
  URL: {
    phoneCall: "wcs/tdc/query",
    messageCall: "wcs/sms/query",
    repayment: "wcs/basRepayment/query",
  },
  selectProps: {
    placeholder: <FormattedMessage id="callParam.selectPlaceholder" />,
    style: { width: 200 },
  },
  disOptions: [
    { label: <FormattedMessage id="callParam.amountBalance" />, value: "amount" },
    { label: <FormattedMessage id="callParam.caseBalance" />, value: "quantity" },
    // {
    //   label: <FormattedMessage id="callParam.amountAndCaseBalance" />,
    //   value: "3",
    // },
  ],
  transOptions: [
    { label: <FormattedMessage id="callParam.teamA" />, value: "1" },
    { label: <FormattedMessage id="callParam.teamB" />, value: "2" },
    { label: <FormattedMessage id="callParam.teamC" />, value: "3" },
    { label: <FormattedMessage id="callParam.outsourcingQueue" />, value: "4" },
    { label: <FormattedMessage id="callParam.litigationQueue" />, value: "5" },
  ],
};
