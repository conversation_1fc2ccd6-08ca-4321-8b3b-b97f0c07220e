// 业务參數/參數/授權/授權回應碼參數
import { CommonTable, GradientButton } from '@/components';
import CommonModal from '@/components/CommonModal';
import { DEFAULT_PAGINATION, I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import useIntlCustom from '@/hooks/useIntlCustom';
import commonService from '@/services/common';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import { Button, Form, message, Select, Space, Tabs, TabsProps } from 'antd';
import { useForm } from 'antd/es/form/Form';
import _ from 'lodash';
import React, { Suspense, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import flowNodeMockData from '../../sourceManage/case/caseInfo/mockData';
import configData from './configData';
import { dictEnum, pageConfig } from './pageConfig';
import useFormConfig from './useFormConfig';

const PageTemplate = React.lazy(() => import('@/components/templates/PageTemplate'));
const DargeGraph = React.lazy(() => import('@/materials/dargeGraph'));
const CaseModal = React.lazy(() => import('./components/CaseModal'));
const CaseDrawer = React.lazy(() => import('./components/CaseDrawer'));
const DispatchDrawer = React.lazy(() => import('./components/DispatchDrawer'));

const CaseManagePage: React.FC = () => {
  const { translate } = useIntlCustom();
  const navigate = useNavigate();
  const { urlObj, prefix, searchSource, columns, cardTitle, resetValue } = pageConfig;

  const formRef = useRef(null);
  const [form] = useForm();
  const [type, setType] = useState<string>(OPERATE_TYPE.list);
  const { infoFormConfig } = useFormConfig(type);

  const [open, setOpen] = useState<boolean>(false);
  const [disVisible, setDisVissible] = useState<boolean>(false);
  const [transVisible, setTransVissible] = useState<boolean>(false);
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [dispatchDrawerOpen, setDispatchDrawerOpen] = useState<boolean>(false);
  const [detailData, setDetailData] = useState<any>({});
  const [flowNodeData, setFlowNodeData] = useState<any>({});
  const [listData, setListData] = useState<object[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 副作用.
  useEffect(() => {
    getTableData();
  }, []);

  // 逻辑处理
  const getTableData = async (searchValue = { keyValue: '', value: '' }, pagination = { ...DEFAULT_PAGINATION }) => {
    // setLoading(true);
    try {
      const param = {
        url: urlConstants.ADUIT_MANAGE.LIST,
        pagination,
        searchValue: { [searchValue.keyValue]: searchValue?.value },
      };
      const res = await commonService.getTableListData(param);
      const { data, total } = res;
      Array.isArray(data) && setListData(data);
    } catch (error) {
      setListData([]);
      setLoading(false);
    }
  };
  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 表格配置
  const tableConfig = {
    columns,
    rowKey: 'paramIndex',
    showPagination: true,
    optionList: [
      { type: OPERATE_TYPE.flowNode },
      {
        type: OPERATE_TYPE.detail,
        onCustomize: (val, row) => {
          navigate('/workBench/collectorBench', { state: { caseId: row.id } });
        },
      },
    ],
    props: {
      scroll: { x: 1500, y: 2000 },
      rowSelection: {
        type: 'checkbox',
        onChange: (selectedRowKeys: string, selectedRows: any) => {
          // console.log('33333--selectedRows--table', selectedRowKeys, selectedRows[0]);
        },
      },
    },
  };

  // 按钮配置
  const formActionConfig = {
    showCreate: false,
    showSubmit: false,
  };

  // 表单配置
  const formConfig = {
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
    customChildren: {
      [OPERATE_TYPE.flowNode]: <DargeGraph data={flowNodeData} />,
    },
  };

  // 获取节点信息
  const handleNodeFlow = async (row) => {
    const params = {
      url: urlConstants.CASE_BASE_INFO.FLOW_NODE,
      caseCode: row.caseCode,
    };
    const res: any = await commonService.getEditPostBiz(params);
    if (res.header?.errorCode === '000000') {
      const nodeData = res.data?.map((item, index) => ({
        id: index?.toString(),
        color: '#C8E6C9',
        label: item.flowchartName,
        data: {
          ...item,
        },
      }));
      setFlowNodeData(
        _.isEmpty(res.data)
          ? { nodes: [], edges: [] }
          : {
              nodes: [...nodeData],
              edges: flowNodeMockData.edges.slice(0, nodeData?.length ? nodeData?.length - 1 : 1),
            },
      );
      _.isEmpty(res.data)
        ? message.error(translate(I18N_COMON_PAGENAME.CALL_PARAM, 'noNodeInfo'))
        : message.success('SUCCESS');
    }
  };

  // 自定义分案提交
  const handleModalSubmit = () => {
    try {
      form.validateFields().then(async (values) => {
        setDisVissible(false);
        setTransVissible(false);
        message.success(translate(I18N_COMON_PAGENAME.CALL_PARAM, 'transferDispatchSuccess'));
      });
    } catch {}
  };

  // 列表按钮操作
  const handleAction = async (editType: string, row: any) => {
    if (editType === OPERATE_TYPE.flowNode) {
      await handleNodeFlow(row);
    }
    setDetailData({ ...row });
    setType(editType);
  };

  // 派案弹窗内容
  const disContent = () => {
    return (
      <Form ref={formRef} form={form} name="basic" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} autoComplete="off">
        <Form.Item
          label={translate(I18N_COMON_PAGENAME.CALL_PARAM, 'dispatchStrategy')}
          name="node"
          initialValue="quantity"
          rules={[{ required: true }]}
        >
          <Select {...configData.selectProps} options={configData.disOptions} />
        </Form.Item>
      </Form>
    );
  };

  // 转案弹窗内容
  const transContent = () => {
    return (
      <Form ref={formRef} form={form} name="basic" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} autoComplete="off">
        <Form.Item
          label={translate(I18N_COMON_PAGENAME.CALL_PARAM, 'targetQueue')}
          name="tranNode"
          initialValue="1"
          rules={[{ required: true }]}
        >
          <Select {...configData.selectProps} options={configData.transOptions} />
        </Form.Item>
      </Form>
    );
  };

  // 列表渲染按钮
  const cardExtra = () => {
    return (
      <Space>
        <GradientButton onClick={() => setTransVissible(true)}>
          {translate(I18N_COMON_PAGENAME.CALL_PARAM, 'transfer')}
        </GradientButton>
        <GradientButton onClick={() => setDisVissible(true)}>
          {translate(I18N_COMON_PAGENAME.CALL_PARAM, 'dispatch')}
        </GradientButton>
      </Space>
    );
  };

  // 渲染表单额外按钮
  const formExtra = () => {
    return (
      <Button type="primary" onClick={() => setDrawerOpen(true)}>
        {translate(I18N_COMON_PAGENAME.CALL_PARAM, 'collectionRecord')}
      </Button>
    );
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: translate(I18N_COMON_PAGENAME.CALL_PARAM, 'casePool'),
      children: (
        <>
          <CommonTable
            rowKey="paramIndex"
            dataSource={listData}
            columns={columns}
            optionList={[]}
            intlPrefix={prefix}
          />
          <div className="flex-row flex-justify-center flex-align-center" style={{ marginTop: '40%' }}>
            <Space size="large">
              <Button type="primary" size="large" icon={<PlusOutlined />}>
                抢案
              </Button>
              <Button type="default" size="large" icon={<ReloadOutlined />}>
                刷新
              </Button>
            </Space>
          </div>
        </>
      ),
    },
    {
      key: '2',
      label: translate(I18N_COMON_PAGENAME.CALL_PARAM, 'caseMyCase'),
      children: (
        <PageTemplate
          searchConfig={searchConfig}
          tableConfig={tableConfig}
          formConfig={formConfig}
          urlObj={urlObj}
          isShowRouterBar={false}
          formExtra={formExtra()}
          cardTitle={cardTitle}
          formActionConfig={formActionConfig}
          intlPrefix={prefix}
          dictEnum={dictEnum}
          onAction={handleAction}
          cardExtra={cardExtra()}
        />
      ),
    },
  ];

  const onChange = (key: string) => {
    console.log(key);
  };

  return (
    <Suspense>
      <Tabs defaultActiveKey="1" items={items} onChange={onChange} className="app-block " style={{ height: '100%' }} />
      {open && <CaseModal visible={open} onCancel={(visible) => setOpen(visible)} />}
      {transVisible && (
        <CommonModal
          type="CALL_TRANS_CUSTOMIZE_MODAL"
          open={transVisible}
          content={transContent}
          onClose={() => setTransVissible(false)}
          onSubmit={() => setTransVissible(false)}
        />
      )}
      {disVisible && (
        <CommonModal
          type="CALL_DIS_CUSTOMIZE_MODAL"
          open={disVisible}
          content={disContent}
          onClose={() => setDisVissible(false)}
          onSubmit={() => {
            (setDisVissible(false), setDispatchDrawerOpen(true));
          }}
        />
      )}
      {dispatchDrawerOpen && (
        <DispatchDrawer
          detailData={{
            node: form.getFieldValue('node') || 'quantity',
          }}
          visible={dispatchDrawerOpen}
          onCancel={(visible) => setDispatchDrawerOpen(visible)}
        />
      )}
      {drawerOpen && (
        <CaseDrawer detailData={detailData} visible={drawerOpen} onCancel={(visible) => setDrawerOpen(visible)} />
      )}
    </Suspense>
  );
};
export default React.memo(CaseManagePage);
