import React, { useRef, useState } from "react";
import _ from "lodash";
import { Drawer, Steps, Card, Button, Space, message, Tabs } from "antd";
import useIntlCustom from "@/hooks/useIntlCustom";
import { I18N_COMON_PAGENAME, OPERATE_TYPE } from "@/constants/publicConstant";
import { EditTable } from "@/components";
import { tableData, tableColumns } from "./mockData";

const prefix = I18N_COMON_PAGENAME.CALL_PARAM;

const DispatchDrawer = ({ visible, detailData, onCancel }) => {
  const { node } = detailData;
  const { translate } = useIntlCustom();
  const editableFormRef: any = useRef();
  const [currentStep, setCurrentStep] = useState(0);
  const [btnDisabled, setBtnDisabled] = useState<boolean>(false);
  const [activeKey, setActiveKey] = useState<string>("positionSteps");
  const [dataSource, setDataSource] = useState({ ...tableData });

  // 步骤配置-岗位
  const positionSteps = [
    {
      title: translate(prefix, detailData.node) + translate(prefix, "balance"),
    },
    {
      title: translate(prefix, detailData.node) + translate(prefix, "balance"),
      description: translate(prefix, "preview"),
    },
  ];
  // 步骤配置-人员
  const staffSteps = [
    {
      title: translate(prefix, detailData.node) + translate(prefix, "balance"),
    },
    {
      title: translate(prefix, detailData.node) + translate(prefix, "balance"),
      description: translate(prefix, "preview"),
    },
  ];
  // 处理上一步
  const handlePrevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // 提交，关闭弹窗
  const handleSubmit = () => {
    setCurrentStep(currentStep + 1);
    message.success("Dispatch Success!");
  };

  // 修改权重
  const handleChangeWeight = (newTableData: any, rateFiled, filed) => {
    const stepKey = activeKey === "positionSteps" ? "step1" : "step2";

    const totalWeight = newTableData.reduce(
      (pre, item) => pre + (Number(item[rateFiled]) || 0),
      0
    );

    const caseTotal = tableData[node][stepKey].reduce(
      (pre, item) => pre + (Number(item[filed]) || 0),
      0
    );
    const aaaa = newTableData.map((item) => ({
      ...item,
      [filed]: Math.round(Number(item[rateFiled] / totalWeight) * caseTotal),
    }));

    aaaa.forEach((item, index) => {
      editableFormRef.current?.setRowData(index, item);
    });

    setDataSource((prevDataSource) => ({
      ...prevDataSource,
      [node]: {
        ...prevDataSource[node],
        [stepKey]: aaaa,
      },
    }));
  };

  // 修改表格数据
  const handleFormSave = (key, row, originRow) => {
    const stepKey = activeKey === "positionSteps" ? "step1" : "step2";
    console.log("5555----key, row, originRow", key, row, originRow);
    const newTableData = dataSource[node][stepKey].map((item) => {
      if (item.id === key) {
        return row;
      }
      return item;
    });
    setDataSource({
      ...dataSource,
      [node]: {
        ...dataSource[node],
        [stepKey]: newTableData,
      },
    });
    setBtnDisabled(false);
    // 修改权重动态调整案件数量
    if (node === "quantity" && activeKey === "positionSteps") {
      handleChangeWeight(newTableData, "weightCoefficient1", "caseNum1");
    } else if (node === "quantity" && activeKey === "staffSteps") {
      handleChangeWeight(newTableData, "weightCoefficient2", "caseNum2");
    } else if (node === "amount" && activeKey === "positionSteps") {
      handleChangeWeight(newTableData, "weightCoefficient1", "caseAmount1");
    } else if (node === "amount" && activeKey === "staffSteps") {
      handleChangeWeight(newTableData, "weightCoefficient2", "caseAmount2");
    }
  };

  // 渲染当前步骤的表格
  const renderStepContent = () => {
    const stepKey = activeKey === "positionSteps" ? "step1" : "step2";
    return (
      <EditTable
        rowKey="id"
        editTableType="caseDemo"
        ref={editableFormRef}
        columns={tableColumns[node][stepKey]}
        dataSource={dataSource[node][stepKey]}
        intlPrefix={prefix}
        optionCount={currentStep === 0 ? 1 : 0}
        onFormSave={handleFormSave}
        onAction={(optionType, row) => setBtnDisabled(true)}
        // @ts-ignore
        getOptionList={(row) => [
          {
            optionType: OPERATE_TYPE.edit,
          },
        ]}
      />
    );
  };

  // 渲染底部按钮
  const renderExtraButtons = () => {
    return (
      <Space>
        {currentStep === 1 && (
          <Button onClick={handlePrevStep} disabled={btnDisabled}>
            {translate(I18N_COMON_PAGENAME.COMMON, "previousStep")}
          </Button>
        )}
        {currentStep == 0 && (
          <Button type="primary" onClick={handleSubmit} disabled={btnDisabled}>
            {translate(I18N_COMON_PAGENAME.COMMON, "submit")}
          </Button>
        )}
      </Space>
    );
  };

  const tabsItems = [
    {
      key: "positionSteps",
      label: translate(prefix, "position"), // 岗位
      children: (
        <Card>
          <Steps
            current={currentStep}
            items={positionSteps}
            style={{ marginBottom: 24 }}
          />
          {renderStepContent()}
        </Card>
      ),
    },
    {
      key: "staffSteps",
      label: translate(prefix, "staff"), // 人员
      children: (
        <Card>
          <Steps
            current={currentStep}
            items={staffSteps}
            style={{ marginBottom: 24 }}
          />
          {renderStepContent()}
        </Card>
      ),
    },
  ];

  return (
    <Drawer
      maskClosable={false}
      open={visible}
      title={translate(prefix, "dispatchProcess")}
      width="80%"
      onClose={() => onCancel(false)}
      extra={renderExtraButtons()}
    >
      <Tabs
        defaultActiveKey="positionSteps"
        activeKey={activeKey}
        items={tabsItems}
        onChange={(val: string) => {
          setActiveKey(val), setCurrentStep(0);
        }}
      />
    </Drawer>
  );
};

export default React.memo(DispatchDrawer);
