// quantity 表示案件均衡，amount 表示金额均衡
import { intlConst } from "@/hooks/useIntlCustom";
import { I18N_COMON_PAGENAME } from "@/constants/publicConstant";

const prefix = I18N_COMON_PAGENAME.CALL_PARAM;

const tableData = {
  quantity: {
    step1: [
      {
        id: "1",
        position: intlConst.formatMessage(prefix, "customerManager"),
        weightCoefficient1: "1",
        caseNum1: 100,
      },
      {
        id: "2",
        position: intlConst.formatMessage(prefix, "collectionQA"),
        weightCoefficient1: "1.5",
        caseNum1: 150,
      },
      {
        id: "3",
        position: intlConst.formatMessage(prefix, "collectionSpecialist"),
        weightCoefficient1: "2",
        caseNum1: 200,
      },
      {
        id: "4",
        position: intlConst.formatMessage(prefix, "seniorCollector"),
        weightCoefficient1: "1.2",
        caseNum1: 120,
      },
      {
        id: "5",
        position: intlConst.formatMessage(prefix, "expertCollector"),
        weightCoefficient1: "1.8",
        caseNum1: 180,
      },
      {
        id: "6",
        position: intlConst.formatMessage(prefix, "collectionSupervisor"),
        weightCoefficient1: "2.5",
        caseNum1: 250,
      },
    ],
    step2: [
      { id: "1", staffName: "JACK", weightCoefficient2: "1", caseNum2: 100 },
      { id: "2", staffName: "SPEED", weightCoefficient2: "2", caseNum2: 200 },
      { id: "3", staffName: "TOM", weightCoefficient2: "1.5", caseNum2: 150 },
      { id: "4", staffName: "SARAH", weightCoefficient2: "2.5", caseNum2: 250 },
      { id: "5", staffName: "MIKE", weightCoefficient2: "1.8", caseNum2: 180 },
      { id: "6", staffName: "LUCY", weightCoefficient2: "1.2", caseNum2: 120 },
    ],
  },
  amount: {
    step1: [
      {
        id: "1",
        position: intlConst.formatMessage(prefix, "customerManager"),
        weightCoefficient1: "1",
        caseAmount1: 10000,
      },
      {
        id: "2",
        position: intlConst.formatMessage(prefix, "collectionQA"),
        weightCoefficient1: "1.5",
        caseAmount1: 15000,
      },
      {
        id: "3",
        position: intlConst.formatMessage(prefix, "collectionSpecialist"),
        weightCoefficient1: "2",
        caseAmount1: 20000,
      },
      {
        id: "4",
        position: intlConst.formatMessage(prefix, "seniorCollector"),
        weightCoefficient1: "1.2",
        caseAmount1: 12000,
      },
      {
        id: "5",
        position: intlConst.formatMessage(prefix, "expertCollector"),
        weightCoefficient1: "1.8",
        caseAmount1: 18000,
      },
      {
        id: "6",
        position: intlConst.formatMessage(prefix, "collectionSupervisor"),
        weightCoefficient1: "2.5",
        caseAmount1: 25000,
      },
    ],
    step2: [
      {
        id: "1",
        staffName: "JACK",
        weightCoefficient2: "1",
        caseAmount2: 10000,
      },
      {
        id: "2",
        staffName: "SPEED",
        weightCoefficient2: "2",
        caseAmount2: 20000,
      },
      {
        id: "3",
        staffName: "TOM",
        weightCoefficient2: "1.5",
        caseAmount2: 15000,
      },
      {
        id: "4",
        staffName: "SARAH",
        weightCoefficient2: "2.5",
        caseAmount2: 25000,
      },
      {
        id: "5",
        staffName: "MIKE",
        weightCoefficient2: "1.8",
        caseAmount2: 18000,
      },
      {
        id: "6",
        staffName: "LUCY",
        weightCoefficient2: "1.2",
        caseAmount2: 12000,
      },
    ],
  },
};

// 定义每个步骤的表格列
const tableColumns = {
  quantity: {
    step1: [
      {
        key: "position",
        title: "position",
        dataIndex: "position",
        fieldProps: { disabled: true },
      },
      {
        key: "weightCoefficient1",
        title: "weightCoefficient",
        dataIndex: "weightCoefficient1",
      },
      {
        key: "caseNum1",
        title: "caseNum",
        dataIndex: "caseNum1",
        fieldProps: { disabled: true },
      },
    ],
    step2: [
      {
        key: "staffName",
        title: "staffName",
        dataIndex: "staffName",
        fieldProps: { disabled: true },
      },
      {
        key: "weightCoefficient2",
        title: "weightCoefficient",
        dataIndex: "weightCoefficient2",
      },
      {
        key: "caseNum2",
        title: "caseNum",
        dataIndex: "caseNum2",
        fieldProps: { disabled: true },
      },
    ],
  },
  amount: {
    step1: [
      {
        key: "position",
        title: "position",
        dataIndex: "position",
        fieldProps: { disabled: true },
      },
      {
        key: "weightCoefficient1",
        title: "weightCoefficient",
        dataIndex: "weightCoefficient1",
      },
      {
        key: "caseAmount1",
        title: "caseAmount",
        dataIndex: "caseAmount1",
        fieldProps: { disabled: true },
      },
    ],
    step2: [
      {
        key: "staffName",
        title: "staffName",
        dataIndex: "staffName",
        fieldProps: { disabled: true },
      },
      {
        key: "weightCoefficient2",
        title: "weightCoefficient",
        dataIndex: "weightCoefficient2",
      },
      {
        key: "caseAmount2",
        title: "caseAmount",
        dataIndex: "caseAmount2",
        fieldProps: { disabled: true },
      },
    ],
  },
};
export { tableData, tableColumns };
