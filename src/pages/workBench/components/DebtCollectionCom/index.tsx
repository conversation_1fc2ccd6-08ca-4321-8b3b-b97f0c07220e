import { FormMaintenance } from '@/components';
import { TIME_FORMATE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import useIntlCustom from '@/hooks/useIntlCustom';
import commonService from '@/services/common';
import { CheckOutlined } from '@ant-design/icons';
import { Button, Form, message, Select, Table } from 'antd';
import { useForm } from 'antd/es/form/Form';
import TextArea from 'antd/es/input/TextArea';
import dayjs from 'dayjs';
import { FC, memo, useRef, useState } from 'react';
import styles from '../../index.module.css';
import mockData from './mockData';

interface IDebtCollectionCom {
  formData?: any;
  onSubmit?: () => void;
  debtData: {
    title: string;
    actionType: string;
  };
}
const DebtCollectionCom: FC<IDebtCollectionCom> = ({ debtData, formData, onSubmit }) => {
  const { translate } = useIntlCustom();
  const { title, actionType } = debtData;
  const [form] = useForm();
  const formRef = useRef(null);
  const [open, setOpen] = useState<boolean>(false);
  const [rowKeys, setRowKeys] = useState<any>(null);

  // 提交
  const handleClick = async () => {
    form
      .validateFields()
      .then(async (values) => {
        let params: any;
        if (actionType === 'PHONE_CALL_MODAL') {
          params = {
            url: mockData.URL[actionType],
            caseCode: mockData.caseCode[actionType],
            actionCode: values.actionCode,
            contactRemarks: values.contactRemarks,
            createUser: '王丽',
            updateUser: '张俊锋',
            orgCustNbr: 'CUST123456',
            caseResultFlag: 'U',
            contactName: '张三',
            phoneNmbr: '13800138000',
            relation: '本人',
            commitmentAmount: '5000.0',
            commitmentDate: '2023-10-05 12:00:00',
            dailFlag: 'Y',
            callDuration: '00:05:30',
            startTime: '2023-10-01 12:00:00',
            endTime: '2023-10-01 12:05:30',
            audioCode: 'AUDIO123456',
            actionDate: '2023-10-01 12:00:00',
            obType: '0',
            telType: 'M',
            callType: '2',
            reviewDate: dayjs(values.reviewDate).format(TIME_FORMATE),
            ...formData,
          };
        } else {
          params = {
            url: mockData.URL[actionType],
            caseCode: mockData.caseCode[actionType],
            actionCode: values.actionCode,
            contactRemarks: values.contactRemarks,
            reviewDate: dayjs(values.reviewDate).format(TIME_FORMATE),
            createUser: '王丽',
            updateUser: '张俊锋',
            orgCustNbr: 'CUST123456',
            templateCode: 'TEMPLATE001',
            templateName: 'Payment Reminder',
            telNum: '13800138000',
            sendContent:
              '刘女士，您好！您的账单[账单编号]已逾期，欠款金额为[金额]元。长期逾期可能会影响您的信用评分及未来金融服务申请。为避免不必要的麻烦，请尽快处理。我们始终愿意与您沟通，共同寻找解决办法。如有任何疑问，欢迎随时联系。',
            receiver: 'John Doe',
            sender: 'Alice Smith',
            sendTime: '2025-03-13 20:27:00',
            actionDate: '2025-03-14 00:00:00',
            jobDay: '2025-03-13 00:00:00',
            ...formData,
          };
        }
        const res: any = await commonService.getEditPostBiz(params);
        const res1: any = await commonService.getEditPostBiz({
          url: urlConstants.CASE_BASE_INFO.EDIT,
          id: formData.id,
          model: formData.model,
          currState: 'C',
        });
        if (res.header?.errorCode === '000000') {
          message.success(res.header?.errorMsg || mockData.message[actionType]);
          setOpen(false);
          onSubmit?.();
        }
      })
      .catch((errorInfo) => {});
  };

  const handleTitle = async () => {
    await onSubmit?.();
    setOpen(true);
  };

  // 打电话/发短信
  const handleModalSubmit = () => {
    if (!rowKeys) return message.error('请选择客户');
    if (Number(formData?.classIBalance) <= 0) {
      message.error('该客户无欠款，请结案');
    } else {
      message.success(mockData.message[actionType]);
      setOpen(false);
    }
  };

  // 弹窗节点内容
  const content = () => (
    <Table
      rowKey="id"
      columns={mockData.columns[actionType]}
      dataSource={mockData.tableData[actionType]}
      rowSelection={{
        type: 'radio',
        onChange: (selectedRowKeys: any, selectedRows: any) => {
          setRowKeys(selectedRows?.[0]);
        },
      }}
    />
  );
  // 节点
  const childNode = () => {
    return (
      <div key={title} className={`${styles.bottomContent} flex-row flex-justify-between flex-align-center`}>
        <div className={styles.left}>
          <Form
            ref={formRef}
            form={form}
            name="basic"
            labelCol={{ span: 10 }}
            wrapperCol={{ span: 14 }}
            autoComplete="off"
            initialValues={{ sameNameAccountAdjustment: 'CMLM' }}
          >
            <Form.Item
              label={translate('callParam', 'sameNameAccountAdjustment')}
              name="sameNameAccountAdjustment"
              rules={[{ required: true }]}
            >
              <Select style={{ width: 180 }} options={mockData.options} />
            </Form.Item>
          </Form>
        </div>
        <div className={styles.center}>
          <Form
            ref={formRef}
            form={form}
            name="basic"
            labelCol={{ span: 10 }}
            wrapperCol={{ span: 14 }}
            autoComplete="off"
          >
            <Form.Item
              label={translate('callParam', 'contactRemarks')}
              name="contactRemarks"
              rules={[{ required: true }]}
            >
              <TextArea />
            </Form.Item>
          </Form>
        </div>
        <div className={`${styles.right}`}>
          {/* {title && (
            <Button className={styles.titleBtn} onClick={handleTitle}>
              {translate('callParam', title)}
            </Button>
          )} */}
          <div className={`${styles.rightBtnCenter}`}>
            <Button type="primary" onClick={handleClick} icon={<CheckOutlined />}>
              {translate('common', 'submit')}
            </Button>
            {/* <Button type="default" className={styles.resetBtn} icon={<RedoOutlined />}>
              {translate('common', 'reset')}
            </Button> */}
          </div>
        </div>
      </div>
    );
  };
  return (
    <>
      <FormMaintenance childNode={childNode()} />
      {/* <CommonModal
        type={actionType}
        open={open}
        content={content}
        onClose={() => setOpen(false)}
        onSubmit={handleModalSubmit}
      /> */}
    </>
  );
};
export default memo(DebtCollectionCom);
