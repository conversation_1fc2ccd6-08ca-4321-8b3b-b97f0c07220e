import { intlConst } from '@/hooks/useIntlCustom';

const prefix = 'callParam';

export default {
  tableData: {
    MESSAGE_CALL_MODAL: [
      {
        id: '1',
        name: intlConst.formatMessage(prefix, 'customerName1'),
        tel: 13685756431,
        template: intlConst.formatMessage(prefix, 'smsTemplate1'),
      },
      {
        id: '2',
        name: intlConst.formatMessage(prefix, 'customerName2'),
        tel: 13625891236,
        template: intlConst.formatMessage(prefix, 'smsTemplate2'),
      },
      {
        id: '3',
        name: intlConst.formatMessage(prefix, 'customerName3'),
        tel: 13669236738,
        template: intlConst.formatMessage(prefix, 'smsTemplate3'),
      },
    ],
    PHONE_CALL_MODAL: [
      {
        id: '1',
        name: intlConst.formatMessage(prefix, 'customerName1'),
        tel: 13685756431,
        guanxi: intlConst.formatMessage(prefix, 'relationColleague'),
      },
      {
        id: '2',
        name: intlConst.formatMessage(prefix, 'customerName2'),
        tel: 13625891236,
        guanxi: intlConst.formatMessage(prefix, 'relationWife'),
      },
      {
        id: '3',
        name: intlConst.formatMessage(prefix, 'customerName3'),
        tel: 13669236738,
        guanxi: intlConst.formatMessage(prefix, 'relationMother'),
      },
    ],
  },
  columns: {
    MESSAGE_CALL_MODAL: [
      {
        key: 'name',
        title: intlConst.formatMessage(prefix, 'name'),
        dataIndex: 'name',
        width: 80,
      },
      {
        key: 'tel',
        title: intlConst.formatMessage(prefix, 'phone'),
        dataIndex: 'tel',
      },
      {
        key: 'template',
        title: intlConst.formatMessage(prefix, 'smsTemplate'),
        dataIndex: 'template',
      },
    ],
    PHONE_CALL_MODAL: [
      {
        key: 'name',
        title: intlConst.formatMessage(prefix, 'name'),
        dataIndex: 'name',
      },
      {
        key: 'tel',
        title: intlConst.formatMessage(prefix, 'phone'),
        dataIndex: 'tel',
      },
      {
        key: 'guanxi',
        title: intlConst.formatMessage(prefix, 'relation'),
        dataIndex: 'guanxi',
      },
    ],
  },
  message: {
    MESSAGE_CALL_MODAL: intlConst.formatMessage(prefix, 'smsSendSuccess'),
    PHONE_CALL_MODAL: intlConst.formatMessage(prefix, 'callSuccess'),
  },
  URL: {
    MESSAGE_CALL_MODAL: 'wcs/sms/create',
    PHONE_CALL_MODAL: 'wcs/tdc/create',
  },
  caseCode: {
    MESSAGE_CALL_MODAL: 'CS20250125-002',
    PHONE_CALL_MODAL: 'CS20250125-001',
  },
  options: [
    {
      label: intlConst.formatMessage(prefix, 'optionPromiseRepayment'),
      value: 'CMLM',
    },
    {
      label: intlConst.formatMessage(prefix, 'optionContactLost'),
      value: 'CMLM1',
    },
    {
      label: intlConst.formatMessage(prefix, 'optionNoAnswer'),
      value: 'CMNA',
    },
    {
      label: intlConst.formatMessage(prefix, 'optionSevereIllness'),
      value: 'WCYH',
    },
  ],
};
