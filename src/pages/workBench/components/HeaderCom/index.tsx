import qianchu from '@/assets/images/qianchu.png';
import qiehuanmoshi from '@/assets/images/qiehuanmoshi.png';
import * as iconList from '@ant-design/icons';
import { Button, Select } from 'antd';
import classNames from 'classnames';
import { FC, memo } from 'react';
import WORKBENCH_CONSTANTS from '../../workBench/constansts';
import styles from './index.module.css';

const userInfo = {
  name: '王少杰',
  phone: '151****3123',
};

const statusOptions = [
  { label: '空闲', value: '1' },
  { label: '忙碌', value: '2' },
];

const currentMode = '预测试';

interface IconButtonProps {
  src: string;
  title: string;
  className?: string;
  onClick?: () => void;
}

const IconButton: FC<IconButtonProps> = ({ src, title, className, onClick }) => (
  <img className={className} src={src} title={title} onClick={onClick} />
);

const HeaderCom: FC = () => {
  return (
    <div className={classNames('app-block', 'm-b', styles.header)}>
      <div className={styles.leftContent}>
        {WORKBENCH_CONSTANTS.HEADER_PHONE_ENUMS?.map((item: any, idx) => {
          return (
            <div
              className={`flex-col flex-align-center flex-justify-around ${styles.icons} ${idx === 2 ? styles.selected : ''}`}
            >
              <p className="icon-text">{iconList[item.key].render()}</p>
              <p>{item.value}</p>
            </div>
          );
        })}
        {/* <div className="flex-col flex-align-center flex-justify-around">
          <div style={{ marginBottom: 5 }}>{userInfo.name}</div>
          <div>{userInfo.phone}</div>
        </div>
        <IconButton className={styles.phone} src={jutingdianhua} title="挂断" />
        <IconButton className={styles.phone} src={dianhua} title="接听" />
        <IconButton className={styles.phone} src={qiehuan} title="切换下一个" /> */}
      </div>
      <div className={classNames('flex-col', 'flex-align-center', 'flex-justify-around', styles.right)}>
        <div>
          <span>LiXiang：</span>
          <Select defaultValue="1" options={statusOptions} style={{ marginRight: 12 }} />
          <Button color="pink" variant="outlined">
            已签入
          </Button>
          <IconButton className={styles.qianchu} src={qianchu} title="签出" />
        </div>
        <div style={{ marginTop: 12 }}>
          <span>当前模式：{currentMode}</span>
          <IconButton className={styles.qiehuanmoshi} src={qiehuanmoshi} title="切换" />
        </div>
      </div>
    </div>
  );
};

export default memo(HeaderCom);
