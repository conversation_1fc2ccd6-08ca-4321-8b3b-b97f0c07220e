.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.leftContent {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 0.6rem;
}
.phone {
  width: 24px;
  height: 24px;
}
.qianchu {
  width: 24px;
  height: 24px;
  cursor: pointer;
  margin-left: 6px;
}
.qiehuanmoshi {
  width: 16px;
  height: 16px;
  cursor: pointer;
  margin-left: 6px;
}
.centerIcon {
  display: flex;
  gap: 0.6rem;
}
.icons {
  padding: 5px 2px;
  cursor: pointer;
  border-radius: 8px;
  transition: background 0.3s;
}
.icons.selected {
  background: #e3f0ff; /* 你可以换成你喜欢的高亮色 */
}
.icons:hover {
  background: linear-gradient(90deg, #a1c4fd 0%, #c2e9fb 100%);
}
.centerText {
  margin-left: 2rem;
}
.cneterText {
  padding-left: 1rem;
}
.right .badge {
  padding-left: 1rem;
  cursor: pointer;
}
.caseCount {
  padding: 0.5rem 1rem;
  background: #d9d9d9;
  border-radius: 0.4rem;
  font-size: 1rem;
}
