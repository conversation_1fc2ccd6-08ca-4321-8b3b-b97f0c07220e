import { I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import { intlConst } from '@/hooks/useIntlCustom';

export const pageConfig = {
  // 国际化前缀
  prefix: I18N_COMON_PAGENAME.LIMIT_PARAM,
  // 页面标题
  cardTitle: 'node',
  // 列表字段
  columns1: [
    {
      title: intlConst.formatMessage('callParam', 'queueName'),
      dataIndex: 'aa1',
      key: 'aa1',
    },
    {
      title: intlConst.formatMessage('callParam', 'riskPool'),
      dataIndex: 'aa2',
      key: 'aa2',
    },
    {
      title: intlConst.formatMessage('callParam', 'pendingCount'),
      dataIndex: 'aa3',
      key: 'aa3',
    },
    {
      title: intlConst.formatMessage('callParam', 'effectiveWorkload'),
      dataIndex: 'aa4',
      key: 'aa4',
    },
    {
      title: intlConst.formatMessage('callParam', 'invalidWorkload'),
      dataIndex: 'aa5',
      key: 'aa5',
    },
    {
      title: intlConst.formatMessage('callParam', 'caseProcessingVolume'),
      dataIndex: 'aa6',
      key: 'aa6',
    },
  ],
  columns2: [
    {
      title: intlConst.formatMessage('callParam', 'queueName'),
      dataIndex: 'bb2',
      key: 'bb2',
    },
    {
      title: intlConst.formatMessage('callParam', 'realTimeRepaymentAmount'),
      dataIndex: 'bb1',
      key: 'bb1',
    },
    {
      title: intlConst.formatMessage('callParam', 'households'),
      dataIndex: 'bb3',
      key: 'bb3',
    },
  ],
  columns3: [
    {
      title: intlConst.formatMessage('callParam', 'taskCategory'),
      dataIndex: 'cc1',
      key: 'cc1',
    },
    {
      title: intlConst.formatMessage('callParam', 'initiator'),
      dataIndex: 'cc2',
      key: 'cc2',
    },
    {
      title: intlConst.formatMessage('callParam', 'initiationTime'),
      dataIndex: 'cc3',
      key: 'cc3',
    },
  ],
  columns4: [
    {
      title: intlConst.formatMessage('callParam', 'transferTime'),
      dataIndex: 'dd1',
      key: 'dd1',
    },
    {
      title: intlConst.formatMessage('callParam', 'initiator'),
      dataIndex: 'dd2',
      key: 'dd2',
    },
    {
      title: intlConst.formatMessage('callParam', 'orgCustomerNumber'),
      dataIndex: 'dd3',
      key: 'dd3',
    },
    {
      title: intlConst.formatMessage('callParam', 'transferReason'),
      dataIndex: 'dd4',
      key: 'dd4',
    },
  ],
  dataSource1: [
    {
      aa1: 'Phone Collection Team A',
      aa2: 'DCDFX0A',
      aa3: 2678,
      aa4: 2789,
      aa5: 67,
      aa6: 2856,
    },
    {
      aa1: 'Phone Collection Team B',
      aa2: 'DCDFX0B',
      aa3: 2532,
      aa4: 2672,
      aa5: 78,
      aa6: 2750,
    },
    {
      aa1: 'Phone Collection Team C',
      aa2: 'DCDFX0C',
      aa3: 2669,
      aa4: 2733,
      aa5: 89,
      aa6: 2822,
    },
    {
      aa1: 'Phone Collection Team D',
      aa2: 'DCDFX0D',
      aa3: 2689,
      aa4: 2726,
      aa5: 67,
      aa6: 2793,
    },
    {
      aa1: 'Outsourcing Team A',
      aa2: 'WFZFX0A',
      aa3: 1561,
      aa4: 1689,
      aa5: 245,
      aa6: 1934,
    },
    {
      aa1: 'Outsourcing Team B',
      aa2: 'WFZFX0B',
      aa3: 1690,
      aa4: 1623,
      aa5: 201,
      aa6: 1824,
    },
    {
      aa1: 'Litigation Team',
      aa2: 'SZZFX01',
      aa3: 679,
      aa4: 721,
      aa5: 108,
      aa6: 829,
    },
  ],
  dataSource2: [
    {
      bb1: 222000,
      bb2: 'Phone Collection Team A',
      bb3: 2781,
    },
    {
      bb1: 219800,
      bb2: 'Phone Collection Team B',
      bb3: 2822,
    },
    {
      bb1: 217600,
      bb2: 'Phone Collection Team C',
      bb3: 2793,
    },
    {
      bb1: 215400,
      bb2: 'Phone Collection Team D',
      bb3: 1934,
    },
    {
      bb1: 213200,
      bb2: 'Outsourcing Team A',
      bb3: 1940,
    },
    {
      bb1: 211000,
      bb2: 'Outsourcing Team B',
      bb3: 1683,
    },
    {
      bb1: 208800,
      bb2: 'Litigation Team',
      bb3: 1426,
    },
  ],
  dataSource3: [
    {
      cc1: 'Tag Management',
      cc2: 'Shen Junze',
      cc3: '2025/3/13 15:36',
    },
    {
      cc1: 'Tag Management',
      cc2: 'Ma Qiming',
      cc3: '2025/3/14 9:36',
    },
    {
      cc1: 'Rule Management',
      cc2: 'Qian Yuchen',
      cc3: '2025/3/13 15:30',
    },
    {
      cc1: 'Tag Management',
      cc2: 'Xu Zichen',
      cc3: '2025/3/13 15:36',
    },
    {
      cc1: 'Rule Management',
      cc2: 'Liu Yichen',
      cc3: '2025/3/14 9:36',
    },
    {
      cc1: 'Node Management',
      cc2: 'Zheng Haoxuan',
      cc3: '2025/3/13 15:30',
    },
    {
      cc1: 'Node Management',
      cc2: 'Gu Chenyang',
      cc3: '2024/10/13 15:30',
    },
  ],
};
