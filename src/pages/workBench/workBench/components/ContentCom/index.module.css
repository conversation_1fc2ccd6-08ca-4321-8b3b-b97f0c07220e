.content {
  height: -webkit-calc(100% - 66px);
}
.carouselDtoss li {
  background-color: blue;
}
.carouselDtoss li::after {
  background-color: red !important; /* 非激活状态的 dots 颜色 */
}
.contentLeft {
  width: 85%;
  overflow: scroll;
  margin-right: 1rem;
}
.contentRight {
  width: 15%;
  padding: 0.5rem;
  background: #fff;
  border-radius: 0.5rem;
}
.table .tableContent {
  margin-top: 12px;
  border-radius: 5px;
  padding: 1rem 1rem 0;
  background-color: #fff;
}
.tableCallStyle {
  width: 100%;
}
.table .tableCallStyleLeft {
  margin-right: 12px;
}
.tableContentFont .ant-table-wrapper .ant-table .ant-table-small {
  font-size: 12px;
}
.tableContentNum {
  color: #40d6c6;
  padding: 6px;
}
.rightText {
  min-height: 32px;
  color: #fff;
  margin-bottom: 12px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
}
.knowledge {
  background-color: #32b372;
}
.task {
  background-color: #ff6f61;
}
.limit {
  background-color: #ffb64d;
}
.remark {
  color: #ff0000;
  font-weight: 600;
}
.message {
  background-color: #40d6c6;
}
.divider {
  margin: 12px 0;
}
.congritulation {
  background-color: #fff;
  color: #000;
}
