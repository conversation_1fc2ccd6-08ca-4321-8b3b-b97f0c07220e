import * as iconList from '@ant-design/icons';
import { Button, Descriptions } from 'antd';
import { FC, memo } from 'react';
import WORKBENCH_CONSTANTS from '../../constansts/index';
import styles from './index.module.css';
type FieldType = {
  username?: string;
  password?: string;
  remember?: string;
};
const HeaderCom: FC = () => {
  return (
    <div className={`app-block m-b ${styles.header}`}>
      <div className={styles.leftContent}>
        {WORKBENCH_CONSTANTS.HEADER_PHONE_ENUMS?.map((item: any) => {
          return (
            <div className={`flex-col flex-align-center flex-justify-around ${styles.icons}`}>
              <p className="icon-text">{iconList[item.key].render()}</p>
              <p>{item.value}</p>
            </div>
          );
        })}
      </div>
      <div className={styles.centerText}>
        <Descriptions
          bordered={false}
          size="small"
          column={2}
          items={WORKBENCH_CONSTANTS.DSCRIPTION_ITEMS}
          style={{ width: '400px' }}
        />
      </div>
      <div className={`flex ${styles.right}`}>
        {/* {WORKBENCH_CONSTANTS.BADGES_DATA?.map((item: any) => {
          return (
            <span className={styles.badge}>
              <Badge count={item.count} style={{ backgroundColor: item.color }}>
                <div className={styles.caseCount}>{item.text}</div>
              </Badge>
            </span>
          );
        })} */}
        <Button type="primary">抢案</Button>
      </div>
    </div>
  );
};
export default memo(HeaderCom);
