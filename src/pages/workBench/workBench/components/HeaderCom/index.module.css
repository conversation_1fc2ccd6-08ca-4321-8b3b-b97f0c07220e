.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.leftContent {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 0.6rem;
}

.centerIcon {
  display: flex;
  gap: 0.6rem;
}
.icons {
  padding: 5px 2px;
  cursor: pointer;
}
.centerText {
  margin-left: 2rem;
}
.cneterText {
  padding-left: 1rem;
}
.right .badge {
  padding-left: 1rem;
  cursor: pointer;
}
.caseCount {
  padding: 0.5rem 1rem;
  background: #d9d9d9;
  border-radius: 0.4rem;
  font-size: 1rem;
}
