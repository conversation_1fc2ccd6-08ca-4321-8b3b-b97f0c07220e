// 用户中心/短信催收工作台
import urlConstants from '@/constants/urlConstants';
import CallBench from '@/materials/callBench';
import commonService from '@/services/common';
import { FC, memo, Suspense, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DebtCollectionCom from '../components/DebtCollectionCom';
import HeaderCom from '../components/HeaderCom';
import CustomLayoutCom from './components/CustomLayoutCom';
import mockData from './mockData';

const debtData = {
  title: 'dialPhone',
  actionType: 'PHONE_CALL_MODAL',
};

const CollectorBench: FC = () => {
  const location = useLocation();
  const [formData, setFormData] = useState<any>({});
  const [open, setOpen] = useState(false);

  const caseId = location.state?.caseId;
  useEffect(() => {
    if (caseId) {
      getCaseFormData();
    }
  }, [caseId]);

  // 获取案件信息
  const getCaseFormData = async () => {
    const res: any = await commonService.getEditPostBiz({
      url: urlConstants.CASE_BASE_INFO.LIST,
      caseId,
      model: 'D',
    });
    const data = res?.data?.filter((item) => item.currState === 'P' || item.caseState === '1') || [];
    setFormData(data[0]);
  };

  const handleCusLayout = (val) => {
    console.log('handleCusLayout', val);
  };

  return (
    <Suspense>
      <HeaderCom />
      <CallBench
        dataSource={{
          ...mockData.formData,
          infoForm1: { ...formData },
          infoForm2: { ...formData },
          infoForm3: { ...formData },
        }}
        workBenchName={1}
        onSubmit={() => getCaseFormData()}
        onCusLayout={(val) => setOpen(val)}
      />
      <DebtCollectionCom debtData={debtData} formData={formData} onSubmit={() => getCaseFormData()} />
      <CustomLayoutCom open={open} onSubmit={handleCusLayout} onCancel={() => setOpen(false)} />
    </Suspense>
  );
};

export default memo(CollectorBench);
