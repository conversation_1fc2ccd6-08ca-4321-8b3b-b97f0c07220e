.drawerBody {
  display: flex;
  height: calc(100vh - 200px);
}

.leftPane {
  flex: 1;
  border-right: 1px solid #f0f0f0;
  padding-right: 16px;
}

.searchBar {
  margin-bottom: 16px;
}

.searchInput {
  margin-bottom: 8px;
}

.between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tree {
  height: calc(100% - 80px);
  overflow: auto;
}

.rightPane {
  flex: 1;
  padding-left: 16px;
}

.listItem {
  padding: 8px 0;
}

.list {
  height: calc(100% - 60px);
  overflow: auto;
}

/* 自定义布局组件样式 */
.sectionContainer {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
}

.sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.sectionTitle {
  margin: 0;
  display: flex;
  align-items: center;
}

.editIcon {
  margin-left: 8px;
  color: #1890ff;
}

.fieldSelect {
  width: 160px;
}

.searchContainer {
  margin-bottom: 12px;
}

/* 左右布局容器 */
.fieldsLayoutContainer {
  display: flex;
  gap: 24px;
  align-items: flex-start;
  width: 100%; /* 确保容器占满宽度 */
}

/* 左侧已选字段区域 */
.selectedFieldsSection {
  flex: 1; /* 占一半宽度 */
  min-width: 0;
}

.selectedFieldsLabel {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
  display: block;
}

.selectedFieldsWrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.emptyFieldsPlaceholder {
  text-align: center;
  color: #8c8c8c;
  padding: 32px 0;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
  width: 100%;
}

/* 中间分割线 */
.divider {
  width: 1px;
  background-color: #d9d9d9;
  margin: 0 12px;
  align-self: stretch;
}

/* 右侧待选字段区域 */
.availableFieldsSection {
  flex: 1; /* 占一半宽度 */
  min-width: 0;
}

.availableFieldsLabel {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
  display: block;
}

.availableFieldsWrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

/* 字段卡片样式 */
.fieldCard {
  margin-bottom: 8px;
  position: relative;
  transition: all 0.3s ease;
  :global {
    .ant-card-body {
      padding: 4px;
      background-color: #e6f7ff; /* 浅蓝色背景，使其更明显 */
    }
  }
}

/* 在文件末尾添加以下样式 */
.deleteButton {
  color: #8c8c8c;
  cursor: pointer;
  margin-left: 4px;
  padding: 2px;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  min-height: 24px;
}

.deleteButton:hover {
  color: #ff4d4f;
  background-color: #fff2f0;
  transform: scale(1.1);
}

/* 确保删除按钮在卡片内正确定位 */
.fieldCardContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.fieldCardLeft {
  display: flex;
  align-items: center;
  cursor: grab;
  flex: 1;
}

.dragIcon {
  margin-right: 8px;
  color: #8c8c8c;
}
