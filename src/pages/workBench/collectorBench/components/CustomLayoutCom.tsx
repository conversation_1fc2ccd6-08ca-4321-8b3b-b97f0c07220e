import { I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import {
  DEFAULT_LAYOUT,
  FieldItem,
  SectionData,
} from '@/pages/workBench/collectorBench/components/customLayoutConstants';
import { CloseOutlined, DragOutlined, EditOutlined } from '@ant-design/icons';
import { closestCenter, DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Card, Col, Drawer, Input, Row, Select, Space, Tag, Typography } from 'antd';
import { FC, memo, useCallback, useState } from 'react';
import styles from './index.module.css';

const { Title } = Typography;
const { Option } = Select;

interface CustomLayoutComType {
  open: boolean;
  onSubmit: (layoutData: SectionData[]) => void;
  onCancel: (val: boolean) => void;
  initialLayout?: SectionData[];
}

// 可拖拽的字段卡片组件
const DraggableFieldCard: FC<{
  field: FieldItem;
  onRemove: (fieldId: string) => void;
}> = ({ field, onRemove }) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: field.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleRemoveClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onRemove(field.id);
  };

  return (
    <Card ref={setNodeRef} style={style} size="small" className={styles.fieldCard}>
      <div className={styles.fieldCardContent}>
        <div className={styles.fieldCardLeft} {...attributes} {...listeners}>
          <DragOutlined className={styles.dragIcon} />
          <span>{field.label}</span>
        </div>
        <CloseOutlined className={styles.deleteButton} onClick={handleRemoveClick} />
      </div>
    </Card>
  );
};

const CustomLayoutCom: FC<CustomLayoutComType> = ({ open, onCancel, onSubmit, initialLayout = DEFAULT_LAYOUT }) => {
  const { translate } = useIntlCustom();
  const [layoutData, setLayoutData] = useState<SectionData[]>(initialLayout);
  const [searchTexts, setSearchTexts] = useState<{ [key: string]: string }>({});

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  // 添加字段到选中区域
  const handleAddField = useCallback((sectionId: string, field: FieldItem) => {
    setLayoutData((prev) =>
      prev.map((section) => {
        if (section.id === sectionId) {
          // 检查字段是否已经存在
          if (section.selectedFields.find((f) => f.id === field.id)) {
            return section;
          }
          return {
            ...section,
            selectedFields: [...section.selectedFields, field],
            availableFields: section.availableFields.filter((f) => f.id !== field.id),
          };
        }
        return section;
      }),
    );
  }, []);

  // 从选中区域移除字段
  const handleRemoveField = useCallback((sectionId: string, fieldId: string) => {
    setLayoutData((prev) =>
      prev.map((section) => {
        if (section.id === sectionId) {
          const fieldToRemove = section.selectedFields.find((f) => f.id === fieldId);
          if (fieldToRemove) {
            return {
              ...section,
              selectedFields: section.selectedFields.filter((f) => f.id !== fieldId),
              availableFields: [...section.availableFields, fieldToRemove],
            };
          }
        }
        return section;
      }),
    );
  }, []);

  // 拖拽排序处理 - 区域内排序
  const handleDragEnd = useCallback((event: any, sectionId: string) => {
    const { active, over } = event;

    if (active.id !== over?.id && over) {
      setLayoutData((prev) =>
        prev.map((section) => {
          // 只处理指定区域的拖拽
          if (section.id === sectionId) {
            const activeFieldIndex = section.selectedFields.findIndex((f) => f.id === active.id);
            const overFieldIndex = section.selectedFields.findIndex((f) => f.id === over.id);

            if (activeFieldIndex !== -1 && overFieldIndex !== -1) {
              // 在同一区域内进行排序
              const newFields = [...section.selectedFields];
              const [removed] = newFields.splice(activeFieldIndex, 1);
              newFields.splice(overFieldIndex, 0, removed);

              return {
                ...section,
                selectedFields: newFields,
              };
            }
          }

          return section;
        }),
      );
    }
  }, []);

  // 搜索字段
  const getFilteredAvailableFields = useCallback(
    (section: SectionData) => {
      const searchText = searchTexts[section.id] || '';
      if (!searchText) return section.availableFields;

      return section.availableFields.filter((field) => field.label.toLowerCase().includes(searchText.toLowerCase()));
    },
    [searchTexts],
  );

  // 提交
  const handleSubmit = () => {
    onSubmit(layoutData);
    onCancel(false);
  };

  // 关闭
  const handleClose = () => {
    onCancel(false);
  };

  return (
    <Drawer
      open={open}
      title={translate(I18N_COMON_PAGENAME.SYSTEM_MANAGE, 'customLayout')}
      onClose={handleClose}
      width={'80%'}
      extra={
        <Space>
          <Button onClick={handleClose}>{translate(I18N_COMON_PAGENAME.COMMON, 'cancel')}</Button>
          <Button type="primary" onClick={handleSubmit}>
            {translate(I18N_COMON_PAGENAME.COMMON, 'save')}
          </Button>
        </Space>
      }
    >
      <Row gutter={[0, 24]}>
        {layoutData.map((section) => (
          <Col span={24} key={section.id}>
            <div className={styles.sectionContainer}>
              {/* 标题区域 */}
              <div className={styles.sectionHeader}>
                <Title level={5} className={styles.sectionTitle}>
                  {section.title}
                  <EditOutlined className={styles.editIcon} />
                </Title>

                {/* 字段选择下拉框 */}
                <Select
                  placeholder={translate(I18N_COMON_PAGENAME.SYSTEM_MANAGE, 'selectField')}
                  className={styles.fieldSelect}
                  showSearch
                  onChange={(value) => {
                    const field = section.availableFields.find((f) => f.id === value);
                    if (field) {
                      handleAddField(section.id, field);
                    }
                  }}
                  value={undefined}
                >
                  {getFilteredAvailableFields(section).map((field) => (
                    <Option key={field.id} value={field.id}>
                      {field.label}
                    </Option>
                  ))}
                </Select>
              </div>

              {/* 字段区域 - 左右布局 */}
              <div className={styles.fieldsLayoutContainer}>
                {/* 左侧：已选字段区域 */}
                <div className={styles.selectedFieldsSection}>
                  <div className={styles.selectedFieldsLabel}>
                    {translate(I18N_COMON_PAGENAME.SYSTEM_MANAGE, 'selectedFields')}:
                  </div>
                  {section.selectedFields.length > 0 ? (
                    <div className={styles.selectedFieldsWrapper}>
                      <DndContext
                        sensors={sensors}
                        collisionDetection={closestCenter}
                        onDragEnd={(event) => handleDragEnd(event, section.id)}
                      >
                        <SortableContext
                          items={section.selectedFields.map((f) => f.id)}
                          strategy={verticalListSortingStrategy}
                        >
                          {section.selectedFields.map((field) => (
                            <DraggableFieldCard
                              key={field.id}
                              field={field}
                              onRemove={(fieldId) => handleRemoveField(section.id, fieldId)}
                            />
                          ))}
                        </SortableContext>
                      </DndContext>
                    </div>
                  ) : (
                    <div className={styles.emptyFieldsPlaceholder}>
                      {translate(I18N_COMON_PAGENAME.SYSTEM_MANAGE, 'noSelectedFields')}
                    </div>
                  )}
                </div>

                {/* 中间分割线 */}
                <div className={styles.divider} />

                {/* 右侧：待选字段区域 */}
                <div className={styles.availableFieldsSection}>
                  <div className={styles.availableFieldsLabel}>
                    {translate(I18N_COMON_PAGENAME.SYSTEM_MANAGE, 'availableFields')}:
                  </div>

                  {/* 字段搜索输入框 */}
                  <div className={styles.searchContainer}>
                    <Input
                      placeholder={translate(I18N_COMON_PAGENAME.SYSTEM_MANAGE, 'enterFieldName')}
                      value={searchTexts[section.id] || ''}
                      onChange={(e) =>
                        setSearchTexts((prev) => ({
                          ...prev,
                          [section.id]: e.target.value,
                        }))
                      }
                      allowClear
                    />
                  </div>

                  <div className={styles.availableFieldsWrapper}>
                    {getFilteredAvailableFields(section).map((field) => (
                      <Tag
                        key={field.id}
                        style={{ cursor: 'pointer' }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#e6f7ff';
                          e.currentTarget.style.color = '#1890ff';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = '';
                          e.currentTarget.style.color = '';
                        }}
                        onClick={() => handleAddField(section.id, field)}
                      >
                        {field.label}
                      </Tag>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </Col>
        ))}
      </Row>
    </Drawer>
  );
};

export default memo(CustomLayoutCom);
