export interface FieldItem {
  id: string;
  name: string;
  label: string;
  type: string;
}

export interface SectionData {
  id: string;
  title: string;
  selectedFields: FieldItem[];
  availableFields: FieldItem[];
}

// 催收信息字段
export const COLLECTION_FIELDS: FieldItem[] = [
  { id: 'customer_name', name: 'customerName', label: '客户姓名', type: 'string' },
  { id: 'case_number', name: 'caseNumber', label: '案件号', type: 'string' },
  { id: 'id_type', name: 'idType', label: '证件类型', type: 'string' },
  { id: 'entry_time', name: 'entryTime', label: '入催时间', type: 'date' },
  { id: 'exit_time', name: 'exitTime', label: '出催时间', type: 'date' },
  { id: 'last_case_time', name: 'lastCaseTime', label: '上次案件时间', type: 'date' },
  { id: 'module', name: 'module', label: '模块', type: 'string' },
];

// 客户信息字段
export const CUSTOMER_FIELDS: FieldItem[] = [
  { id: 'customer_name', name: 'customerName', label: '客户姓名', type: 'string' },
  { id: 'case_number', name: 'caseNumber', label: '案件号', type: 'string' },
  { id: 'id_type', name: 'idType', label: '证件类型', type: 'string' },
  { id: 'entry_time', name: 'entryTime', label: '入催时间', type: 'date' },
  { id: 'exit_time', name: 'exitTime', label: '出催时间', type: 'date' },
  { id: 'last_case_time', name: 'lastCaseTime', label: '上次案件时间', type: 'date' },
  { id: 'module', name: 'module', label: '模块', type: 'string' },
];

// 默认布局配置
export const DEFAULT_LAYOUT: SectionData[] = [
  {
    id: 'collection_info',
    title: '催收信息',
    selectedFields: [
      { id: 'customer_name', name: 'customerName', label: '客户姓名', type: 'string' },
      { id: 'case_number', name: 'caseNumber', label: '案件号', type: 'string' },
    ],
    availableFields: [
      { id: 'id_type', name: 'idType', label: '证件类型', type: 'string' },
      { id: 'entry_time', name: 'entryTime', label: '入催时间', type: 'date' },
      { id: 'exit_time', name: 'exitTime', label: '出催时间', type: 'date' },
      { id: 'last_case_time', name: 'lastCaseTime', label: '上次案件时间', type: 'date' },
      { id: 'module', name: 'module', label: '模块', type: 'string' },
    ],
  },
  {
    id: 'customer_info',
    title: '客户信息',
    selectedFields: [
      { id: 'customer_name', name: 'customerName', label: '客户姓名', type: 'string' },
      { id: 'case_number', name: 'caseNumber', label: '案件号', type: 'string' },
    ],
    availableFields: [
      { id: 'id_type', name: 'idType', label: '证件类型', type: 'string' },
      { id: 'entry_time', name: 'entryTime', label: '入催时间', type: 'date' },
      { id: 'exit_time', name: 'exitTime', label: '出催时间', type: 'date' },
      { id: 'last_case_time', name: 'lastCaseTime', label: '上次案件时间', type: 'date' },
      { id: 'module', name: 'module', label: '模块', type: 'string' },
    ],
  },
  {
    id: 'case_info',
    title: '案件信息',
    selectedFields: [
      { id: 'customer_name', name: 'customerName', label: '客户姓名', type: 'string' },
      { id: 'case_number', name: 'caseNumber', label: '案件号', type: 'string' },
    ],
    availableFields: [
      { id: 'id_type', name: 'idType', label: '证件类型', type: 'string' },
      { id: 'entry_time', name: 'entryTime', label: '入催时间', type: 'date' },
      { id: 'exit_time', name: 'exitTime', label: '出催时间', type: 'date' },
      { id: 'last_case_time', name: 'lastCaseTime', label: '上次案件时间', type: 'date' },
      { id: 'module', name: 'module', label: '模块', type: 'string' },
    ],
  },
];
