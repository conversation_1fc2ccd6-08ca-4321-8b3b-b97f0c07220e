# CustomLayoutCom 自定义布局组件

## 功能描述

这是一个自定义布局组件，允许用户通过拖拽和选择来配置三个区域的字段布局。每个区域都有独立的字段选择和排序功能。

## 主要特性

- 🎯 **三个独立区域**: 催收信息、客户信息、案件信息
- 🔍 **字段搜索**: 支持按字段名称搜索
- 📝 **字段选择**: 通过下拉框选择字段
- 🗑️ **字段删除**: 可删除已选择的字段
- 🎨 **拖拽排序**: 支持拖拽调整字段顺序
- 💾 **布局保存**: 保存自定义布局配置

## 使用方法

### 基本用法

```tsx
import CustomLayoutCom from './components/CustomLayoutCom';
import { SectionData } from '@/constants/customLayoutConstants';

const MyComponent = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [layoutData, setLayoutData] = useState<SectionData[]>([]);

  const handleSubmit = (data: SectionData[]) => {
    setLayoutData(data);
    console.log('保存的布局:', data);
  };

  return (
    <div>
      <Button onClick={() => setIsOpen(true)}>打开自定义布局</Button>

      <CustomLayoutCom
        open={isOpen}
        onCancel={(val) => setIsOpen(val)}
        onSubmit={handleSubmit}
        initialLayout={layoutData}
      />
    </div>
  );
};
```

### 组件属性

| 属性            | 类型                                  | 必填 | 默认值           | 说明                   |
| --------------- | ------------------------------------- | ---- | ---------------- | ---------------------- |
| `open`          | `boolean`                             | ✅   | -                | 控制抽屉是否打开       |
| `onCancel`      | `(val: boolean) => void`              | ✅   | -                | 关闭抽屉的回调函数     |
| `onSubmit`      | `(layoutData: SectionData[]) => void` | ✅   | -                | 提交布局数据的回调函数 |
| `initialLayout` | `SectionData[]`                       | ❌   | `DEFAULT_LAYOUT` | 初始布局数据           |

### 数据结构

```typescript
interface FieldItem {
  id: string; // 字段唯一标识
  name: string; // 字段名称
  label: string; // 字段显示标签
  type: string; // 字段类型
}

interface SectionData {
  id: string; // 区域唯一标识
  title: string; // 区域标题
  selectedFields: FieldItem[]; // 已选择的字段
  availableFields: FieldItem[]; // 可选择的字段
}
```

## 操作说明

### 添加字段

1. 在区域标题右侧的下拉框中选择要添加的字段
2. 字段会自动添加到"已选字段"区域

### 删除字段

1. 点击已选字段标签上的关闭按钮
2. 字段会从"已选字段"移除，回到"待选字段"

### 调整字段顺序

1. 拖拽"已选字段"区域中的字段卡片
2. 释放鼠标完成排序

### 搜索字段

1. 在"请输入字段名称"输入框中输入关键词
2. "待选字段"区域会实时过滤显示匹配的字段

## 样式定制

组件使用内联样式，可以通过修改 `src/constants/customLayoutConstants.ts` 文件中的常量来调整：

- 字段数据
- 默认布局
- 区域标题

## 依赖包

- `@dnd-kit/core`: 拖拽核心功能
- `@dnd-kit/sortable`: 排序功能
- `@dnd-kit/utilities`: 工具函数
- `antd`: UI组件库

## 注意事项

1. 确保已安装所有必要的依赖包
2. 组件宽度设置为1000px，适合大屏幕使用
3. 拖拽功能支持键盘和鼠标操作
4. 字段ID必须唯一，避免重复选择

