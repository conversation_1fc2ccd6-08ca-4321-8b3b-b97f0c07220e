export default {
  formData: {
    infoForm1: {
      name: '<PERSON>',
      gender: 'Male',
      branchNumber: '0001',
      billDay: '08',
      englishName: 'Jack',
      totalDebt: '0.00',
      riskLevel: 'Low',
      idNumber: '****************78',
      badMark: false,
      delayDays: 0,
      reAuditFlag: false,
      permanentCreditLimit: '10000.00',
      activeInstallmentFlag: 0,
      largeAccountFlag: false,
      twentyFourMonthDelayStatus: 'Normal',
      memo: '',
      companyName: 'Zhao <PERSON>n Technology Company',
      collectionRemark: '',
      customerId: 'CUST123456',
      vipLevel: 'Ordinary',
    },
    infoForm2: {
      name: '<PERSON>',
      gender: 'Male',
      branchNumber: '0001',
      subBranchNumber: '0002',
      documentValidity: '2025-12-31',
      birthDate: '1990-01-01',
      residenceDuration: '5 years',
      occupation: 'Software Engineer',
      companyName: 'Zhao <PERSON> Company',
      abCustomerGroup: 'A',
      currentBadFlagDomain: 'None',
      accountAuthorization: 'Authorized',
      lockCode: 'None',
      oldLockCodeStartDate: '2020-01-01',
      mobileNumber: '***********',
      email: '<EMAIL>',
      documentType: 'ID Card',
      education: "Bachelor's Degree",
      major: 'Computer Science',
      maritalStatus: 'Unmarried',
      cardholderType: 'Individual',
      temporaryCreditLimit: '5000',
      cardholderDifferenceSwitch: 'Off',
      statusChangeDate: '2023-01-01',
      guarantorPhone: '***********',
      deposit: '10000',
    },
    infoForm3: {
      homePhone: '010-********',
      mobilePhone: '***********',
      workPhone1: '020-********',
      workPhone2: '020-********',
      workPhone3: '020-********',
      emergencyContact1Name: 'Zhang San',
      emergencyContact1Phone: '***********',
      emergencyContact2Name: 'Li Si',
      emergencyContact2Phone: '***********',
      householdAddress: 'Chaoyang District, Beijing',
      homeAddress: 'Haidian District, Beijing',
      workAddress: 'Fengtai District, Beijing',
      currentResidence: 'Chaoyang District, Beijing',
      billingAddress: 'Haidian District, Beijing',
    },
    infoForm4: {
      institutionNumber: 'Institution 001',
      cardTransferNewAccountCurrency: 'RMB',
      creditCardHolderNumber: '****************',
      accountFreezeCode: 'FREEZE001',
      accountFreezeCodeReasonCode: 'Reason 001',
      accountBillDay: 1,
      accountFreezeStatus: 'Normal',
      waivePenaltyFlag: true,
      collectionExceptionFlag: true,
      accountCollectionCount: 1,
      currentMinimumRepaymentRemaining: 100,
      '150DayOverdueAmount': 100,
      overduePeriods: 1,
      '150DayOverdueCount': 1,
      initialConsumptionRemainingAmount: 100,
      unsecuredRepaymentAmount: 100,
      accountWithdrawalAmountAccumulated: 100,
      accountLastWithdrawalDate: '2023-01-01',
      pledgedConsumptionVoucherBalance: 100,
      specialProductFlag: true,
      accountHighestAmountDate: '2023-12-31',
      lastPeriodRepaymentDueDate: '2023-12-31',
      accountNumber: 'ACCOUNT001',
      cardTransferNewAccountNumber: 'Number001',
      accountToleranceSwitchFlag: true,
      oldAccountFreezeCode: 'OLD001',
      oldAccountFreezeCodeReasonCode: 'Reason 002',
      accountBirthdayValidDate: '2023-01-01',
      accountFreezeReasonCode: 'Reason 003',
      waiveOverlimitFeeFlag: true,
      accountCoreFlag: true,
      accountLastOverdueDate: '2023-01-01',
      '30DayOverdueAmount': 30,
      '180DayOverdueAmount': 180,
      '30DayOverdueCount': 30,
      '180DayOverdueCount': 180,
      withdrawalInitialBalance: 50,
      redemptionRepaymentOriginalBalance: 50,
      accountInterestWaiverFlag: true,
      lastConsumptionAmount: 10,
      pledgedWithdrawalVoucherBalance: 10,
      lastTransactionDate: '2023-01-01',
      currentBusinessDate: '2023-01-01',
      accountPin: 'PIN1234',
      accountStatus: 'Active',
      statusChangeDate: '2023-01-01',
      singleCurrencySettlementFlag: true,
      accountFreezeCodeRemarks: 'Remark 001',
      autoRepaymentAccount: true,
      nonLunarNewYearOverdueFlag: true,
      collectionReason: 'Reason 004',
      initialCollectionDate: '2023-01-01',
      '120DayOverdueAmount': 120,
      '120DayOverdueCount': 120,
      gracePeriodDate: '2023-01-01',
      accountConsumptionAmountAccumulated: 200,
      lastWithdrawalAmount: 20,
      currentRepaymentToleranceIndicator: 20,
      accountHighestBalance: 1000,
      exemptOverlimitFee: true,
    },
    infoForm5: {
      institutionNumber: 1,
      cardLevel: 'Advanced',
      specialProductFlag: true,
      cardholderInstitutionNumber: 1,
      cardFreezeCode: 'FREEZE002',
      cardPreviousFreezeCode: 'FREEZE003',
      newCardCurrencyAfterReplacement: 'CNY - Renminbi',
      annualFeeDiscountFlag: true,
      passwordIncorrectCount: 1,
      cardShortName: 'Zhang San',
      applicationFormSerialNumber: 'Serial 001',
      marketingStaffEmployeeNumber: 'Employee 001',
      closeDate: '2023-12-31',
      cardTransactionScopeFlag: true,
      accountNumber: 'ACCOUNT001',
      primarySecondaryCardFlag: true,
      cardIssuingCode: 'Code 001',
      cardholderNumber: 'Number001',
      cardFreezeReasonCode: 'Reason 005',
      cardPreviousFreezeReasonCode: 'Reason 006',
      newCardProductNumberAfterReplacement: 'Product 001',
      waiveAnnualFeeFlag: true,
      passwordIncorrectDate: '2023-01-01',
      cardholderName: 'Li Si',
      communicationCode: 'Code 002',
      marketingStaffName: 'Wang Wu',
      cardFeeParameterCode: 'Code 001',
      cardActivationStatus: 'Activated',
      currency: 'CNY - Renminbi',
      cardQuality: 'Excellent',
      validityPeriod: '2024-12-31',
      supplementaryCardholderInstitutionNumber: 1,
      cardFreezeCodeMaintenanceDate: '2023-12-31',
      cardPreviousFreezeCodeEndDate: '2023-12-31',
      newCardNumberAfterReplacement: 'Number002',
      annualFeeCollectionDate: '2023-12-31',
      passwordFlag: true,
      printableName: 'Company Name',
      cardPickupMethod: 'Self-pickup',
      cardExpirationDate: '2024-12-31',
      cardProcessingParameterCode: 'Code 002',
      cardActivationDate: '2023-12-31',
      cardProduct: 'Product 001',
      cardStatusChangeDate: '2023-12-31',
      cardIssuingOperationFlag: true,
      cardIssuingDate: '2023-12-31',
      previousCardIssuingDate: '2023-12-31',
      cardIssuingRequestAccumulated: 1,
      oldCardNumber: 'Number003',
      cardEndDate: '2023-12-31',
      exemptionFlag: true,
      coBrandedCardPartnerCode: 'Code 003',
      cardCancellationStaffName: 'Zhao Liu',
      cardActivationMethod: 'Manual',
    },
  },
  // Right anchor data
  anchorData: [
    {
      key: 'infoForm1',
      href: '#infoForm1',
      title: 'Collection Information',
    },
    {
      key: 'infoForm2',
      href: '#infoForm2',
      title: 'Customer Information',
    },
    {
      key: 'infoForm3',
      href: '#infoForm3',
      title: 'Contact Information',
    },
    {
      key: 'infoForm4',
      href: '#infoForm4',
      title: 'Account Information',
    },
    {
      key: 'infoForm5',
      href: '#infoForm5',
      title: 'Card Information',
    },
  ],
  // Left menu data
  segmentedData: [
    {
      label: { backgroundColor: '#f56a00', text: 'Transfer', title: 'Transfer Case' },
      value: 'A',
    },
    {
      label: { backgroundColor: '#FFEB3B', text: 'Advance', title: 'Advance Payment' },
      value: 'B',
    },
    {
      label: { backgroundColor: '#4CAF50', text: 'Stop', title: 'Stop Payment' },
      value: 'C',
    },
    {
      label: { backgroundColor: '#FF9800', text: 'Reduce', title: 'Reduction' },
      value: 'D',
    },
    {
      label: { backgroundColor: '#2196F3', text: 'Guarantee', title: 'Guarantee Deposit Offset Debt' },
      value: 'E',
    },
    {
      label: { backgroundColor: '#87d068', text: 'Same', title: 'Same-Name Account Adjustment' },
      value: 'F',
    },
    {
      label: { backgroundColor: '#f56a00', text: 'Remove', title: 'Remove Strong Collection Mark' },
      value: 'G',
    },
    {
      label: { backgroundColor: '#FFEB3B', text: 'Negotiate', title: 'Negotiate Installment Plan' },
      value: 'H',
    },
    {
      label: { backgroundColor: '#4CAF50', text: 'Light', title: 'Light Financial Order' },
      value: 'I',
    },
    {
      label: { backgroundColor: '#FF9800', text: 'Repair', title: 'Information Repair' },
      value: 'J',
    },
    {
      label: { backgroundColor: '#2196F3', text: 'SMS', title: 'SMS Message' },
      value: 'K',
    },
    {
      label: { backgroundColor: '#87d068', text: 'WeChat', title: 'WeChat Message' },
      value: 'L',
    },
    {
      label: { backgroundColor: '#4CAF50', text: 'APP', title: 'APP Operation' },
      value: 'M',
    },
    {
      label: { backgroundColor: '#FF9800', text: 'Email', title: 'Email Message' },
      value: 'N',
    },
    {
      label: { backgroundColor: '#2196F3', text: 'Letter', title: 'Letter Message' },
      value: 'O',
    },
  ],
};
