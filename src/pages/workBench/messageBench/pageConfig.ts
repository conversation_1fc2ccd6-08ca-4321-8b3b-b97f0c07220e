import dictConstants from '@/constants/dictConstants';
import { COMPONENT_TYPE, DATE_FORMATE, TIME_FORMATE } from '@/constants/publicConstant';
import dayjs from 'dayjs';

// 页面表单模块配置
export const descData = [
  {
    props: {
      title: '催收信息概览',
      column: 4,
    },
    type: COMPONENT_TYPE.FORM,
    key: 'infoForm1',
    columns: [
      {
        title: '案件号',
        key: 'caseCode',
        dataIndex: 'caseCode',
      },
      {
        title: '客户号',
        key: 'orgCustNbr',
        dataIndex: 'orgCustNbr',
        // editable: 'true',
      },
      {
        title: '客户名称',
        key: 'custName',
        dataIndex: 'custName',
      },
      {
        title: '证件类型',
        key: 'icType',
        dataIndex: 'icType',
      },
      {
        title: '证件号',
        key: 'custIc',
        dataIndex: 'custIc',
      },
      {
        title: '核销标',
        key: 'chargoffFlag',
        dataIndex: 'chargoffFlag',
      },
      {
        title: '入催次数',
        key: 'countOdue',
        dataIndex: 'countOdue',
      },
      {
        title: '入催时间',
        key: 'dteIntoCollection',
        dataIndex: 'dteIntoCollection',
        render: (record) => dayjs(record.dteIntoCollection).format(TIME_FORMATE),
      },
      {
        title: '出催时间',
        key: 'dteOutCollection',
        dataIndex: 'dteOutCollection',
        render: (record) => dayjs(record.dteOutCollection).format(TIME_FORMATE),
      },
      {
        title: '首次入催时间',
        key: 'firstDate',
        dataIndex: 'firstDate',
        render: (record) => dayjs(record.firstDate).format(TIME_FORMATE),
      },
      {
        title: '上案件号',
        key: 'lastCaseCode',
        dataIndex: 'lastCaseCode',
      },
      {
        title: '上次案件时间',
        key: 'lastCaseDate',
        dataIndex: 'lastCaseDate',
        render: (record) => dayjs(record.lastCaseDate).format(TIME_FORMATE),
      },
      {
        title: '诉讼日期',
        key: 'lawDate',
        dataIndex: 'lawDate',
        render: (record) => dayjs(record.lawDate).format(DATE_FORMATE),
      },
      {
        title: '诉讼标识',
        key: 'lawFlag',
        dataIndex: 'lawFlag',
      },
      {
        title: '模块',
        key: 'model',
        dataIndex: 'model',
        render: (text, record) => {
          const str = dictConstants.WORKBENCH_MODEL.find((item) => item.key === record?.model)?.value;
          return record?.model ? `${record?.model}-${str}` : record?.model;
        },
      },
      {
        title: '当前状态',
        key: 'currState',
        dataIndex: 'currState',
        render: (text, record) => {
          const str = dictConstants.WORKBENCH_CURR_STATE.find((item) => item.key === record?.currState)?.value;
          return record?.currState ? `${record?.currState}-${str}` : record?.currState;
        },
      },
      {
        title: '案件状态',
        key: 'caseState',
        dataIndex: 'caseState',
      },
      {
        title: '风险池编号',
        key: 'teamCode',
        dataIndex: 'teamCode',
      },
      {
        title: '最近行动时间',
        key: 'lastOpeTime',
        dataIndex: 'lastOpeTime',
        render: (record) => dayjs(record.lastOpeTime).format(TIME_FORMATE),
      },
      {
        title: '入组日期',
        key: 'inTeamDate',
        dataIndex: 'inTeamDate',
        render: (record) => dayjs(record.inTeamDate).format(DATE_FORMATE),
      },
      {
        title: '风险池原因码',
        key: 'reasonCode',
        dataIndex: 'reasonCode',
      },
      {
        title: '失联决策代码',
        key: 'noneContactRuleCode',
        dataIndex: 'noneContactRuleCode',
      },
      {
        title: '联系情况',
        key: 'contactCode',
        dataIndex: 'contactCode',
      },
      {
        title: '人工联系次数',
        key: 'contactTimes',
        dataIndex: 'contactTimes',
      },
      {
        title: '结果完成时间',
        key: 'resultTime',
        dataIndex: 'resultTime',
        render: (record) => dayjs(record.resultTime).format(TIME_FORMATE),
      },
      {
        title: '呼入呼出',
        key: 'resultDirection',
        dataIndex: 'resultDirection',
      },
      {
        title: '尝试联系次数',
        key: 'attemptCount',
        dataIndex: 'attemptCount',
      },
      {
        title: '有效催收总数',
        key: 'callTotal',
        dataIndex: 'callTotal',
      },
      {
        title: '上一模板',
        key: 'lastModel',
        dataIndex: 'lastModel',
      },
      {
        title: '英文名',
        key: 'ename',
        dataIndex: 'ename',
      },
      {
        title: '性别',
        key: 'eusex',
        dataIndex: 'eusex',
      },
      {
        title: '账单类型',
        key: 'statementTypeAll',
        dataIndex: 'statementTypeAll',
      },
      {
        title: '账单日',
        key: 'billingCycle',
        dataIndex: 'billingCycle',
      },
      {
        title: '不良标识域',
        key: 'badnessCode',
        dataIndex: 'badnessCode',
      },
      {
        title: '总欠款',
        key: 'classIBalance',
        dataIndex: 'classIBalance',
      },
      {
        title: '风险等级',
        key: 'riskRank',
        dataIndex: 'riskRank',
      },
      {
        title: '延滞天数',
        key: 'delayDays',
        dataIndex: 'delayDays',
      },
      {
        title: '活跃分期标识',
        key: 'activeInstallmentFlag',
        dataIndex: 'activeInstallmentFlag',
      },
      {
        title: '大额账户标识',
        key: 'productType',
        dataIndex: 'productType',
      },
      {
        title: '24期状况',
        key: 'status24',
        dataIndex: 'status24',
      },
      {
        title: '委外标志',
        key: 'wcsOutcode',
        dataIndex: 'wcsOutcode',
      },
      {
        title: '手别',
        key: 'handType',
        dataIndex: 'handType',
      },
      // {
      //   title: "客户标签",
      //   key: "custTags",
      //   dataIndex: "custTags",
      //   // editable: 'true',
      // },
    ],
  },
  {
    props: {
      title: '客户信息',
      column: 4,
    },
    type: COMPONENT_TYPE.FORM,
    key: 'infoForm2',
    columns: [
      {
        title: '分行号',
        key: 'bankNbr',
        dataIndex: 'bankNbr',
      },
      {
        title: '支行号',
        key: 'branchNbr',
        dataIndex: 'branchNbr',
      },
      {
        title: '已重审不能开通',
        key: 'restateNotOpen',
        dataIndex: 'restateNotOpen',
      },
      {
        title: '永久信用额',
        key: 'crlimitPerm',
        dataIndex: 'crlimitPerm',
      },
      {
        title: '临时额度',
        key: 'crlimitTemp',
        dataIndex: 'crlimitTemp',
      },
      {
        title: '备忘事项',
        key: 'note',
        dataIndex: 'note',
      },
      {
        title: '催收备注',
        key: 'custWcsMemo',
        dataIndex: 'custWcsMemo',
      },
      {
        title: 'VIP标识域',
        key: 'vipcode',
        dataIndex: 'vipcode',
      },
      {
        title: '证件有效期',
        key: 'permIdExp',
        dataIndex: 'permIdExp',
      },
      {
        title: '出生日期',
        key: 'bornDate',
        dataIndex: 'bornDate',
        // render: (record) =>
        //   dayjs(record.bornDate).format(DATE_FORMATE),
      },
      {
        title: '居住类型',
        key: 'euTypeOfRes',
        dataIndex: 'euTypeOfRes',
      },
      {
        title: '居住年限',
        key: 'euPerOfRes',
        dataIndex: 'euPerOfRes',
      },
      {
        title: '职业',
        key: 'job',
        dataIndex: 'job',
      },
      {
        title: '单位名称',
        key: 'employer',
        dataIndex: 'employer',
      },
      {
        title: '年薪',
        key: 'income',
        dataIndex: 'income',
      },
      {
        title: 'AB客户群',
        key: 'abClass',
        dataIndex: 'abClass',
      },
      {
        title: '封锁码',
        key: 'blockcode',
        dataIndex: 'blockcode',
      },
      {
        title: '封锁码日期',
        key: 'blockDate',
        dataIndex: 'blockDate',
        // render: (record) => dayjs(record?.blockDate).format(TIME_FORMATE),
      },
      {
        title: '旧封锁码',
        key: 'prvBlockcode',
        dataIndex: 'prvBlockcode',
      },
      {
        title: '旧封锁码日期',
        key: 'prvBlockDate',
        dataIndex: 'prvBlockDate',
        // render: (record) => dayjs(record?.prvBlockDate).format(TIME_FORMATE),
      },
      {
        title: '手机号',
        key: 'telNum',
        dataIndex: 'telNum',
      },
      {
        title: '电子邮箱',
        key: 'email',
        dataIndex: 'email',
      },
      {
        title: '毕业学校名称',
        key: 'school',
        dataIndex: 'school',
      },
      {
        title: '学历',
        key: 'prexCustQualification',
        dataIndex: 'prexCustQualification',
      },
      {
        title: '专业名称',
        key: 'major',
        dataIndex: 'major',
      },
      {
        title: '婚姻状态',
        key: 'euMaritalStatus',
        dataIndex: 'euMaritalStatus',
      },
      {
        title: '持卡人类型',
        key: 'custType',
        dataIndex: 'custType',
      },
      {
        title: '持卡人状态',
        key: 'custStatus',
        dataIndex: 'custStatus',
      },
      {
        title: '持卡人状态改变日期',
        key: 'custStatusDate',
        dataIndex: 'custStatusDate',
        render: (record) => dayjs(record.custStatusDate).format(DATE_FORMATE),
      },
      {
        title: '担保人姓名',
        key: 'custGName',
        dataIndex: 'custGName',
      },
      {
        title: '担保人电话',
        key: 'custGPhone',
        dataIndex: 'custGPhone',
      },
      {
        title: '保证金',
        key: 'depositArea',
        dataIndex: 'depositArea',
      },
      {
        title: '创建（开户）日期',
        key: 'custOpenDate',
        dataIndex: 'custOpenDate',
        render: (record) => dayjs(record.custOpenDate).format(DATE_FORMATE),
      },
    ],
  },
  {
    props: {
      title: '联系信息',
      column: 3,
    },
    type: COMPONENT_TYPE.FORM,
    key: 'infoForm3',
    columns: [
      {
        title: '家庭电话',
        key: 'custPhone',
        dataIndex: 'custPhone',
      },
      {
        title: '备用联系电话',
        key: 'custBackPhone',
        dataIndex: 'custBackPhone',
      },
      {
        title: '单位电话1',
        key: 'custEmpTel1',
        dataIndex: 'custEmpTel1',
      },
      {
        title: '单位电话2',
        key: 'custEmpTel2',
        dataIndex: 'custEmpTel2',
      },
      {
        title: '单位电话3',
        key: 'custEmpTel3',
        dataIndex: 'custEmpTel3',
      },
      {
        title: '紧急联系人1关系',
        key: 'custGlRln1',
        dataIndex: 'custGlRln1',
      },
      {
        title: '紧急联系人1姓名',
        key: 'custGlNam1',
        dataIndex: 'custGlNam1',
      },
      {
        title: '紧急联系人1电话',
        key: 'custGlTel1',
        dataIndex: 'custGlTel1',
      },
      {
        title: '紧急联系人2关系',
        key: 'custGlRln2',
        dataIndex: 'custGlRln2',
      },
      {
        title: '紧急联系人2姓名',
        key: 'custGlNam2',
        dataIndex: 'custGlNam2',
      },
      {
        title: '紧急联系人2电话',
        key: 'custGlTel2',
        dataIndex: 'custGlTel2',
      },
      {
        title: '户籍地址',
        key: 'custHomeCityAddr',
        dataIndex: 'custHomeCityAddr',
      },
      {
        title: '家庭地址',
        key: 'custAddr',
        dataIndex: 'custAddr',
      },
      {
        title: '单位地址',
        key: 'custGEmpA',
        dataIndex: 'custGEmpA',
      },
      {
        title: '现住地',
        key: 'custmAddr',
        dataIndex: 'custmAddr',
      },
      {
        title: '账单地址',
        key: 'billAddr',
        dataIndex: 'billAddr',
      },
    ],
  },
  {
    props: {
      title: '账户信息',
      column: 4,
    },
    type: COMPONENT_TYPE.FORM,
    key: 'infoForm4',
    columns: [
      { title: '机构号', key: 'jiGouHao', dataIndex: 'jiGouHao' },
      {
        title: '卡转新账户币种',
        key: 'kaZhuanXinZhangHuBiZhong',
        dataIndex: 'kaZhuanXinZhangHuBiZhong',
      },
      {
        title: '信用卡持卡人号',
        key: 'xinYongKaChiKaRenHao',
        dataIndex: 'xinYongKaChiKaRenHao',
      },
      {
        title: '账户封锁码',
        key: 'zhangHuFengSuoMa',
        dataIndex: 'zhangHuFengSuoMa',
      },
      {
        title: '账户封锁码原因码',
        key: 'zhangHuFengSuoMaYuanYinMa',
        dataIndex: 'zhangHuFengSuoMaYuanYinMa',
      },
      { title: '账户单日', key: 'zhangHuDanRi', dataIndex: 'zhangHuDanRi' },
      {
        title: '账户冻结状态',
        key: 'zhangHuDongJieZhuangTai',
        dataIndex: 'zhangHuDongJieZhuangTai',
      },
      {
        title: '免收违约金标识',
        key: 'mianShouWeiYueJinBiaoShi',
        dataIndex: 'mianShouWeiYueJinBiaoShi',
      },
      {
        title: '催收委外标识',
        key: 'cuiShouWeiWaiBiaoShi',
        dataIndex: 'cuiShouWeiWaiBiaoShi',
      },
      {
        title: '账户入催次数',
        key: 'zhangHuRuCuiCiShu',
        dataIndex: 'zhangHuRuCuiCiShu',
      },
      {
        title: '本期最低还款剩余',
        key: 'benQiZuiDiHuanKuanShengYu',
        dataIndex: 'benQiZuiDiHuanKuanShengYu',
      },
      {
        title: '150天延期金额',
        key: '150TianYanQiJinE',
        dataIndex: '150TianYanQiJinE',
      },
      { title: '延期期数', key: 'yanQiQiShu', dataIndex: 'yanQiQiShu' },
      {
        title: '150天延期次数',
        key: '150TianYanQiCiShu',
        dataIndex: '150TianYanQiCiShu',
      },
      {
        title: '期初消费剩余金额',
        key: 'qiChuXiaoFeiShengYuJinE',
        dataIndex: 'qiChuXiaoFeiShengYuJinE',
      },
      {
        title: '未挂现还款金额',
        key: 'weiGuaXianHuanKuanJinE',
        dataIndex: 'weiGuaXianHuanKuanJinE',
      },
      {
        title: '账户取现金额累计',
        key: 'zhangHuQuXianJinELeiJi',
        dataIndex: 'zhangHuQuXianJinELeiJi',
      },
      {
        title: '账户上次取现日期',
        key: 'zhangHuShangCiQuXianRiQi',
        dataIndex: 'zhangHuShangCiQuXianRiQi',
        render: (record) => dayjs(record.zhangHuShangCiQuXianRiQi).format(DATE_FORMATE),
      },
      {
        title: '已挂消费贷调余额',
        key: 'yiGuaXiaoFeiDaiTiaoYuE',
        dataIndex: 'yiGuaXiaoFeiDaiTiaoYuE',
      },
      {
        title: '特殊产品标识',
        key: 'teShuChanPinBiaoShi',
        dataIndex: 'teShuChanPinBiaoShi',
      },
      {
        title: '账户最高金额日期',
        key: 'zhangHuZuiGaoJinERiQi',
        dataIndex: 'zhangHuZuiGaoJinERiQi',
        render: (record) => dayjs(record.zhangHuZuiGaoJinERiQi).format(DATE_FORMATE),
      },
      {
        title: '上期还款到期日期',
        key: 'shangQiHuanKuanDaoQiRiQi',
        dataIndex: 'shangQiHuanKuanDaoQiRiQi',
        render: (record) => dayjs(record.shangQiHuanKuanDaoQiRiQi).format(DATE_FORMATE),
      },
      { title: '账户号', key: 'zhangHuHao', dataIndex: 'zhangHuHao' },
      {
        title: '卡转新账户序号',
        key: 'kaZhuanXinZhangHuXuHao',
        dataIndex: 'kaZhuanXinZhangHuXuHao',
      },
      {
        title: '账户容差开关标识',
        key: 'zhangHuRongChaKaiGuanBiaoShi',
        dataIndex: 'zhangHuRongChaKaiGuanBiaoShi',
      },
      {
        title: '旧账户封锁码',
        key: 'jiuZhangHuFengSuoMa',
        dataIndex: 'jiuZhangHuFengSuoMa',
      },
      {
        title: '旧账户封锁码原因码',
        key: 'jiuZhangHuFengSuoMaYuanYinMa',
        dataIndex: 'jiuZhangHuFengSuoMaYuanYinMa',
      },
      {
        title: '账户生日有效日',
        key: 'zhangHuShengRiYouXiaoRi',
        dataIndex: 'zhangHuShengRiYouXiaoRi',
      },
      {
        title: '账户冻结原因码',
        key: 'zhangHuDongJieYuanYinMa',
        dataIndex: 'zhangHuDongJieYuanYinMa',
      },
      {
        title: '免收超限金标识',
        key: 'mianShouChaoXianJinBiaoShi',
        dataIndex: 'mianShouChaoXianJinBiaoShi',
      },
      {
        title: '账户核心标识',
        key: 'zhangHuHeXinBiaoShi',
        dataIndex: 'zhangHuHeXinBiaoShi',
      },
      {
        title: '账户上次延期日期',
        key: 'zhangHuShangCiYanQiRiQi',
        dataIndex: 'zhangHuShangCiYanQiRiQi',
        render: (record) => dayjs(record.zhangHuShangCiYanQiRiQi).format(DATE_FORMATE),
      },
      {
        title: '30天延期金额',
        key: '30TianYanQiJinE',
        dataIndex: '30TianYanQiJinE',
      },
      {
        title: '180天延期金额',
        key: '180TianYanQiJinE',
        dataIndex: '180TianYanQiJinE',
      },
      {
        title: '30天延期次数',
        key: '30TianYanQiCiShu',
        dataIndex: '30TianYanQiCiShu',
      },
      {
        title: '180天延期次数',
        key: '180TianYanQiCiShu',
        dataIndex: '180TianYanQiCiShu',
      },
      {
        title: '提现期初余额',
        key: 'tiXianQiChuYuE',
        dataIndex: 'tiXianQiChuYuE',
      },
      {
        title: '兑现还款原余额',
        key: 'duiXianHuanKuanYuanYuE',
        dataIndex: 'duiXianHuanKuanYuanYuE',
      },
      {
        title: '账户免息标识',
        key: 'zhangHuMianXiBiaoShi',
        dataIndex: 'zhangHuMianXiBiaoShi',
      },
      {
        title: '上次消费金额',
        key: 'shangCiXiaoFeiJinE',
        dataIndex: 'shangCiXiaoFeiJinE',
      },
      {
        title: '已挂现贷调余额',
        key: 'yiGuaXianDaiTiaoYuE',
        dataIndex: 'yiGuaXianDaiTiaoYuE',
      },
      {
        title: '上次交易日期',
        key: 'shangCiJiaoYiRiQi',
        dataIndex: 'shangCiJiaoYiRiQi',
        render: (record) => dayjs(record.shangCiJiaoYiRiQi).format(DATE_FORMATE),
      },
      {
        title: '当前景计日期',
        key: 'dangQianJingJiRiQi',
        dataIndex: 'dangQianJingJiRiQi',
        render: (record) => dayjs(record.dangQianJingJiRiQi).format(DATE_FORMATE),
      },
      { title: '账户品', key: 'zhangHuPin', dataIndex: 'zhangHuPin' },
      {
        title: '账户状态',
        key: 'zhangHuZhuangTai',
        dataIndex: 'zhangHuZhuangTai',
      },
      {
        title: '状态改变日期',
        key: 'zhuangTaiGaiBianRiQi',
        dataIndex: 'zhuangTaiGaiBianRiQi',
        render: (record) => dayjs(record.zhuangTaiGaiBianRiQi).format(DATE_FORMATE),
      },
      {
        title: '单一货币结算标识',
        key: 'danYiHuoBiJieSuanBiaoShi',
        dataIndex: 'danYiHuoBiJieSuanBiaoShi',
      },
      {
        title: '账户封锁码备注',
        key: 'zhangHuFengSuoMaBeiZhu',
        dataIndex: 'zhangHuFengSuoMaBeiZhu',
      },
      {
        title: '自动还款账户',
        key: 'ziDongHuanKuanZhangHu',
        dataIndex: 'ziDongHuanKuanZhangHu',
      },
      {
        title: '纯年费延期标识',
        key: 'chunNianFeiYanQiBiaoShi',
        dataIndex: 'chunNianFeiYanQiBiaoShi',
      },
      { title: '入催原因', key: 'ruCuiYuanYin', dataIndex: 'ruCuiYuanYin' },
      {
        title: '出催日期',
        key: 'chuCuiRiQi',
        dataIndex: 'chuCuiRiQi',
        render: (record) => dayjs(record.chuCuiRiQi).format(DATE_FORMATE),
      },
      {
        title: '120天延期金额',
        key: '120TianYanQiJinE',
        dataIndex: '120TianYanQiJinE',
      },
      {
        title: '120天延期次数',
        key: '120TianYanQiCiShu',
        dataIndex: '120TianYanQiCiShu',
      },
      {
        title: '宽限日期',
        key: 'kuanXianRiQi',
        dataIndex: 'kuanXianRiQi',
        render: (record) => dayjs(record.kuanXianRiQi).format(DATE_FORMATE),
      },
      {
        title: '账户消费金额累积',
        key: 'zhangHuXiaoFeiJinELeiJi',
        dataIndex: 'zhangHuXiaoFeiJinELeiJi',
      },
      {
        title: '上次取现金额',
        key: 'shangCiQuXianJinE',
        dataIndex: 'shangCiQuXianJinE',
      },
      {
        title: '当前还款容时标',
        key: 'dangQianHuanKuanRongShiBiao',
        dataIndex: 'dangQianHuanKuanRongShiBiao',
      },
      {
        title: '账户最高余额',
        key: 'zhangHuZuiGaoYuE',
        dataIndex: 'zhangHuZuiGaoYuE',
      },
      {
        title: '免计超限金',
        key: 'mianJiChaoXianJin',
        dataIndex: 'mianJiChaoXianJin',
      },
    ],
  },
  {
    props: {
      title: '卡片信息',
      column: 4,
    },
    type: COMPONENT_TYPE.FORM,
    key: 'infoForm5',
    columns: [
      { title: '机构号', key: 'jiGouHao', dataIndex: 'jiGouHao' },
      { title: '卡等级', key: 'kaDengJi', dataIndex: 'kaDengJi' },
      {
        title: '特殊产品标识',
        key: 'teShuChanPinBiaoShi',
        dataIndex: 'teShuChanPinBiaoShi',
      },
      {
        title: '持卡人机构号',
        key: 'chiKaRenJiGouHao',
        dataIndex: 'chiKaRenJiGouHao',
      },
      {
        title: '卡片封锁码',
        key: 'kaPianFengSuoMa',
        dataIndex: 'kaPianFengSuoMa',
      },
      {
        title: '卡片上一封锁码',
        key: 'kaPianShangYiFengSuoMa',
        dataIndex: 'kaPianShangYiFengSuoMa',
      },
      {
        title: '换卡新币种',
        key: 'huanKaXinBiZhong',
        dataIndex: 'huanKaXinBiZhong',
      },
      {
        title: '年费折扣标识',
        key: 'nianFeiZheKouBiaoShi',
        dataIndex: 'nianFeiZheKouBiaoShi',
      },
      {
        title: '密码错误次数',
        key: 'miMaCuoWuCiShu',
        dataIndex: 'miMaCuoWuCiShu',
      },
      {
        title: '卡片姓名简称',
        key: 'kaPianXingMingJianCheng',
        dataIndex: 'kaPianXingMingJianCheng',
      },
      {
        title: '申请书流水编号',
        key: 'shenQingShuLiuShuiBianHao',
        dataIndex: 'shenQingShuLiuShuiBianHao',
      },
      {
        title: '营销人员工号',
        key: 'yingXiaoRenYuanGongHao',
        dataIndex: 'yingXiaoRenYuanGongHao',
      },
      {
        title: '关闭日期',
        key: 'guanBiRiQi',
        dataIndex: 'guanBiRiQi',
        render: (record) => dayjs(record.guanBiRiQi).format(DATE_FORMATE),
      },
      {
        title: '卡片交易范围标识',
        key: 'kaPianJiaoYiFanWeiBiaoShi',
        dataIndex: 'kaPianJiaoYiFanWeiBiaoShi',
      },
      { title: '账号', key: 'zhangHao', dataIndex: 'zhangHao' },
      {
        title: '主附卡标识',
        key: 'zhuFuKaBiaoShi',
        dataIndex: 'zhuFuKaBiaoShi',
      },
      { title: '卡版代码', key: 'kaBanDaiMa', dataIndex: 'kaBanDaiMa' },
      { title: '持卡人号', key: 'chiKaRenHao', dataIndex: 'chiKaRenHao' },
      {
        title: '卡片封锁原因码',
        key: 'kaPianFengSuoYuanYinMa',
        dataIndex: 'kaPianFengSuoYuanYinMa',
      },
      {
        title: '卡片上一封锁原因码',
        key: 'kaPianShangYiFengSuoYuanYinMa',
        dataIndex: 'kaPianShangYiFengSuoYuanYinMa',
      },
      {
        title: '换卡新产品编号',
        key: 'huanKaXinChanPinBianHao',
        dataIndex: 'huanKaXinChanPinBianHao',
      },
      {
        title: '免收年费标识',
        key: 'mianShouNianFeiBiaoShi',
        dataIndex: 'mianShouNianFeiBiaoShi',
      },
      {
        title: '密码错误日期',
        key: 'miMaCuoWuRiQi',
        dataIndex: 'miMaCuoWuRiQi',
        render: (record) => dayjs(record.miMaCuoWuRiQi).format(DATE_FORMATE),
      },
      {
        title: '持卡人姓名',
        key: 'chiKaRenXingMing',
        dataIndex: 'chiKaRenXingMing',
      },
      { title: '通路代码', key: 'tongLuDaiMa', dataIndex: 'tongLuDaiMa' },
      {
        title: '营销人员姓名',
        key: 'yingXiaoRenYuanXingMing',
        dataIndex: 'yingXiaoRenYuanXingMing',
      },
      {
        title: '卡片费用参数编码',
        key: 'kaPianFeiYongCanShuBianMa',
        dataIndex: 'kaPianFeiYongCanShuBianMa',
      },
      {
        title: '卡片激活状态',
        key: 'kaPianJiHuoZhuangTai',
        dataIndex: 'kaPianJiHuoZhuangTai',
      },
      { title: '币种', key: 'biZhong', dataIndex: 'biZhong' },
      { title: '卡片质量', key: 'kaPianZhiLiang', dataIndex: 'kaPianZhiLiang' },
      { title: '有效期', key: 'youXiaoQi', dataIndex: 'youXiaoQi' },
      {
        title: '附属持卡人机构号',
        key: 'fuShuChiKaRenJiGouHao',
        dataIndex: 'fuShuChiKaRenJiGouHao',
      },
      {
        title: '卡片封锁码维护日期',
        key: 'kaPianFengSuoMaWeiHuRiQi',
        dataIndex: 'kaPianFengSuoMaWeiHuRiQi',
        render: (record) => dayjs(record.kaPianFengSuoMaWeiHuRiQi).format(DATE_FORMATE),
      },
      {
        title: '上一封锁码结束日期',
        key: 'kaPianShangYiFengSuoMaJieShuRiQi',
        dataIndex: 'kaPianShangYiFengSuoMaJieShuRiQi',
        render: (record) => dayjs(record.kaPianShangYiFengSuoMaJieShuRiQi).format(DATE_FORMATE),
      },
      {
        title: '换卡新卡号',
        key: 'huanKaXinKaHao',
        dataIndex: 'huanKaXinKaHao',
      },
      {
        title: '年费收取日期',
        key: 'nianFeiShouQuRiQi',
        dataIndex: 'nianFeiShouQuRiQi',
        render: (record) => dayjs(record.nianFeiShouQuRiQi).format(DATE_FORMATE),
      },
      { title: '密码标识', key: 'miMaBiaoShi', dataIndex: 'miMaBiaoShi' },
      { title: '刻印名称', key: 'keYinMingCheng', dataIndex: 'keYinMingCheng' },
      { title: '领卡方式', key: 'lingKaFangShi', dataIndex: 'lingKaFangShi' },
      {
        title: '销卡到期日期',
        key: 'xiaoKaDaoQiRiQi',
        dataIndex: 'xiaoKaDaoQiRiQi',
        render: (record) => dayjs(record.xiaoKaDaoQiRiQi).format(DATE_FORMATE),
      },
      {
        title: '卡片处理参数编码',
        key: 'kaPianChuLiCanShuBianMa',
        dataIndex: 'kaPianChuLiCanShuBianMa',
      },
      {
        title: '卡片激活日期',
        key: 'kaPianJiHuoRiQi',
        dataIndex: 'kaPianJiHuoRiQi',
        render: (record) => dayjs(record.kaPianJiHuoRiQi).format(DATE_FORMATE),
      },
      { title: '卡产品', key: 'kaChanPin', dataIndex: 'kaChanPin' },
      {
        title: '卡片状态改变日',
        key: 'kaPianZhuangTaiGaiBianRi',
        dataIndex: 'kaPianZhuangTaiGaiBianRi',
      },
      {
        title: '制卡作业标识',
        key: 'zhiKaZuoYeBiaoShi',
        dataIndex: 'zhiKaZuoYeBiaoShi',
      },
      {
        title: '制卡日期',
        key: 'zhiKaRiQi',
        dataIndex: 'zhiKaRiQi',
        render: (record) => dayjs(record.zhiKaRiQi).format(DATE_FORMATE),
      },
      {
        title: '上一制卡日期',
        key: 'shangYiZhiKaRiQi',
        dataIndex: 'shangYiZhiKaRiQi',
        render: (record) => dayjs(record.shangYiZhiKaRiQi).format(DATE_FORMATE),
      },
      {
        title: '制卡请求累计',
        key: 'zhiKaQingQiuLeiJi',
        dataIndex: 'zhiKaQingQiuLeiJi',
      },
      { title: '旧卡卡号', key: 'jiuKaKaHao', dataIndex: 'jiuKaKaHao' },
      {
        title: '卡片结束日期',
        key: 'kaPianJieShuRiQi',
        dataIndex: 'kaPianJieShuRiQi',
        render: (record) => dayjs(record.kaPianJieShuRiQi).format(DATE_FORMATE),
      },
      { title: '小额免标识', key: 'mianBiaoShi', dataIndex: 'mianBiaoShi' },
      {
        title: '联名卡合作方编码',
        key: 'lianMingKaHeZuoFangBianMa',
        dataIndex: 'lianMingKaHeZuoFangBianMa',
      },
      {
        title: '销卡人员姓名',
        key: 'xiaoKaRenYuanXingMing',
        dataIndex: 'xiaoKaRenYuanXingMing',
      },
      {
        title: '卡片激活方式',
        key: 'kaPianJiHuoFangShi',
        dataIndex: 'kaPianJiHuoFangShi',
      },
    ],
  },
];
// 页面左侧辅助功能配置
export const segmentedData = {
  A: {
    label: { backgroundColor: '#f56a00', text: '转', title: '转案' },
    value: 'A',
  },
  B: {
    label: { backgroundColor: '#FFEB3B', text: '提', title: '提前支付' },
    value: 'B',
  },
  C: {
    label: { backgroundColor: '#4CAF50', text: '止', title: '止付' },
    value: 'C',
  },
  D: {
    label: { backgroundColor: '#FF9800', text: '减', title: '减免' },
    value: 'D',
  },
  E: {
    label: { backgroundColor: '#2196F3', text: '保', title: '保证金抵欠' },
    value: 'E',
  },
  F: {
    label: { backgroundColor: '#87d068', text: '同', title: '同名账户调账' },
    value: 'F',
  },
  G: {
    label: { backgroundColor: '#f56a00', text: '上', title: '上除强催标' },
    value: 'G',
  },
  H: {
    label: { backgroundColor: '#FFEB3B', text: '协', title: '协商分期' },
    value: 'H',
  },
  I: {
    label: { backgroundColor: '#4CAF50', text: '轻', title: '轻财订单' },
    value: 'I',
  },
  J: {
    label: { backgroundColor: '#FF9800', text: '信', title: '信息修复' },
    value: 'J',
  },
  K: {
    label: { backgroundColor: '#2196F3', text: '短', title: '短信' },
    value: 'K',
  },
  L: {
    label: { backgroundColor: '#87d068', text: '微', title: '微信' },
    value: 'L',
  },
  M: {
    label: { backgroundColor: '#4CAF50', text: 'A', title: 'APP' },
    value: 'M',
  },
  N: {
    label: { backgroundColor: '#FF9800', text: '电', title: '电邮' },
    value: 'N',
  },
  O: {
    label: { backgroundColor: '#2196F3', text: '信', title: '信函' },
    value: 'O',
  },
};
// 页面右侧模块功能配置
export const anchorData = {
  infoForm1: {
    key: 'infoForm1',
    href: '#infoForm1',
    title: '催收信息',
  },
  infoForm2: {
    key: 'infoForm2',
    href: '#infoForm2',
    title: '客户信息',
  },
  infoForm3: {
    key: 'infoForm3',
    href: '#infoForm3',
    title: '联系信息',
  },
  infoForm4: {
    key: 'infoForm4',
    href: '#infoForm4',
    title: '账户信息',
  },
  infoForm5: {
    key: 'infoForm5',
    href: '#infoForm5',
    title: '卡片信息',
  },
};
