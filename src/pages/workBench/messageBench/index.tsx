import EchartsComponent from '@/components/EchartsComponent';
import { But<PERSON>, DatePicker, Tabs } from 'antd';
import { useState } from 'react';

const { RangePicker } = DatePicker;

const performanceTrendOptions = {
  title: { text: '业绩趋势' },
  tooltip: {},
  xAxis: {
    data: ['10月', '11月', '12月', '01月', '02月', '03月', '04月', '05月', '06月', '07月', '08月', '09月'],
  },
  yAxis: {},
  series: [
    {
      type: 'bar',
      data: [200, 750, 1000, 500, 400, 450, 300, 800, 600, 700, 400, 350],
    },
  ],
  grid: { left: 40, right: 20, top: 40, bottom: 40 },
};

const realTimeLineOptions = {
  tooltip: { trigger: 'axis' },
  legend: { data: ['蓝色', '绿色'] },
  xAxis: {
    type: 'category',
    data: ['10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'],
  },
  yAxis: { type: 'value' },
  series: [
    {
      name: '蓝色',
      type: 'line',
      data: [1000, 3000, 2800, 1500, 2000, 4000, 3100],
    },
    {
      name: '绿色',
      type: 'line',
      data: [1200, 2000, 3100, 1800, 1500, 3500, 2500],
    },
  ],
};

const salesPieOptions = {
  title: {
    text: '销售额',
    left: 'center',
    top: '40%',
    textStyle: { fontSize: 20, fontWeight: 'bold' },
  },
  tooltip: { trigger: 'item' },
  legend: { orient: 'vertical', left: 'left', top: 'center' },
  series: [
    {
      name: '销售额',
      type: 'pie',
      radius: ['60%', '80%'],
      center: ['60%', '50%'],
      avoidLabelOverlap: false,
      label: { show: false, position: 'center' },
      emphasis: { label: { show: true, fontSize: 18, fontWeight: 'bold' } },
      labelLine: { show: false },
      data: [
        { value: 4544, name: '家用电器' },
        { value: 4544, name: '食用酒水' },
        { value: 4544, name: '个护健康' },
        { value: 4544, name: '服饰箱包' },
        { value: 4544, name: '母婴产品' },
        { value: 4544, name: '其他' },
      ],
    },
  ],
};

const salesPieLegend = [
  { color: '#4e8ef7', name: '家用电器', percent: '36%', value: '￥4,544' },
  { color: '#50f0c4', name: '食用酒水', percent: '20%', value: '￥4,544' },
  { color: '#ffd700', name: '个护健康', percent: '16%', value: '￥4,544' },
  { color: '#e573c7', name: '服饰箱包', percent: '10%', value: '￥4,544' },
  { color: '#f7b500', name: '母婴产品', percent: '9%', value: '￥4,544' },
  { color: '#bdbdbd', name: '其他', percent: '9%', value: '￥4,544' },
];

const performanceRank = [
  { name: '张力', value: '323,234' },
  { name: '王杰', value: '323,234' },
  { name: '刘斌', value: '323,234' },
  { name: '王芳', value: '323,234' },
  { name: '黄致', value: '323,234' },
  { name: '刘力', value: '323,234' },
  { name: '张镇麟', value: '323,234' },
];

const MessageBench = () => {
  const [tab, setTab] = useState('业绩');
  const [salesTab, setSalesTab] = useState('全部渠道');
  return (
    <div
      style={{
        background: '#fff',
        padding: 24,
        height: '100vh',
        overflowY: 'auto',
      }}
    >
      {/* 顶部Tab和日期选择 */}
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
        <Tabs
          activeKey={tab}
          onChange={setTab}
          items={[
            { key: '业绩', label: '业绩' },
            { key: '回款率', label: '回款率' },
          ]}
          style={{ flex: 1 }}
        />
        <div style={{ marginLeft: 'auto', display: 'flex', gap: 8 }}>
          <Button type="link">今日</Button>
          <Button type="link">本周</Button>
          <Button type="link">本月</Button>
          <Button type="link">全年</Button>
          <RangePicker style={{ width: 200 }} allowClear={false} />
        </div>
      </div>
      {/* 岗位回款情况 + 指数 */}
      <div style={{ display: 'flex', gap: 24, marginBottom: 32 }}>
        {/* 岗位回款情况 */}
        <div style={{ flex: 2, minWidth: 0 }}>
          <div style={{ fontWeight: 500, fontSize: 16, marginBottom: 8 }}>岗位回款情况</div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
            <div style={{ fontSize: 32, fontWeight: 700, marginRight: 8 }}>
              210 <span style={{ fontSize: 18, fontWeight: 400 }}>s</span>
            </div>
            <div style={{ marginLeft: 32, display: 'flex', gap: 8 }}>
              <Button type="link" style={{ padding: 0 }}>
                今日
              </Button>
              <Button type="link" style={{ padding: 0 }}>
                本周
              </Button>
              <Button type="link" style={{ padding: 0 }}>
                本月
              </Button>
              <Button
                type="primary"
                style={{
                  padding: 0,
                  height: 24,
                  lineHeight: '22px',
                  fontSize: 14,
                }}
              >
                全年
              </Button>
              <RangePicker style={{ width: 200, marginLeft: 8 }} allowClear={false} size="small" />
            </div>
          </div>
          <EchartsComponent
            options={{
              tooltip: { trigger: 'axis' },
              legend: {
                data: ['>150s', '90~150s', '<150s'],
                bottom: 0,
                itemWidth: 12,
                itemHeight: 12,
                textStyle: { fontSize: 12 },
              },
              grid: { left: 30, right: 20, top: 30, bottom: 40 },
              xAxis: {
                type: 'category',
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
              },
              yAxis: { type: 'value' },
              series: [
                {
                  name: '>150s',
                  type: 'bar',
                  data: [58, 28, 45, 60, 55, 40, 20],
                  itemStyle: { color: '#4e8ef7' },
                },
                {
                  name: '90~150s',
                  type: 'bar',
                  data: [32, 18, 35, 40, 35, 30, 10],
                  itemStyle: { color: '#50f0c4' },
                },
                {
                  name: '<150s',
                  type: 'bar',
                  data: [18, 12, 15, 20, 15, 10, 7],
                  itemStyle: { color: '#ffd700' },
                },
              ],
            }}
            layoutStyle={{ width: '100%', height: 220 }}
          />
        </div>
        {/* 指数 */}
        <div
          style={{
            flex: 1,
            minWidth: 320,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'flex-start',
          }}
        >
          <div
            style={{
              fontWeight: 500,
              fontSize: 16,
              marginBottom: 8,
              alignSelf: 'flex-start',
            }}
          >
            指数
          </div>
          <EchartsComponent
            options={{
              tooltip: { trigger: 'item' },
              radar: {
                indicator: [
                  { name: '引用', max: 10 },
                  { name: '产量', max: 10 },
                  { name: '贡献', max: 10 },
                  { name: '热度', max: 10 },
                  { name: '口碑', max: 10 },
                ],
                radius: 70,
                splitNumber: 5,
                shape: 'polygon',
                axisName: { color: '#666', fontSize: 12 },
                splitLine: { lineStyle: { color: '#e0e6f1' } },
                splitArea: { areaStyle: { color: ['#fff'] } },
                axisLine: { lineStyle: { color: '#e0e6f1' } },
              },
              series: [
                {
                  name: '个人',
                  type: 'radar',
                  data: [
                    {
                      value: [6, 5, 7, 6, 6],
                      name: '个人',
                      lineStyle: { color: '#4e8ef7' },
                      areaStyle: { color: 'rgba(78,142,247,0.1)' },
                      symbol: 'circle',
                      itemStyle: { color: '#4e8ef7' },
                    },
                    {
                      value: [5, 6, 6, 5, 7],
                      name: '团队',
                      lineStyle: { color: '#ffd700' },
                      areaStyle: { color: 'rgba(255,215,0,0.1)' },
                      symbol: 'circle',
                      itemStyle: { color: '#ffd700' },
                    },
                    {
                      value: [7, 6, 5, 7, 5],
                      name: '部门',
                      lineStyle: { color: '#50f0c4' },
                      areaStyle: { color: 'rgba(80,240,196,0.1)' },
                      symbol: 'circle',
                      itemStyle: { color: '#50f0c4' },
                    },
                  ],
                },
              ],
            }}
            layoutStyle={{ width: 260, height: 220 }}
          />
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginTop: 16,
              width: '100%',
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', marginRight: 24 }}>
              <span
                style={{
                  display: 'inline-block',
                  width: 10,
                  height: 10,
                  borderRadius: '50%',
                  background: '#4e8ef7',
                  marginRight: 6,
                }}
              />
              <span style={{ color: '#333', fontSize: 14 }}>个人</span>
              <span style={{ color: '#333', fontWeight: 700, marginLeft: 4 }}>33</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', marginRight: 24 }}>
              <span
                style={{
                  display: 'inline-block',
                  width: 10,
                  height: 10,
                  borderRadius: '50%',
                  background: '#ffd700',
                  marginRight: 6,
                }}
              />
              <span style={{ color: '#333', fontSize: 14 }}>团队</span>
              <span style={{ color: '#333', fontWeight: 700, marginLeft: 4 }}>28</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span
                style={{
                  display: 'inline-block',
                  width: 10,
                  height: 10,
                  borderRadius: '50%',
                  background: '#50f0c4',
                  marginRight: 6,
                }}
              />
              <span style={{ color: '#333', fontSize: 14 }}>部门</span>
              <span style={{ color: '#333', fontWeight: 700, marginLeft: 4 }}>33</span>
            </div>
          </div>
        </div>
      </div>
      {/* 业绩趋势+业绩排名 */}
      <div style={{ display: 'flex', gap: 24 }}>
        <div style={{ flex: 2, minWidth: 0 }}>
          <EchartsComponent options={performanceTrendOptions} layoutStyle={{ width: '100%', height: 260 }} />
        </div>
        <div
          style={{
            flex: 1,
            minWidth: 220,
            background: '#fafbfc',
            borderRadius: 8,
            padding: 16,
          }}
        >
          <div style={{ fontWeight: 500, marginBottom: 16 }}>业绩排名</div>
          {performanceRank.map((item, idx) => (
            <div key={item.name} style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
              <span
                style={{
                  width: 24,
                  textAlign: 'center',
                  color: idx < 3 ? '#1890ff' : '#999',
                  fontWeight: idx < 3 ? 700 : 400,
                }}
              >
                {idx + 1}
              </span>
              <span style={{ flex: 1 }}>{item.name}</span>
              <span style={{ fontWeight: 500 }}>{item.value}</span>
            </div>
          ))}
        </div>
      </div>
      {/* 活动实时交易+销售额分类占比 */}
      <div style={{ display: 'flex', gap: 24, marginTop: 32 }}>
        <div
          style={{
            flex: 1,
            minWidth: 0,
            background: '#fafbfc',
            borderRadius: 8,
            padding: 16,
          }}
        >
          <div style={{ fontWeight: 500, marginBottom: 16 }}>活动实时交易情况</div>
          <div style={{ display: 'flex', gap: 32 }}>
            <div>
              <div style={{ fontSize: 18, fontWeight: 700 }}>124,543,233元</div>
              <div style={{ color: '#999', fontSize: 12 }}>今日交易总额</div>
            </div>
            <div>
              <div style={{ fontSize: 18, fontWeight: 700 }}>92%</div>
              <div style={{ color: '#999', fontSize: 12 }}>销售目标完成率</div>
            </div>
            <div>
              <div style={{ fontSize: 18, fontWeight: 700 }}>234元</div>
              <div style={{ color: '#999', fontSize: 12 }}>每秒交易总额</div>
            </div>
          </div>
          <div style={{ marginTop: 24 }}>
            <EchartsComponent options={realTimeLineOptions} layoutStyle={{ width: '100%', height: 180 }} />
          </div>
        </div>
        <div
          style={{
            flex: 1,
            minWidth: 0,
            background: '#fafbfc',
            borderRadius: 8,
            padding: 16,
          }}
        >
          <div style={{ fontWeight: 500, marginBottom: 16 }}>销售额类别占比</div>
          <div style={{ display: 'flex', gap: 8 }}>
            <Button
              type={salesTab === '全部渠道' ? 'primary' : 'default'}
              size="small"
              onClick={() => setSalesTab('全部渠道')}
            >
              全部渠道
            </Button>
            <Button type={salesTab === '线上' ? 'primary' : 'default'} size="small" onClick={() => setSalesTab('线上')}>
              线上
            </Button>
            <Button type={salesTab === '书店' ? 'primary' : 'default'} size="small" onClick={() => setSalesTab('书店')}>
              书店
            </Button>
          </div>
          <div style={{ marginTop: 8, marginBottom: 8 }}>
            <EchartsComponent options={salesPieOptions} layoutStyle={{ width: '100%', height: 180 }} />
          </div>
          <div>
            {salesPieLegend.map((item) => (
              <div
                key={item.name}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: 4,
                }}
              >
                <span
                  style={{
                    display: 'inline-block',
                    width: 10,
                    height: 10,
                    borderRadius: '50%',
                    background: item.color,
                    marginRight: 8,
                  }}
                />
                <span style={{ flex: 1 }}>{item.name}</span>
                <span style={{ color: '#999', marginRight: 8 }}>{item.percent}</span>
                <span style={{ fontWeight: 500 }}>{item.value}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
      {/* 数据标题区块 */}
      <div style={{ display: 'flex', gap: 24, marginTop: 40 }}>
        {/* 柱状图 */}
        <div style={{ flex: 1, background: '#fff', borderRadius: 8, padding: 16 }}>
          <div style={{ fontWeight: 500, marginBottom: 16 }}>数据标题</div>
          <EchartsComponent
            options={{
              tooltip: { trigger: 'axis' },
              legend: { data: ['项目一', '项目二', '项目三'], bottom: 0 },
              grid: { left: 30, right: 20, top: 30, bottom: 40 },
              xAxis: {
                type: 'category',
                data: ['2012', '2013', '2014', '2015', '2016'],
              },
              yAxis: { type: 'value' },
              series: [
                {
                  name: '项目一',
                  type: 'bar',
                  stack: '总量',
                  data: [32000, 33200, 30100, 33400, 39000],
                  itemStyle: { color: '#4e8ef7' },
                },
                {
                  name: '项目二',
                  type: 'bar',
                  stack: '总量',
                  data: [12000, 13200, 10100, 13400, 9000],
                  itemStyle: { color: '#50f0c4' },
                },
                {
                  name: '项目三',
                  type: 'bar',
                  stack: '总量',
                  data: [22000, 18200, 19100, 23400, 29000],
                  itemStyle: { color: '#ffd700' },
                },
              ],
            }}
            layoutStyle={{ width: '100%', height: 220 }}
          />
        </div>
        {/* 气泡图 */}
        <div style={{ flex: 1, background: '#fff', borderRadius: 8, padding: 16 }}>
          <div style={{ fontWeight: 500, marginBottom: 16 }}>数据标题</div>
          <EchartsComponent
            options={{
              tooltip: {
                trigger: 'item',
                formatter: function (params) {
                  return `${params.seriesName}<br/>${params.name}: ${params.value[2]}`;
                },
              },
              legend: { data: ['付款订单量'], bottom: 0 },
              grid: { left: 30, right: 20, top: 30, bottom: 40 },
              xAxis: {
                type: 'category',
                data: ['2012', '2013', '2014', '2015', '2016'],
              },
              yAxis: { type: 'value' },
              series: [
                {
                  name: '付款订单量',
                  type: 'scatter',
                  symbolSize: function (data) {
                    return Math.sqrt(data[2]) / 2;
                  },
                  data: [
                    { value: ['2012', 10, 1000], name: '2012' },
                    { value: ['2013', 20, 4000], name: '2013' },
                    { value: ['2014', 30, 9000], name: '2014' },
                    { value: ['2015', 25, 16000], name: '2015' },
                    { value: ['2016', 35, 25000], name: '2016' },
                  ],
                  itemStyle: { color: '#4e8ef7', opacity: 0.7 },
                  emphasis: {
                    itemStyle: { borderColor: '#333', borderWidth: 1 },
                  },
                },
              ],
            }}
            layoutStyle={{ width: '100%', height: 220 }}
          />
          <div style={{ color: '#999', fontSize: 12, marginTop: 8 }}>
            <span
              style={{
                display: 'inline-block',
                width: 12,
                height: 12,
                background: '#4e8ef7',
                borderRadius: '50%',
                marginRight: 4,
                verticalAlign: 'middle',
              }}
            />
            气泡大小：订单量
          </div>
        </div>
        {/* 散点图 */}
        <div style={{ flex: 1, background: '#fff', borderRadius: 8, padding: 16 }}>
          <div style={{ fontWeight: 500, marginBottom: 16 }}>数据标题</div>
          <EchartsComponent
            options={{
              tooltip: {
                trigger: 'item',
                formatter: function (params) {
                  return `汽车销量<br/>年齡: ${params.value[0]}<br/>价格: ${params.value[1]}`;
                },
              },
              legend: { data: ['汽车销量'], bottom: 0 },
              grid: { left: 30, right: 20, top: 30, bottom: 40 },
              xAxis: {
                type: 'category',
                data: ['19-30', '30-40', '41-60', '60+'],
                name: '年龄',
              },
              yAxis: { type: 'value', name: '价格' },
              series: [
                {
                  name: '汽车销量',
                  type: 'scatter',
                  data: [
                    ['19-30', 200000],
                    ['19-30', 220000],
                    ['19-30', 250000],
                    ['30-40', 210000],
                    ['30-40', 230000],
                    ['30-40', 260000],
                    ['41-60', 220000],
                    ['41-60', 240000],
                    ['41-60', 270000],
                    ['60+', 210000],
                    ['60+', 230000],
                    ['60+', 260000],
                    ['60+', 300000],
                  ],
                  itemStyle: { color: '#4e8ef7', opacity: 0.7 },
                },
              ],
            }}
            layoutStyle={{ width: '100%', height: 220 }}
          />
        </div>
      </div>
    </div>
  );
};

export default MessageBench;
