import { COMPONENT_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import { IFormConfig } from '@/types/IForm';

const useFormConfig = (type: string) => {
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'tradingTypeParamTable',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'crcdOrgNo',
          label: 'crcdOrgNo',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'trxType',
          label: 'trxType',
          // data: DICT_CONSTANTS.TRX_TYPE,
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'trxTypeDesc',
          label: 'trxTypeDesc',
          rules: [{ required: true }],
          maxLength: 15,
        },
      ],
    },
  ];

  return {
    infoFormConfig,
  };
};

export default useFormConfig;
