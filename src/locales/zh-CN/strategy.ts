export default {
  'strategy.strategyManage': '策略管理',
  'strategy.strategyId': '策略ID',
  'strategy.strategyName': '策略名称',
  'strategy.strategyType': '策略类型',
  'strategy.considerInventory': '是否考虑库存',
  'strategy.considerWeight': '是否考虑权重',
  'strategy.weightStrategyId': '策略业务ID',
  'strategy.description': '描述',
  'strategy.strategySetting': '策略设置',
  'strategy.factorId': '因子ID',
  'strategy.coefficient': '催收员级别',
  'strategy.performance': '催收员业绩',
  'strategy.factorType': '因子类型',
  'strategy.factorLevel': '因子级别',
  'strategy.performanceMin': '业绩最低值',
  'strategy.performanceMax': '业绩最高值',
  'strategy.weightingFactor': '加权系数',
  'strategy.weightRatio': '权重系数',
  'strategy.id': '主键ID',
  'strategy.weightStrategyName': '策略名称',
  'strategy.effectiveStart': '策略生效开始日期',
  'strategy.effectiveEnd': '策略生效结束日期',
  // 排序模板
  'strategy.templateId': '模板ID',
  'strategy.templateName': '模板名称',
  'strategy.positionId': '岗位ID',
  'strategy.positionName': '岗位名称',
  'strategy.status': '状态',
  'strategy.priority': '优先级',
  'strategy.fieldName': '字段名称',
  'strategy.fieldDisplay': '字段显示名称',
  'strategy.sortOrder': '排序顺序',
  'strategy.sortSetting': '排序管理',
};
