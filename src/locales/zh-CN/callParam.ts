// 标签管理页面
// 维表管理页面
// 案件基本信息页面
// 审批管理页面
// 节点流程页面
// 电话催收工作台
// 短信催收工作台
// 节点管理
// 处理程序管理
// 处理程序管理
// 综合管理-工作台配置

export default {
  // 标签管理页面
  'callParam.labelManage': '标签管理',
  'callParam.tagName': '标签名称',
  'callParam.paramIndex': '序号',
  'callParam.tagType': '标签类型',
  'callParam.tagSource': '标签来源',
  'callParam.tagId': '标签ID',
  'callParam.tagDescription': '标签描述',
  'callParam.tagAttribute': '标签属性',
  'callParam.labelType1': '还款意愿',
  'callParam.labelType2': '还款能力',
  'callParam.labelType3': '逾期阶段',
  'callParam.labelType4': '风险等级',
  'callParam.labelType5': '流转判断',
  'callParam.labelType6': '特殊标签',
  'callParam.labelNameSrc1': '手动输入',
  'callParam.labelNameSrc2': '批量导入',
  'callParam.labelNameSrc3': '实时接口',
  'callParam.attribute1': '常规标签',
  'callParam.attribute2': '特殊标签',
  'callParam.attribute3': '其他',
  'callParam.labelSource1': '手工导入',
  'callParam.labelSource2': '批量接口',
  'callParam.labelSource3': '实时接口',
  'callParam.labelDesc': '标签描述',
  'callParam.updateUser': '更新人',
  'callParam.updater': '更新人',
  'callParam.updateTime': '更新时间',
  'callParam.createTime': '创建时间',
  'callParam.aduitUser': '审批人',
  'callParam.aduitTime': '审批时间',
  'callParam.status': '状态',
  'callParam.ruleType': '规则类型',
  'callParam.pleaseSelectRuleType': '请选择规则类型',
  'callParam.rulePackageId': '规则包ID',
  'callParam.pleaseSelectRulePackageId': '请选择规则包ID',
  'callParam.selectList': '选择列表',
  'callParam.pleaseSelectList': '请选择选择列表',
  'callParam.labelSetting': '标签设置',

  // 维表管理页面
  'callParam.dimensionalManage': '维表管理',
  'callParam.custName': '客户名称',
  'callParam.custPerson': '客群总数',
  'callParam.custDesc': '客群描述',
  'callParam.baseInfo': '基础信息',
  'callParam.invalid': '失效',
  'callParam.effective': '有效',

  // 案件基本信息页面
  'callParam.caseManage': '案件信息',
  'callParam.aiRulesCheck': 'AI辅助',
  'callParam.caseInfo': '案件概览',
  'callParam.caseCode': '案件号',
  'callParam.caseList': '案件列表',
  'callParam.caseQuery': '案件查询',
  'callParam.caseType': '案件类型',
  'callParam.chargoffFlag': '核销标识',
  'callParam.countOdue': '入催次数',
  'callParam.icType': '证件类型',
  'callParam.custIc': '证件号',
  'callParam.dteIntoCollection': '入催时间',
  'callParam.dteOutCollection': '出催时间',
  'callParam.firstDate': '首次入催时间',
  'callParam.lastCaseCode': '上案件号',
  'callParam.lastCaseDate': '上次案件时间',
  'callParam.lawDate': '诉讼日期',
  'callParam.lawFlag': '诉讼标识',
  'callParam.model': '模块',
  'callParam.orgCustNbr': '客户号',
  'callParam.currState': '当前状态',
  'callParam.caseState': '案件状态',
  'callParam.teamCode': '风险池编号',
  'callParam.lastOpeTime': '最近行动时间',
  'callParam.inTeamDate': '入组日期',
  'callParam.reasonCode': '风险池原因码',
  'callParam.actSerialId': '行动路径编号',
  'callParam.noneContactRuleCode': '失联决策代码',
  'callParam.contactCode': '联系情况',
  'callParam.contactTimes': '人工联系次数',
  'callParam.resultCode': '结果代码',
  'callParam.resultTime': '结果完成时间',
  'callParam.resultDirection': '呼入/呼出',
  'callParam.attemptCount': '尝试联系这个帐户的次数',
  'callParam.clientCode': '用户定义的结果代码',
  'callParam.voiceCallType': '信息传达标记',
  'callParam.specialCaseNote': '特别案件备注',
  'callParam.callTotal': '有效催收总数',
  'callParam.lastModel': '上一模板',
  'callParam.ename': '英文名',
  'callParam.eusex': '性别',
  'callParam.statementTypeAll': '账单类型',
  'callParam.billingCycle': '账单日',
  'callParam.badnessCode': '不良标识域',
  'callParam.classIBalance': '总欠款',
  'callParam.riskRank': '风险等级',
  'callParam.delayDays': '延滞天数',
  'callParam.activeInstallmentFlag': '活跃分期标识',
  'callParam.productType': '大额账户标识',
  'callParam.status24': '24期状况',
  'callParam.wcsOutcode': '委外标志',
  'callParam.handType': '手别',
  'callParam.model1': '电催',
  'callParam.model2': '委外',
  'callParam.model3': '诉讼',
  'callParam.model4': '短信',
  'callParam.model5': '核销',
  'callParam.model6': '专案',
  'callParam.dayNum': '延滞天数',
  'callParam.caseAmt': '逾期金额',
  'callParam.pending': '待办',
  'callParam.completed': '结案',

  // 审批管理页面
  'callParam.aduitManage': '审批管理',
  'callParam.aduitPage': '审批项',
  'callParam.aduitStatus': '审批状态',
  'callParam.applyUser': '申请人',
  'callParam.applyTime': '申请时间',

  // 节点流程页面
  'callParam.dispatchManage': '节点流程',
  'callParam.flowchartId': '节点ID',
  'callParam.flowchartName': '节点名称',
  'callParam.description': '描述',
  'callParam.flowchartText': '节点流程图',
  'callParam.nodeExecStatus': '节点执行状态',
  'callParam.nodeExecCount': '节点执行次数',
  'callParam.lastExecDuration': '最后一次执行时长',
  'callParam.lastExecTime': '最后一次执行时间',
  'callParam.nextNode': '下一节点',
  'callParam.nodeProgram': '节点程序',
  'callParam.nodeProStrategy': '流转策略',
  'callParam.nodeRule': '规则',
  'callParam.nodeRuleFactor': '规则因子',
  'callParam.nodeRuleFactorResult': '规则结果',
  'callParam.operator': '运算符',
  'callParam.nodeRuleValue': '值',

  // 电话催收工作台
  'callParam.phone': '电话催收工作台',

  // 短信催收工作台
  'callParam.message': '短信催收工作台',

  // 节点管理
  'callParam.nodeConfig': '节点管理',
  'callParam.nodeCode': '节点ID',
  'callParam.nodeNodeName': '节点名称',
  'callParam.nodeFuncDesc': '节点说明',
  'callParam.nodeType': '节点类型',
  'callParam.nodeAuth': '节点权限',
  'callParam.priority': '优先级',
  'callParam.nodeAttriId': '节点处理程序',
  'callParam.programCode': '程序代码',
  'callParam.cycleStartFlag': '循环起始标志',
  'callParam.programName': '程序名称',
  'callParam.instanceName': '实例名',
  'callParam.programFuncDesc': '程序功能描述',
  'callParam.programType': '程序类型',
  'callParam.maxInstanceNum': '最大实例数',
  'callParam.nodeAuth1': '业务员',
  'callParam.nodeAuth2': '组长',
  'callParam.nodeAuth3': '审批人',
  'callParam.nodeAuth4': '管理员',
  'callParam.nodeType0': '开始节点',
  'callParam.nodeType1': '技术节点',
  'callParam.nodeType2': '业务节点',
  'callParam.nodeStatus1': '编辑中',
  'callParam.nodeStatus2': '生效中',
  'callParam.nodeStatus3': '审批中',
  'callParam.nodeStatus4': '停用/失效',

  // 处理程序管理
  'callParam.nodeFlowManage': '节点处理程序',
  'callParam.nodeFlowId': '程序ID',
  'callParam.nodeFlowName': '程序名称',
  'callParam.nodeFlowCode': '支持节点代码',
  'callParam.nodeFlowDesc': '简介',
  'callParam.nodeFlowUp': '实例上限',

  // 处理程序管理
  'callParam.nodeAttribute': '处理程序管理',
  'callParam.nodeAttriName': '程序名称',
  'callParam.nodeAttriType': '程序类型',
  'callParam.nodeRuleype': '关联规则',
  'callParam.nodeAttriDesc': '简介',
  'callParam.nodeAttriCode': '支持节点代码',
  'callParam.nodeAttriUp': '实例上限',
  'callParam.nodeAttriType1': '数据同步',
  'callParam.nodeAttriType2': '标签处理',
  'callParam.nodeAttriType3': '第三方接口',
  'callParam.nodeAttriType4': '本系统接口',
  'callParam.department': '部门',
  'callParam.groups': '组别',

  // 综合管理-工作台配置
  'callParam.integrateManageWorkBench': '工作台配置',
  'callParam.workBenchName': '配置页面',
  'callParam.workBenchModel': '展示模块',
  'callParam.callFields': '催收信息字段',
  'callParam.customFields': '客户信息字段',
  'callParam.contact': '姓名',
  'callParam.auxiliaryFunctions': '辅助功能',
  'callParam.authPage1': '电话催收工作台',
  'callParam.authPage2': '短信催收工作台',
  'callParam.authPage3': '核销催收工作台',
  'callParam.authPage4': '委外催收工作台',
  'callParam.authPage5': '法诉催收工作台',
  'callParam.authPage6': '专案工作台',
  'callParam.rightAction1': '催收信息',
  'callParam.rightAction2': '客户信息',
  'callParam.rightAction3': '联系信息',
  'callParam.rightAction4': '账户信息',
  'callParam.rightAction5': '卡片信息',
  'callParam.leftAction1': '转案',
  'callParam.leftAction2': '提前支付',
  'callParam.leftAction3': '止付',
  'callParam.leftAction4': '减免',
  'callParam.leftAction5': '保证金抵欠',
  'callParam.leftAction6': '同名账户调账',
  'callParam.leftAction7': '上除强催标',
  'callParam.leftAction8': '协商分期',
  'callParam.leftAction9': '轻财订单',
  'callParam.leftAction10': '信息修复',
  'callParam.leftAction11': '短信',
  'callParam.leftAction12': '微信',
  'callParam.leftAction13': 'APP',
  'callParam.leftAction14': '电邮',
  'callParam.leftAction15': '信函',

  // 智能规则检查
  'callParam.aiRulesTitle': '智能规则检查',
  'callParam.ruleTypeLabel': '请输入要检查的规则类型：',
  'callParam.ruleTypePlaceholder': '例如：节点流程规则',
  'callParam.promptLabel': '请输入提示词：',
  'callParam.promptPlaceholder': '请输入需要检查的内容',
  'callParam.startCheck': '开始检查',
  'callParam.startCheckMessage': '开始检查',
  'callParam.pleaseComplete': '请填写完整信息',

  // 案件管理
  'callParam.noNodeInfo': '无节点信息',
  'callParam.transferDispatchSuccess': '转派成功',
  'callParam.dispatchStrategy': '派案策略',
  'callParam.targetQueue': '目标队列',
  'callParam.transfer': '转案',
  'callParam.dispatch': '派案',
  'callParam.collectionRecord': '催收记录',
  'callParam.casePool': '案件池',
  'callParam.caseMyCase': '我的案件',
  'callParam.reviewDate': '复核时间',
  'callParam.commitmentAmount': '承诺金额',
  'callParam.commitmentDate': '承诺时间',
  'callParam.dailFlag': '是否继续拨号',
  'callParam.callDuration': '通话时长',
  'callParam.startTime': '通话开始时间',
  'callParam.endTime': '通话结束时间',
  'callParam.audioCode': '录音编号',
  'callParam.actionDate': '行动日期',
  'callParam.obType': '外呼类型',
  'callParam.telType': '电话类型',
  'callParam.callType': '通话方向',
  'callParam.outbound': '外呼',
  'callParam.mobile': '手机',
  'callParam.callOut': '呼出',
  'callParam.id': '编号',
  'callParam.customerNo': '客户号',
  'callParam.templateCode': '模板编号',
  'callParam.templateName': '模板名称',
  'callParam.mobileNumber': '手机号码',
  'callParam.sendContent': '发送内容',
  'callParam.receiverName': '接收人姓名',
  'callParam.senderCollector': '发送催收员',
  'callParam.sendTime': '发送时间',
  'callParam.taskDate': '任务日期',
  'callParam.creator': '创建人',
  'callParam.cardNumber': '卡号',
  'callParam.transactionCode': '交易代码',
  'callParam.transactionCurrency': '交易货币',
  'callParam.transactionAmount': '交易金额',
  'callParam.postingCurrency': '入账货币',
  'callParam.postingAmount': '入账金额',
  'callParam.transactionDate': '交易日期',
  'callParam.postingDate': '入账日期',
  'callParam.smsSuccess': '短信发送成功',
  'callParam.connected': '已接通',
  'callParam.rejected': '拒接',
  'callParam.selectPlaceholder': '请选择',
  'callParam.amountBalance': '金额均衡',
  'callParam.caseBalance': '数量均衡',
  'callParam.amountAndCaseBalance': '金额 + 数量均衡',
  'callParam.teamA': '电催A组',
  'callParam.teamB': '电催B组',
  'callParam.teamC': '电催C组',
  'callParam.outsourcingQueue': '委外队列',
  'callParam.litigationQueue': '诉讼队列',

  // 催收工作台
  'callParam.collectionInfoOverview': '催收信息概览',

  // 岗位管理
  'callParam.stationManagement': '岗位管理',
  'callParam.staffManagement': '人员管理',
  'callParam.stationId': 'ID',
  'callParam.position': '岗位',
  'callParam.staff': '人员',
  'callParam.preview': '结果',
  'callParam.weightCoefficient': '分案权重',
  'callParam.staffName': '姓名',
  'callParam.station1': '客户经理',
  'callParam.station2': '授信QA',
  'callParam.station3': '催收QA',
  'callParam.customerManager': '客户经理',
  'callParam.collectionQA': '催收QA',
  'callParam.collectionSpecialist': '催收专员',
  'callParam.seniorCollector': '高级催收员',
  'callParam.expertCollector': '资深催收员',
  'callParam.collectionSupervisor': '催收主管',
  'callParam.seniorSupervisor': '高级主管',
  'callParam.paramSettingTitle': '多标签处理程序 - 参数设置',
  'callParam.paramName': '变量名',
  'callParam.paramDesc': '参数描述',
  'callParam.type': '类型',
  'callParam.value': '取值',
  'callParam.action': '操作',
  'callParam.add': '新增',
  'callParam.edit': '编辑',
  'callParam.delete': '删除',
  'callParam.save': '保存',
  'callParam.cancel': '取消',
  'callParam.deleteConfirm': '确定删除?',
  'callParam.pleaseFinishParamFirst': '请先填写未完成的参数项',
  'callParam.input': '入参',
  'callParam.output': '出参',

  'callParam.name': '姓名',
  'callParam.contactName': '联系人姓名',
  'callParam.phoneNmbr': '电话号码',
  'callParam.relation': '关系',
  'callParam.contactRemarks': '话务总结 ',
  'callParam.caseResultFlag': '案件催收小结标识',
  'callParam.transactionDescription': '注释/交易描述',
  'callParam.sendMessageSuccess': '短信发送成功',
  'callParam.outsourcingFlag': '委外标识',
  'callParam.phoneCallRecord': '电话催收',
  'callParam.messageCallRecord': '短信催收',
  'callParam.repaymentRecord': '还款记录',
  'callParam.nodeFlowRule': '节点流转规则',
  'callParam.labelMatchRule': '标签匹配规则',
  'callParam.labelHandleRule': '标签处理规则',
  'callParam.checkResult': '检查结果',
  'callParam.messageCallTitle': '短信催收',
  'callParam.phoneCallTitle': '电话催收',
  'callParam.callPersonTitle': '催收员列表',
  'callParam.aiHelpTitle': '规则AI检查',
  'callParam.aiHelpOkText': '知道了',
  'callParam.blockCodeTitle': '修改客户封锁码',
  'callParam.dispatchTitle': '派案',
  'callParam.transferTitle': '转案',
  'callParam.marketingStaffName': '营销人员姓名',
  'callParam.cardFeeParamCode': '卡片费用参数编码',
  'callParam.passwordIdentifier': '密码标识',
  'callParam.embossedName': '刻印名称',
  'callParam.cardCollectionMethod': '领卡方式',
  'callParam.cardCancellationDueDate': '销卡到期日期',
  'callParam.cardProcessingParamCode': '卡片处理参数编码',
  'callParam.cardActivationDate': '卡片激活日期',
  'callParam.cardProduct': '卡产品',
  'callParam.cardStatusChangeDate': '卡片状态改变日',
  'callParam.cardProductionFlag': '制卡作业标识',
  'callParam.cardProductionDate': '制卡日期',
  'callParam.previousCardProductionDate': '上一制卡日期',
  'callParam.cardProductionRequestCount': '制卡请求累计',
  'callParam.oldCardNumber': '旧卡卡号',
  'callParam.cardEndDate': '卡片结束日期',
  'callParam.smallAmountExemptionFlag': '小额免标识',
  'callParam.coBrandPartnerCode': '联名卡合作方编码',
  'callParam.cardCancellationStaffName': '销卡人员姓名',
  'callParam.cardActivationMethod': '卡片激活方式',
  'callParam.transferShort': '转案',
  'callParam.advancePayment': '提前支付',
  'callParam.advancePaymentShort': '提',
  'callParam.stopPayment': '止付',
  'callParam.stopPaymentShort': '止付',
  'callParam.reduction': '减免',
  'callParam.reductionShort': '减免',
  'callParam.marginDeduction': '保证金抵欠',
  'callParam.marginDeductionShort': '保',
  'callParam.sameNameAccountAdjustment': '行动代码',
  'callParam.sameNameAccountAdjustmentShort': '行动',
  'callParam.addStrongCollection': '上除强催标',
  'callParam.addStrongCollectionShort': '上标',
  'callParam.negotiatedInstallment': '协商分期',
  'callParam.negotiatedInstallmentShort': '协商',
  'callParam.lightFinanceOrder': '轻财订单',
  'callParam.lightFinanceOrderShort': '轻',
  'callParam.infoRepair': '信息修复',
  'callParam.infoRepairShort': '信',
  'callParam.sms': '短信',
  'callParam.smsShort': '短信',
  'callParam.wechat': '微信',
  'callParam.wechatShort': '微',
  'callParam.app': 'APP',
  'callParam.appShort': 'A',
  'callParam.emailShort': '电',
  'callParam.letter': '信函',
  'callParam.letterShort': '信',
  'callParam.collectionInfo': '催收信息',
  'callParam.customerInfo': '客户信息',
  'callParam.contactInfo': '联系信息',
  'callParam.accountInfo': '账户信息',
  'callParam.transactionRecord': '交易记录',
  'callParam.cardInfo': '卡片信息',
  'callParam.customerName': '客户姓名',
  'callParam.idType': '证件类型',
  'callParam.idNumber': '证件号',
  'callParam.chargeOffFlag': '核销标识',
  'callParam.collectionCount': '入催次数',
  'callParam.collectionEntryTime': '入催时间',
  'callParam.collectionExitTime': '出催时间',
  'callParam.firstCollectionTime': '首次入催时间',
  'callParam.previousCaseCode': '上案件号',
  'callParam.previousCaseTime': '上次案件时间',
  'callParam.litigationDate': '诉讼日期',
  'callParam.litigationFlag': '诉讼标识',
  'callParam.module': '模块',
  'callParam.currentStatus': '当前状态',
  'callParam.caseStatus': '案件状态',
  'callParam.riskPoolCode': '风险池编号',
  'callParam.lastActionTime': '最近行动时间',
  'callParam.teamEntryDate': '入组日期',
  'callParam.riskPoolReasonCode': '风险池原因码',
  'callParam.lostContactDecisionCode': '失联决策代码',
  'callParam.contactStatus': '联系情况',
  'callParam.manualContactCount': '人工联系次数',
  'callParam.resultCompletionTime': '结果完成时间',
  'callParam.callDirection': '呼入呼出',
  'callParam.attemptedContactCount': '尝试联系次数',
  'callParam.effectiveCollectionTotal': '有效催收总数',
  'callParam.previousTemplate': '上一模板',
  'callParam.englishName': '英文名',
  'callParam.gender': '性别',
  'callParam.billType': '账单类型',
  'callParam.billingDay': '账单日',
  'callParam.badDebtFlag': '不良标识',
  'callParam.totalDebt': '总欠款',
  'callParam.riskLevel': '风险等级',
  'callParam.largeAccountFlag': '大额账户标识',
  'callParam.statementType': '账单类型',
  'callParam.currentBusinessDate': '当前营业日',
  'callParam.intlConstanswer': '接话',
  'callParam.intlConstdial': '拨号',
  'callParam.intlConsthold': '保持',
  'callParam.intlConstconsult': '咨询',
  'callParam.intlConsttransfer': '转接',
  'callParam.intlConstconference': '会议',
  'callParam.intlConstbusy': '示忙',
  'callParam.intlConstready': '就绪',
  'callParam.intlConstafterProcess': '后处理',
  'callParam.intlConstsignIn': '签入',
  'callParam.intlConsttotalCases': '总案件',
  'callParam.intlConstpendingCases': '待办案件',
  'callParam.intlConstcompletedCases': '已办案件',
  'callParam.intlConstperformanceList': '绩效列表',
  'callParam.intlConststatus': '状态',
  'callParam.intlConstcollected': '已催',
  'callParam.intlConstmode': '模式',
  'callParam.intlConstcontact': '联系人',
  'callParam.intlConstactivity': '活动',
  'callParam.intlConstrelation': '关系',
  'callParam.intlConstspouse': '夫妻',
  'callParam.intlConstdirection': '方向',
  'callParam.intlConstsource': '来源',
  'callParam.intlConstduration': '时长',
  'callParam.intlConstcaseTrendChart': '案件量趋势图',
  'callParam.intlConsttotalPendingCases': '总待办案件',
  'callParam.intlConstnewCollectionCases': '新入催案件',
  'callParam.intlConstexitCollectionCases': '出催案件',
  'callParam.intlConstcaseDistributionChart': '案件分布图',
  'callParam.intlConstphoneCollectionCases': '电催案件',
  'callParam.intlConstsmsCollectionCases': '短信催收',
  'callParam.intlConstoutsourcingCases': '委外案件',
  'callParam.intlConstwriteOffCases': '核销案件',
  'callParam.intlConstlitigationCases': '诉讼案件',
  'callParam.performanceList': '绩效列表',
  'callParam.knowledgeBase': '知识库',
  'callParam.pendingQualityTasks': '个待处理品质任务',
  'callParam.largeSumRepayment': '大额还款轮播',
  'callParam.noLargeRepayment': '昨日暂无两万以上还款案件，敬请等待最新消息',
  'callParam.unreadNotifications': '条未读通知',
  'callParam.queueName': '队列名称',
  'callParam.riskPool': '风险池',
  'callParam.pendingCount': '待处理量',
  'callParam.effectiveWorkload': '有效工作量',
  'callParam.invalidWorkload': '无效工作量',
  'callParam.caseProcessingVolume': '案件处理量',
  'callParam.realTimeRepaymentAmount': '案件实时还款金额',
  'callParam.households': '户数',
  'callParam.taskCategory': '任务类别',
  'callParam.initiator': '发起人',
  'callParam.initiationTime': '发起时间',
  'callParam.transferTime': '转案时间',
  'callParam.orgCustomerNumber': 'ORG客户号',
  'callParam.transferReason': '转案原因',
  'callParam.realTimeRepayment': '实时还款',
  'callParam.pendingApprovalItems': '个待审批事项',
  'callParam.dialPhone': '拨打电话',
  'callParam.sendMessage': '发送短信',
  'callParam.smsTemplate': '短信模板',
  'callParam.relationColleague': '同事',
  'callParam.relationWife': '妻子',
  'callParam.relationMother': '母亲',
  'callParam.smsTemplate1':
    '尊敬的[客户姓名]，您好！提醒您，您的账单[账单编号]已到期，应还款额为[金额]元。为了避免不必要的滞纳金，请尽快完成支付。如有任何疑问或需要帮助，请随时联系我们。感谢您的配合与支持！',
  'callParam.smsTemplate2':
    '[客户姓名]，您好！您的账单[账单编号]已逾期，欠款金额为[金额]元。长期逾期可能会影响您的信用评分及未来金融服务申请。为避免不必要的麻烦，请尽快处理。我们始终愿意与您沟通，共同寻找解决办法。如有任何疑问，欢迎随时联系。',
  'callParam.smsTemplate3':
    '亲爱的[客户姓名]，值此[节日名称]之际，祝您节日快乐！同时，温馨提醒您，您的账单[账单编号]将于节后到期，应还款额为[金额]元。请提前做好安排，以免影响节日心情。如有需要帮助，我们随时待命。再次祝您节日愉快！',
  'callParam.smsSendSuccess': '短信发送成功',
  'callParam.callSuccess': '电话拨打成功',
  'callParam.willingToPay': '客户愿意还款',
  'callParam.consideringPayment': '客户考虑还款',
  'callParam.customerName1': '沈羽昕',
  'callParam.customerName2': '马慧颖',
  'callParam.customerName3': '钱雪薇',
  'callParam.branchNumber': '分行号',
  'callParam.subBranchNumber': '支行号',
  'callParam.restatedAndCannotBeOpened': '重述且不能开立',
  'callParam.permanentCreditLimit': '永久信用额度',
  'callParam.temporaryCreditLimit': '临时信用额度',
  'callParam.memo': '备注',
  'callParam.collectionRemarks': '催收备注',
  'callParam.vipFlagDomain': 'VIP标识域',
  'callParam.idValidity': '证件有效期',
  'callParam.dateOfBirth': '出生日期',
  'callParam.residenceType': '居住类型',
  'callParam.residenceDuration': '居住时长',
  'callParam.occupation': '职业',
  'callParam.companyName': '公司名称',
  'callParam.annualSalary': '年收入',
  'callParam.abCustomerGroup': 'AB客户组',
  'callParam.blockCode': '锁码',
  'callParam.blockCodeDate': '锁码日期',
  'callParam.previousBlockCode': '上一锁码',
  'callParam.previousBlockCodeDate': '上一锁码日期',
  'callParam.email': '电子邮箱',
  'callParam.graduationSchoolName': '毕业院校',
  'callParam.educationLevel': '学历',
  'callParam.major': '专业',
  'callParam.maritalStatus': '婚姻状况',
  'callParam.cardholderType': '持卡人类型',
  'callParam.cardholderStatus': '持卡人状态',
  'callParam.cardholderStatusChangeDate': '持卡人状态变更日期',
  'callParam.guarantorName': '担保人姓名',
  'callParam.guarantorPhone': '担保人电话',
  'callParam.deposit': '存款',
  'callParam.creationAccountOpeningDate': '创建开户日期',
  'callParam.homePhone': '家庭电话',
  'callParam.alternateContactPhone': '备用联系电话',
  'callParam.workPhone1': '工作电话1',
  'callParam.workPhone2': '工作电话2',
  'callParam.workPhone3': '工作电话3',
  'callParam.emergencyContact1Relationship': '紧急联系人1关系',
  'callParam.emergencyContact1Name': '紧急联系人1姓名',
  'callParam.emergencyContact1Phone': '紧急联系人1电话',
  'callParam.emergencyContact2Relationship': '紧急联系人2关系',
  'callParam.emergencyContact2Name': '紧急联系人2姓名',
  'callParam.emergencyContact2Phone': '紧急联系人2电话',
  'callParam.householdAddress': '户籍地址',
  'callParam.homeAddress': '家庭地址',
  'callParam.workAddress': '工作地址',
  'callParam.currentResidence': '现居住地',
  'callParam.billingAddress': '账单地址',
  'callParam.accountStatus': '账户状态',
  'callParam.accountOpenDate': '开户日期',
  'callParam.accountCloseDate': '销户日期',
  'callParam.accountType': '账户类型',
  'callParam.creditLimit': '信用额度',
  'callParam.tempCreditLimit': '临时信用额度',
  'callParam.tempCreditLimitExpireDate': '临时额度到期日',
  'callParam.availableCredit': '可用额度',
  'callParam.cashLimit': '取现额度',
  'callParam.cashAvailable': '可用取现额度',
  'callParam.statementDate': '账单日',
  'callParam.dueDate': '到期还款日',
  'callParam.lastPaymentDate': '最后还款日',
  'callParam.lastPaymentAmount': '最后还款金额',
  'callParam.minimumPayment': '最低还款额',
  'callParam.currentBalance': '当前余额',
  'callParam.statementBalance': '账单余额',
  'callParam.pastDueAmount': '逾期金额',
  'callParam.pastDueDays': '逾期天数',
  'callParam.interestRate': '利率',
  'callParam.annualFee': '年费',
  'callParam.cardActivationStatus': '卡片激活状态',
  'callParam.rewardPoints': '奖励积分',
  'callParam.creditScore': '信用评分',
  'callParam.paymentHistory': '还款历史',
  'callParam.autoPaymentStatus': '自动还款状态',
  'callParam.lastStatementDate': '上次账单日',
  'callParam.creditUtilization': '信用额度使用率',
  'callParam.accountCurrency': '账户币种',
  'callParam.accountLevel': '账户等级',
  'callParam.accountNotes': '账户备注',
  'callParam.lastTransactionDate': '最后交易日期',
  'callParam.lastTransactionAmount': '最后交易金额',
  'callParam.overdraftLimit': '透支额度',
  'callParam.accountFrozenStatus': '账户冻结状态',
  'callParam.accountFrozenDate': '账户冻结日期',
  'callParam.accountFrozenReason': '账户冻结原因',
  'callParam.institutionNumber': '机构号',
  'callParam.cardTransferNewAccountCurrency': '卡转新账户币种',
  'callParam.creditCardHolderNumber': '信用卡持卡人号',
  'callParam.accountBlockCode': '账户封锁码',
  'callParam.accountBlockCodeReasonCode': '账户封锁码原因码',
  'callParam.accountSingleDay': '账户单日',
  'callParam.accountFreezeStatus': '账户冻结状态',
  'callParam.waiverOfPenaltyFlag': '免收违约金标识',
  'callParam.collectionOutsourcingFlag': '催收委外标识',
  'callParam.accountCollectionTimes': '账户入催次数',
  'callParam.currentMinimumRepaymentRemaining': '本期最低还款剩余',
  'callParam.150DayDeferralAmount': '150天延期金额',
  'callParam.deferralPeriods': '延期期数',
  'callParam.150DayDeferralTimes': '150天延期次数',
  'callParam.initialConsumptionRemainingAmount': '期初消费剩余金额',
  'callParam.unsecuredRepaymentAmount': '未挂现还款金额',
  'callParam.accountWithdrawalAmountAccumulated': '账户取现金额累计',
  'callParam.accountLastWithdrawalDate': '账户上次取现日期',
  'callParam.pledgedConsumptionLoanAdjustmentBalance': '已挂消费贷调余额',
  'callParam.specialProductFlag': '特殊产品标识',
  'callParam.accountHighestAmountDate': '账户最高金额日期',
  'callParam.previousRepaymentDueDate': '上期还款到期日期',
  'callParam.accountNumber': '账户号码',
  'callParam.cardTransferNewAccountSerialNumber': '卡片转账新账户流水号',
  'callParam.currency': '货币',
  'callParam.cardQuality': '卡片等级',
  'callParam.validityPeriod': '有效期',
  'callParam.supplementaryCardholderInstitutionNumber': '附属卡持卡人机构编号',
  'callParam.cardBlockCodeMaintenanceDate': '卡片止付代码维护日期',
  'callParam.endDateOfPreviousBlockCode': '前一个止付代码结束日期',
  'callParam.newCardNumberForCardReplacement': '换卡后的新卡号',
  'callParam.annualFeeCollectionDate': '年费收取日期',
  'callParam.passwordFlag': '密码标志',
  'callParam.engravedName': '刻印姓名',
  'callParam.cardCancellationExpiryDate': '卡片注销到期日',
  'callParam.cardProcessingParameterCode': '卡片处理参数代码',
  'callParam.cardMakingOperationFlag': '制卡操作标志',
  'callParam.cardMakingDate': '制卡日期',
  'callParam.previousCardMakingDate': '前一次制卡日期',
  'callParam.accumulatedCardMakingRequests': '累计制卡请求数',
  'callParam.smallAmountWaiverFlag': '小额免除标志',
  'callParam.co - brandedCardPartnerCode': '联名卡合作伙伴代码',
  'callParam.accountToleranceSwitchFlag': '账户容差开关标志',
  'callParam.oldAccountBlockCode': '旧账户止付代码',
  'callParam.oldAccountBlockCodeReasonCode': '旧账户止付代码原因代码',
  'callParam.accountBirthdayValidDate': '账户生日有效日期',
  'callParam.accountFreezeReasonCode': '账户冻结原因代码',
  'callParam.waiverOfOverlimitFeeFlag': '超限费免除标志',
  'callParam.accountCoreFlag': '账户核心标志',
  'callParam.accountLastDeferralDate': '账户最后延期日期',
  'callParam.30DayDeferralAmount': '30天延期金额',
  'callParam.180DayDeferralAmount': '180天延期金额',
  'callParam.30DayDeferralTimes': '30天延期次数',
  'callParam.180DayDeferralTimes': '180天延期次数',
  'callParam.withdrawalInitialBalance': '取现初始余额',
  'callParam.redemptionRepaymentOriginalBalance': '赎回还款原始余额',
  'callParam.accountInterestWaiverFlag': '账户利息免除标志',
  'callParam.lastConsumptionAmount': '最后消费金额',
  'callParam.pledgedCashLoanAdjustmentBalance': '质押现金贷款调整余额',
  'callParam.accountProduct': '账户产品',
  'callParam.statusChangeDate': '状态变更日期',
  'callParam.singleCurrencySettlementFlag': '单一货币结算标志',
  'callParam.accountBlockCodeRemarks': '账户止付代码备注',
  'callParam.automaticRepaymentAccount': '自动还款账户',
  'callParam.pureAnnualFeeDeferralFlag': '纯年费延期标志',
  'callParam.reasonForCollection': '收取原因',
  'callParam.exitCollectionDate': '退出收取日期',
  'callParam.120DayDeferralAmount': '120天延期金额',
  'callParam.120DayDeferralTimes': '120天延期次数',
  'callParam.gracePeriodDate': '宽限期日期',
  'callParam.accumulatedAccountConsumptionAmount': '累计账户消费金额',
  'callParam.lastWithdrawalAmount': '最后取现金额',
  'callParam.currentRepaymentGracePeriodFlag': '当前还款宽限期标志',
  'callParam.highestAccountBalance': '最高账户余额',
  'callParam.waiverOfOverlimitAmount': '超限金额免除',
  'callParam.cardLevel': '卡片等级',
  'callParam.cardholderInstitutionNumber': '持卡人机构编号',
  'callParam.cardBlockCode': '卡片止付代码',
  'callParam.previousCardBlockCode': '前一个卡片止付代码',
  'callParam.newCurrencyForCardReplacement': '换卡后的新货币',
  'callParam.annualFeeDiscountFlag': '年费折扣标志',
  'callParam.numberOfPasswordErrors': '密码错误次数',
  'callParam.abbreviatedCardholderName': '持卡人姓名缩写',
  'callParam.applicationFormSerialNumber': '申请表流水号',
  'callParam.marketingStaffEmployeeNumber': '营销人员员工编号',
  'callParam.closingDate': '结清日期',
  'callParam.cardTransactionScopeFlag': '卡片交易范围标志',
  'callParam.primaryOrSupplementaryCardFlag': '主卡或附属卡标志',
  'callParam.cardVersionCode': '卡片版本代码',
  'callParam.cardholderNumber': '持卡人编号',
  'callParam.cardBlockReasonCode': '卡片止付原因代码',
  'callParam.previousCardBlockReasonCode': '前一个卡片止付原因代码',
  'callParam.newProductNumberForCardReplacement': '换卡后的新产品编号',
  'callParam.annualFeeWaiverFlag': '年费免除标志',
  'callParam.passwordErrorDate': '密码错误日期',
  'callParam.cardholderName': '持卡人姓名',
  'callParam.channelCode': '渠道代码',
  'callParam.cardFeeParameterCode': '卡片费用参数代码',
  'callParam.nodeView': '节点查看',
  'callParam.nodeProps': '节点属性',
  'callParam.actionRecord': '行动记录',
  'callParam.taskId': '任务ID',
  'callParam.actionTime': '操作时间',
  'callParam.actionResult': '操作结果',
  'callParam.start': '开始',
  'callParam.preProcess': '预处理',
  'callParam.decisionDataLoad': '决策数据加载',
  'callParam.writeOffRules': '核销规则',
  'callParam.writeOffQueue': '核销队列',
  'callParam.customerTagProcess': '客户标签处理',
  'callParam.customerCollectionMark': '客户催收标识处理',
  'callParam.sectionRules': '分板块规则',
  'callParam.phoneCollectionQueue': '电催队列',
  'callParam.outsourceQueue': '委外队列',
  'callParam.smsQueue': '短信队列',
  'callParam.ivrQueue': 'IVR队列',
  'callParam.phoneCollectionGroupA': '电催A组',
  'callParam.phoneCollectionGroupB': '电催B组',
  'callParam.phoneCollectionGroupC': '电催C组',
  'callParam.success': '成功',
  'callParam.accountDataSync': '账户数据同步批处理',
  'callParam.cardDataSync': '卡片数据同步批处理',
  'callParam.cardholderDataSync': '持卡人数据同步批处理',
  'callParam.transactionDataSync': '交易信息同步批处理',
  'callParam.operationCenterProcess': '运营中心处理',
  'callParam.creditScoreNode': '信用评分节点',
  'callParam.overduePredictionNode': '逾期风险预测节点',
  'callParam.lostContactRepairNode': '失联修复文件节点',
  'callParam.defaultUserName': '张俊锋',
  'callParam.defaultEmpName': '催收一部',
  'callParam.defaultBranchName': '广州分行',
  'callParam.userCenter': '用户中心',
  'callParam.workbench': '工作台',
  'callParam.workbenchConfig': '工作台配置',
  'callParam.caseMonitoring': '案件监控',
  'callParam.phoneCollectionWorkbench': '电话催收工作台',
  'callParam.smsCollectionWorkbench': '短信催收工作台',
  'callParam.specialCaseWorkbench': '专案工作台',
  'callParam.externalCollectionWorkbench': '外访催收工作台',
  'callParam.outsourcingCollectionWorkbench': '委外催收工作台',
  'callParam.legalActionCollectionWorkbench': '诉讼催收工作台',
  'callParam.approvalManagement': '审批管理',
  'callParam.dispatchProcess': '派案',
  'callParam.amount': '金额',
  'callParam.case': '案件',
  'callParam.balance': '平衡',
  'callParam.stationDispatch': '岗位分配',
  'callParam.staffDispatch': '人员分配',
  'callParam.caseNum': '案件数量',
  'callParam.caseAmount': '案件金额',
  'callParam.dispatchSuccess': '派案成功',
  'callParam.quantity': '数量',
  'callParam.warning': '警告',
  'callParam.dispatchBalanceWarning':
    '当前分配的案件{type}总和：{newSum}，应当等于初始案件{type1}总和：{oldSum}，请重新分配',
  'callParam.submitConfirm': '确认提交？',
  'callParam.inCollection': '在催',
  'callParam.outCollection': '出催',
  'callParam.contactResult': '联系结果',
  'callParam.reviewTime': '复核时间',
  'callParam.answer': '接听',
  'callParam.dial': '拨号',
  'callParam.hold': '保持',
  'callParam.consult': '咨询',
  'callParam.conference': '会议',
  'callParam.busy': '示忙',
  'callParam.ready': '就绪',
  'callParam.afterProcess': '后处理',
  'callParam.signIn': '签入',
  'callParam.totalCases': '总案件',
  'callParam.pendingCases': '待办案件',
  'callParam.completedCases': '已办案件',
  'callParam.caseTrendChart': '案件量趋势图',
  'callParam.totalPendingCases': '总待办案件',
  'callParam.newCollectionCases': '新入催案件',
  'callParam.exitCollectionCases': '出催案件',
  'callParam.phoneCollectionCases': '电催案件',
  'callParam.smsCollectionCases': '短信催收案件',
  'callParam.outsourcingCases': '委外案件',
  'callParam.writeOffCases': '核销案件',
  'callParam.litigationCases': '诉讼案件',
  'callParam.collected': '已催',
  'callParam.mode': '模式',
  'callParam.activity': '活动',
  'callParam.spouse': '配偶',
  'callParam.direction': '方向',
  'callParam.source': '来源',
  'callParam.duration': '时长',
  'callParam.anchorCollectionInfo': '催收信息',
  'callParam.anchorCustomerInfo': '客户信息',
  'callParam.anchorContactInfo': '联系信息',
  'callParam.caseTransfer': '案件转派',
  'callParam.earlyPayment': '提前还款',
  'callParam.guaranteeDeposit': '保证金抵扣',
  'callParam.accountTransfer': '同名账户转账',
  'callParam.userId': '用户Id',
  'callParam.userName': '用户姓名',
  'callParam.optionPromiseRepayment': '承诺还款',
  'callParam.optionContactLost': '联系人失联',
  'callParam.optionNoAnswer': '联系人电话无人接听',
  'callParam.optionSevereIllness': '重疾无力还款',
  // ... existing code ...
};
