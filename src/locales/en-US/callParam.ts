export default {
  'callParam.labelManage': 'Label Management',
  'callParam.tagName': 'Label Name',
  'callParam.paramIndex': 'NO.',
  'callParam.tagType': 'Label Type',
  'callParam.tagSource': 'Label Source',
  'callParam.tagId': 'Tag ID ',
  'callParam.tagDescription': 'Label Description',
  'callParam.tagAttribute': 'Label Attributes',
  'callParam.labelType1': 'Repayment Intention',
  'callParam.labelType2': 'Repayment Ability',
  'callParam.labelType3': 'Overdue Stage',
  'callParam.labelType4': 'Risk Level',
  'callParam.labelType5': 'Transfer Tags',
  'callParam.labelType6': 'Special Tags',
  'callParam.labelNameSrc1': 'Manual Input',
  'callParam.labelNameSrc2': 'Batch Import ',
  'callParam.labelNameSrc3': 'Real Time Interface ',
  'callParam.attribute1': 'Regular Tags',
  'callParam.attribute2': 'Special Tags',
  'callParam.attribute3': 'Other',
  'callParam.labelSource1': 'Manual Import ',
  'callParam.labelSource2': 'Batch Interface ',
  'callParam.labelSource3': 'Real Time Interface ',
  'callParam.labelDesc': 'Label Description',
  'callParam.updateUser': 'Update Person',
  'callParam.updater': 'Update Person',
  'callParam.updateTime': 'Update Time ',
  'callParam.createTime': 'Creation Time ',
  'callParam.aduitUser': 'Approver',
  'callParam.aduitTime': 'Approval Time ',
  'callParam.status': 'Status',
  'callParam.ruleType': 'Rule Type',
  'callParam.pleaseSelectRuleType': 'Please select rule type',
  'callParam.rulePackageId': 'Rule Package ID',
  'callParam.pleaseSelectRulePackageId': 'Please select rule package ID',
  'callParam.selectList': 'Select List',
  'callParam.pleaseSelectList': 'Please select select list',
  'callParam.labelSetting': 'Label Setting',

  'callParam.dimensionalManage': 'Maintenance Table Management',
  'callParam.custName': 'Customer Name ',
  'callParam.custPerson': 'The Total Number Of Customer Groups',
  'callParam.custDesc': 'Customer Group Description',
  'callParam.baseInfo': 'Basic Information ',
  'callParam.invalid': 'Invalid',
  'callParam.effective': 'Effective',
  'callParam.caseManage': 'Case Information',
  'callParam.aiRulesCheck': 'AI',
  'callParam.caseInfo': 'Case Overview ',
  'callParam.caseCode': 'Case Number ',
  'callParam.chargoffFlag': 'Verification Identification ',
  'callParam.countOdue': 'Number Of Reminders',
  'callParam.icType': 'Certificate Type',
  'callParam.custIc': 'ID Number',
  'callParam.dteIntoCollection': 'Reminder Time',
  'callParam.dteOutCollection': 'Reminder Time',
  'callParam.firstDate': 'First Reminder Time ',
  'callParam.lastCaseCode': 'Case Number',
  'callParam.lastCaseDate': 'Last Case Time ',
  'callParam.lawDate': 'Litigation Date',
  'callParam.lawFlag': 'Litigation Identification',
  'callParam.model': 'Module ',
  'callParam.orgCustNbr': 'Customer Number ',
  'callParam.currState': 'Current Status',
  'callParam.caseState': 'Case Status',
  'callParam.teamCode': 'Risk Pool Number',
  'callParam.lastOpeTime': 'Recent Action Time ',
  'callParam.inTeamDate': 'Date Of Joining The Group',
  'callParam.reasonCode': 'Risk Pool Reason Code',
  'callParam.actSerialId': 'Action Path Number',
  'callParam.noneContactRuleCode': 'Missing Decision Code',
  'callParam.contactCode': 'Contact Information',
  'callParam.contactTimes': 'Number Of Manual Contacts',
  'callParam.resultCode': 'Result Code',
  'callParam.resultTime': 'Result Completion Time ',
  'callParam.resultDirection': 'Call In/Call Out',
  'callParam.attemptCount': 'The Number Of Attempts To Contact This Account',
  'callParam.clientCode': 'User Defined Result Code',
  'callParam.voiceCallType': 'Information Communication Markers',
  'callParam.specialCaseNote': 'Special Case Remarks ',
  'callParam.callTotal': 'Effective Collection Total',
  'callParam.model1': 'Electric Urging',
  'callParam.model2': 'Outsourcing',
  'callParam.model3': 'Litigation',
  'callParam.model4': 'SMS ',
  'callParam.model5': 'Verification',
  'callParam.model6': 'Project',
  'callParam.lastModel': 'Last Template',
  'callParam.ename': 'English Name',
  'callParam.eusex': 'Gender',
  'callParam.statementTypeAll': 'Statement Type',
  'callParam.billingCycle': 'Billing Date',
  'callParam.badnessCode': 'Bad Debt Identifier',
  'callParam.classIBalance': 'Total Arrears',
  'callParam.riskRank': 'Risk Level',
  'callParam.delayDays': 'Delay Days',
  'callParam.activeInstallmentFlag': 'Active Installment Flag',
  'callParam.productType': 'Large Account Identifier',
  'callParam.status24': '24-period Status',
  'callParam.wcsOutcode': 'Outsourcing Flag',
  'callParam.handType': 'Hand Type',
  'callParam.dayNum': 'Delay Days',
  'callParam.caseAmt': 'Overdue Amount',
  'callParam.pending': 'Pending',
  'callParam.completed': 'Completed',
  'callParam.aduitManage': 'Approval Management',
  'callParam.aduitPage': 'Approval Items ',
  'callParam.aduitStatus': 'Approval Status ',
  'callParam.applyUser': 'The Applicant',
  'callParam.applyTime': 'Application Time ',
  'callParam.dispatchManage': 'Node Process ',
  'callParam.flowchartId': 'Node ID ',
  'callParam.flowchartName': 'Node Name',
  'callParam.description': 'Description',
  'callParam.flowchartText': 'Node Flowchart',
  'callParam.nodeExecStatus': 'Node Execution Status',
  'callParam.nodeExecCount': 'Number Of Node Executions',
  'callParam.lastExecDuration': 'Last Execution Duration',
  'callParam.lastExecTime': 'Last Execution Time ',
  'callParam.nextNode': 'Next Node',
  'callParam.nodeProgram': 'Node Program',
  'callParam.nodeProStrategy': 'Rule Strategy',
  'callParam.nodeRule': 'Rules',
  'callParam.nodeRuleFactor': 'Rule Factor',
  'callParam.operator': 'Operator ',
  'callParam.nodeRuleValue': 'Value',
  'callParam.nodeRuleFactorResult': 'Rule Result',
  'callParam.phone': 'Phone Collection Workbench',
  'callParam.message': 'SMS Collection Workbench',
  'callParam.nodeConfig': 'Node Management',
  'callParam.nodeCode': 'Node ID ',
  'callParam.nodeNodeName': 'Node Name',
  'callParam.nodeFuncDesc': 'Node Description',
  'callParam.nodeType': 'Node Type',
  'callParam.nodeAuth': 'Node Permissions ',
  'callParam.priority': 'Sequence ',
  'callParam.nodeAttriId': 'Node Processing Program',
  'callParam.programCode': 'Program Code',
  'callParam.cycleStartFlag': 'Cycle Start Flag',

  'callParam.nodeAuth1': 'The Salesperson',
  'callParam.nodeAuth2': 'Leader',
  'callParam.nodeAuth3': 'Approver',
  'callParam.nodeAuth4': 'Administrator',
  'callParam.nodeType0': 'Start Node ',
  'callParam.nodeType1': 'Technical Nodes',
  'callParam.nodeType2': 'Business Node',
  'callParam.nodeStatus1': 'In The Process Of Editing',
  'callParam.nodeStatus2': 'In Effect',
  'callParam.nodeStatus3': 'Under Approval',
  'callParam.nodeStatus4': 'Deactivation/Invalidation',
  'callParam.nodeFlowManage': 'Node Processing Program',
  'callParam.nodeFlowId': 'Program ID ',
  'callParam.nodeFlowName': ' Program Name',
  'callParam.nodeFlowCode': 'Support Node Code ',
  'callParam.nodeFlowDesc': 'Introduction',
  'callParam.nodeFlowUp': 'Instance Limit',
  'callParam.nodeAttribute': 'Program Management',
  'callParam.nodeAttriName': 'Program Name',
  'callParam.nodeRuleype': 'Association Rules',
  'callParam.nodeAttriType': 'Program Type',
  'callParam.nodeAttriDesc': 'Introduction',
  'callParam.nodeAttriCode': 'Support Node Code',
  'callParam.nodeAttriUp': 'Instance Limit',
  'callParam.nodeAttriType1': 'Data Synchronization',
  'callParam.nodeAttriType2': 'Label Processing',
  'callParam.nodeAttriType3': 'Third Party Interface',
  'callParam.nodeAttriType4': 'This System Interface',
  'callParam.department': 'Department',
  'callParam.groups': 'Group',
  'callParam.integrateManageWorkBench': 'Workbench Management',
  'callParam.userId': 'User ID ',
  'callParam.userName': 'User Name ',
  'callParam.workBenchName': 'Configuration Page',
  'callParam.workBenchModel': 'Display Tab ',
  'callParam.auxiliaryFunctions': 'Function List ',
  'callParam.authPage1': 'Phone Collection Workbench',
  'callParam.authPage2': 'SMS Collection Workbench',
  'callParam.authPage3': 'Verification And Collection Workbench',
  'callParam.authPage4': 'Outsourcing Collection Workbench',
  'callParam.authPage5': 'The Legal Collection Workbench',
  'callParam.authPage6': 'Project Workbench',
  'callParam.rightAction1': 'Collection',
  'callParam.rightAction2': 'Customer',
  'callParam.rightAction3': 'Contact',
  'callParam.rightAction4': 'Account',
  'callParam.rightAction5': 'Card',
  'callParam.leftAction1': 'Transfer Case',
  'callParam.leftAction2': 'Advance Payment ',
  'callParam.leftAction3': 'Stop Payment',
  'callParam.leftAction4': 'Reduction',
  'callParam.leftAction5': 'Margin Offset',
  'callParam.leftAction6': 'Account Reconciliation With The Same Name ',
  'callParam.leftAction7': 'Remove Mandatory Bidding',
  'callParam.leftAction8': 'Negotiate Installment Payments',
  'callParam.leftAction9': 'The Light Wealth Order',
  'callParam.leftAction10': 'Information Restoration',
  'callParam.leftAction11': 'SMS ',
  'callParam.leftAction12': 'WeChat ',
  'callParam.leftAction13': 'APP',
  'callParam.leftAction14': 'Email ',
  'callParam.leftAction15': 'The Letter',
  'callParam.callFields': 'Collection Information Fields',
  'callParam.customFields': 'Customer Information Fields',
  'callParam.contact': 'Contact Information Fields',
  'callParam.aiRulesTitle': 'Intelligent Rule Check',
  'callParam.ruleTypeLabel': 'Please Enter The Rule Type To Check:',
  'callParam.ruleTypePlaceholder': 'e.g.: Node Process Rules',
  'callParam.promptLabel': 'Please Enter The Prompt:',
  'callParam.promptPlaceholder': 'Please Enter The Content To Be Checked',
  'callParam.startCheck': 'Start Check',
  'callParam.startCheckMessage': 'Start Checking',
  'callParam.pleaseComplete': 'Please Fill In Complete Information',
  'callParam.noNodeInfo': 'No Node Information',
  'callParam.transferDispatchSuccess': 'Transfer Dispatch Successful',
  'callParam.dispatchStrategy': 'Dispatch Strategy',
  'callParam.targetQueue': 'Target Queue',
  'callParam.transfer': 'Transfer',
  'callParam.dispatch': 'Dispatch',
  'callParam.collectionRecord': 'Collection Record',
  'callParam.casePool': 'Case Pool',
  'callParam.caseMyCase': 'My Case',
  'callParam.robbingCases': 'Robbing Cases',
  'callParam.refresh': 'Refresh',
  'callParam.reviewDate': 'Review Time',
  'callParam.commitmentAmount': 'Commitment Amount',
  'callParam.commitmentDate': 'Commitment Time',
  'callParam.dailFlag': 'Continue Dialing',
  'callParam.callDuration': 'Call Duration',
  'callParam.startTime': 'Call Start Time',
  'callParam.endTime': 'Call End Time',
  'callParam.audioCode': 'Recording Number',
  'callParam.actionDate': 'Action Date',
  'callParam.obType': 'Outbound Type',
  'callParam.telType': 'Phone Type',
  'callParam.callType': 'Call Direction',
  'callParam.outbound': 'Outbound',
  'callParam.mobile': 'Mobile',
  'callParam.callOut': 'Call Out',
  'callParam.id': 'ID',
  'callParam.customerNo': 'Customer No.',
  'callParam.templateCode': 'Template Code',
  'callParam.templateName': 'Template Name',
  'callParam.mobileNumber': 'Mobile Number',
  'callParam.sendContent': 'Send Content',
  'callParam.receiverName': 'Receiver Name',
  'callParam.senderCollector': 'Sending Collector',
  'callParam.sendTime': 'Send Time',
  'callParam.taskDate': 'Task Date',
  'callParam.creator': 'Creator',
  'callParam.cardNumber': 'Card Number',
  'callParam.transactionCode': 'Transaction Code',
  'callParam.transactionCurrency': 'Transaction Currency',
  'callParam.transactionAmount': 'Transaction Amount',
  'callParam.postingCurrency': 'Posting Currency',
  'callParam.postingAmount': 'Posting Amount',
  'callParam.transactionDate': 'Transaction Date',
  'callParam.postingDate': 'Posting Date',
  'callParam.transactionDescription': 'Notes/Transaction Description',
  'callParam.smsSuccess': 'SMS Sent Successfully',
  'callParam.connected': 'Connected',
  'callParam.rejected': 'Rejected',
  'callParam.selectPlaceholder': 'Please Select',
  'callParam.amountBalance': 'Amount Balance',
  'callParam.caseBalance': 'Quantity Balance',
  'callParam.amountAndCaseBalance': 'Amount + Quantity Balance',
  'callParam.teamA': 'Telecollection Team A',
  'callParam.teamB': 'Telecollection Team B',
  'callParam.teamC': 'Telecollection Team C',
  'callParam.outsourcingQueue': 'Outsourcing Queue',
  'callParam.litigationQueue': 'Litigation Queue',
  'callParam.collectionInfoOverview': 'Collection Information Overview',
  'callParam.stationManagement': 'Position Management',
  'callParam.staffManagement': 'Staff Management',
  'callParam.stationId': 'ID',
  'callParam.position': 'Position',
  'callParam.weightCoefficient': 'Weight Coefficient',
  'callParam.staffName': 'Name ',
  'callParam.staff': 'Staff',
  'callParam.preview': 'Result',
  'callParam.station1': 'Customer Manager',
  'callParam.station2': 'Credit QA',
  'callParam.station3': 'Collection QA',
  'callParam.customerManager': 'Customer Manager',
  'callParam.collectionQA': 'Collection QA',
  'callParam.collectionSpecialist': 'Collection Specialist',
  'callParam.seniorCollector': 'Senior Collector',
  'callParam.expertCollector': 'Expert Collector',
  'callParam.collectionSupervisor': 'Collection Supervisor',
  'callParam.seniorSupervisor': 'Senior Supervisor',
  'callParam.paramSettingTitle': 'Multi-Tab Handler - Parameter Settings',
  'callParam.paramName': 'Parameter Name',
  'callParam.paramDesc': 'Description',
  'callParam.type': 'Type',
  'callParam.value': 'Value',
  'callParam.action': 'Action',
  'callParam.add': 'Add',
  'callParam.edit': 'Edit',
  'callParam.delete': 'Delete',
  'callParam.save': 'Save',
  'callParam.cancel': 'Cancel',
  'callParam.deleteConfirm': 'Are you sure to delete?',
  'callParam.pleaseFinishParamFirst': 'Please complete the unfinished parameter first',
  'callParam.input': 'Input',
  'callParam.output': 'Output',
  'callParam.optionPromiseRepayment': 'Promise to Repay',
  'callParam.optionContactLost': 'Contact Lost',
  'callParam.optionNoAnswer': 'Contact No Answer',
  'callParam.optionSevereIllness': 'Severe Illness, Unable to Repay',
  // ... existing code ...
};
