export default {
  'strategy.strategyManage': 'Strategy Management',
  'strategy.strategyId': 'Strategy ID',
  'strategy.strategyName': 'Strategy Name',
  'strategy.strategyType': 'Strategy Type',
  'strategy.considerInventory': 'Consider Inventory',
  'strategy.considerWeight': 'Consider Weight',
  'strategy.weightStrategyId': 'Main Strategy ID when using weight strategy',
  'strategy.description': 'Strategy Description',
  'strategy.strategySetting': 'Strategy Setting',
  'strategy.factorId': 'Factor ID',
  'strategy.coefficient': 'Co-efficient',
  'strategy.performance': 'Performance',
  'strategy.factorType': 'Factor Type',
  'strategy.factorLevel': 'Factor Level',
  'strategy.performanceMin': 'Performance Minimum',
  'strategy.performanceMax': 'Performance Maximum',
  'strategy.weightingFactor': 'Weighting Factor',
  'strategy.weightRatio': 'Weight Ratio',
  'strategy.id': 'Primary Key ID',
  'strategy.weightStrategyName': 'Strategy Name',
  'strategy.effectiveStart': 'Effective Start Date',
  'strategy.effectiveEnd': 'Effective End Date',
  // 排序模板
  'strategy.templateId': 'Sort Template ID',
  'strategy.templateName': 'Sort Template Name',
  'strategy.positionId': 'Position ID',
  'strategy.positionName': 'Position Name',
  'strategy.status': 'Status',
  'strategy.priority': 'Priority',
  'strategy.fieldName': 'Field Name',
  'strategy.fieldDisplay': 'Field Display Name',
  'strategy.sortOrder': 'Sort Order',
  'strategy.sortSetting': 'Sort Setting',
};
