export default {
  'systemManage.baseInfo': 'Basic Information',
  // User Management
  'systemManage.userManage': 'User Management',
  'systemManage.userId': 'User ID',
  'systemManage.userName': 'Username',
  'systemManage.orgName': 'Organization Name',
  'systemManage.role': 'Role',
  'systemManage.position': 'Position',
  'systemManage.gender': 'Gender',
  'systemManage.language': 'Language',
  'systemManage.level': 'Level',
  'systemManage.subPositionPermission': 'Subordinate Data Permission',
  'systemManage.originalPassword': 'Original Password',
  'systemManage.dept': 'Department',
  // Position Management
  'systemManage.positionManage': 'Position Management',
  'systemManage.positionId': 'Position ID',
  'systemManage.positionName': 'Position Name',
  'systemManage.parentName': 'Superior Position',
  'systemManage.description': 'Position Description',
  // Role Management
  'systemManage.roleManage': 'Role Management',
  'systemManage.roleId': 'Role ID',
  'systemManage.roleStatus': 'Role Status',
  'systemManage.mountStatus': 'Mount Status',
  'systemManage.action': 'Action',
  'systemManage.roleName': 'Role Name',
  'systemManage.enable': 'Enable',
  'systemManage.unEnable': 'Disable',
  'systemManage.mounted': 'Mounted',
  'systemManage.unmounted': 'Unmounted',
  'systemManage.roleNamePlaceholder': 'Please enter role name',
  'systemManage.roleStatusPlaceholder': 'Please select role status',
  'systemManage.mountStatusPlaceholder': 'Please select mount status',
  'systemManage.viewMount': 'View Mount Details',
  'systemManage.editMount': 'Edit Mount',
  'systemManage.tip': 'Tip',
  'systemManage.editMountSuccess': 'Role permission mount edited successfully',
  'systemManage.editSuccess': 'Edit successful!',
  'systemManage.addSuccess': 'Add successful!',
  'systemManage.view': 'Search',
  'systemManage.reset': 'Reset',
  'systemManage.add': 'Add',
  'systemManage.roleDesc': 'Role Description',
  'systemManage.roleIdPlaceholder': 'Please enter role ID',
  'systemManage.roleDescPlaceholder': 'Please enter role description',
  'systemManage.limit16': 'Cannot exceed 16 characters',
  'systemManage.limit2': 'Cannot exceed 2 characters',
  'systemManage.limit200': 'Cannot exceed 200 characters',
  'systemManage.copyObject': 'Copy Object',
  'systemManage.menuPermissions': 'Menu Permissions',
  'systemManage.viewMountDetail': 'View Role Menu Mount',
  'systemManage.editMountDetail': 'Edit Role Menu Mount',
  'systemManage.mountDetail': 'Role Mount',
  'systemManage.warningMsg': 'Disabled roles cannot be edited',
  'systemManage.permissionConfig': 'Permission Config',
  'systemManage.totalItems': 'Total {count}',
  'systemManage.selectItems': 'Selected {count}',
  'systemManage.userDetailDesc':
    'This only shows the user information of the current role, if you need to remove or add users, please go to the user management module to modify the role permissions for the user',
  // org
  'systemManage.orgTree': 'Organization Tree',
  'systemManage.orgId': 'Organization ID',
  'systemManage.parentOrg': 'Parent Organization',
  'systemManage.orgType': 'Type',
  'systemManage.orgCount': 'Department Count',
  'systemManage.edit': 'Edit',
  'systemManage.delete': 'Delete',
  'systemManage.orgTreeRoot': 'GF Bank',
  'systemManage.orgTreeCreditCard': 'Credit Card Center',
  'systemManage.orgTreeOps': 'Operation Management',
  'systemManage.orgTreeDecision': 'Decision Dept',
  'systemManage.orgTreeOperation': 'Operation Dept',
  'systemManage.orgTreeSystem': 'System Dept',
  'systemManage.orgTreeHead': 'Head Office',
  'systemManage.orgTreeItem13': 'Item 1.3',
  'systemManage.orgTypeDepartment': 'Department',
  'systemManage.orgTypeCompany': 'Company',
  'systemManage.orgTypeBranch': 'Branch',
  'systemManage.orgTypeSubsidiary': 'Subsidiary',
  'systemManage.orgParentOps': 'Operations',
  'systemManage.orgParentDecision': 'Decision',
  'systemManage.orgParentHead': 'Headquarters',
  'systemManage.orgParentSystem': 'System',
  'systemManage.addOrg': 'Add Organization',
  'systemManage.searchOrgPlaceholder': 'Search organization name',
  'systemManage.orgIdRequired': 'Please enter organization ID',
  'systemManage.orgIdPlaceholder': 'Please enter organization ID',
  'systemManage.orgNameRequired': 'Please enter organization name',
  'systemManage.orgNamePlaceholder': 'Please enter organization name',
  'systemManage.parentOrgRequired': 'Please select parent organization',
  'systemManage.parentOrgPlaceholder': 'Please select parent organization',
  'systemManage.orgTypeRequired': 'Please select type',
  'systemManage.orgTypePlaceholder': 'Please select type',
  // 数据字典
  'systemManage.dictManage': 'Dictionary Management',
  'systemManage.dictName': 'Dictionary Name',
  'systemManage.dictCode': 'Dictionary Code',
  'systemManage.dictType': 'Dictionary Type',
  'systemManage.sortOrder': 'Sort Order',
  'systemManage.dictItem': 'Dictionary Item',
  'systemManage.itemCode': 'Dictionary Item Code',
  'systemManage.itemName': 'Dictionary Item Name',
  'systemManage.itemValue': 'Dictionary Item Value',
  'systemManage.itemDesc': 'Dictionary Item Description',
  'systemManage.status': 'Status',
  'systemManage.dictDesc': 'Dictionary Description',
  'systemManage.statusEnabled': 'Enabled',
  'systemManage.statusDisabled': 'Disabled',
  'systemManage.save': 'Save',
  'systemManage.cancel': 'Cancel',
  // 菜单管理
  'systemManage.menuManage': 'Menu Management',
  'systemManage.menuId': 'Menu ID', // 菜单ID
  'systemManage.menuName': 'Menu Name', // 菜单名称
  'systemManage.menuStatus': 'Menu Status', // 菜单状态
  'systemManage.open': 'Open', // 开启
  'systemManage.close': 'Close', // 关闭
  'systemManage.menuNamePlaceholder': 'Please enter menu name', // 请输入菜单名称
  'systemManage.menuStatusPlaceholder': 'Please select status', // 请选择状态
  'systemManage.sort': 'Sort', // 排序
  'systemManage.menuType': 'Menu Type', // 菜单类型
  'systemManage.createTs': 'Create Time', // 创建时间
  'systemManage.updateTs': 'Update Time', // 更新时间
  'systemManage.menu': 'Menu', // 菜单
  'systemManage.button': 'Button', // 按钮
  'systemManage.menuInfo': 'Menu Information', // 菜单信息
  'systemManage.expanded': 'Expand', // 展开
  'systemManage.unexpanded': 'Collapse', // 折叠
  'systemManage.opFailed': 'Operation Failed', // 操作失败
  'systemManage.supMenu': 'Superior Menu', // 上级菜单
  'systemManage.supMenuPlaceholder': 'Please select superior menu', // 请选择上级菜单
  'systemManage.menuTypePlaceholder': 'Please select menu type', // 请选择菜单类型
  'systemManage.limit150': 'Cannot exceed 150 characters', // 不能超过150个字
  'systemManage.routeUrl': 'Route URL', // Route URL
  'systemManage.displaySorting': 'Display Sorting', // 展示排序
  'systemManage.displaySortPlaceholder': 'Please enter display sorting', // 请输入展示排序
  'systemManage.permissionId': 'Permission ID',
  'systemManage.iconId': 'Icon ID',
  'systemManage.iconSelect': 'Icon Select',
  'systemManage.direction': 'Directional Icon',
  'systemManage.suggestion': 'Suggestion Icon',
  'systemManage.editor': 'Editor Icon',
  'systemManage.data': 'Data Icon',
  'systemManage.logo': 'Brand and Identity',
  'systemManage.other': 'General Website Icons',
  'systemManage.messTip': 'Current Selected Icon:',
  'systemManage.addIcon': 'Select Icon',
  // 催收员工作台
  'systemManage.customLayout': 'Custom Layout',
  'systemManage.selectField': 'Select Field',
  'systemManage.selectedFields': 'Selected Fields',
  'systemManage.noSelectedFields': 'No selected fields, please add from above',
  'systemManage.enterFieldName': 'Please enter field name',
  'systemManage.availableFields': 'Available Fields',
};
