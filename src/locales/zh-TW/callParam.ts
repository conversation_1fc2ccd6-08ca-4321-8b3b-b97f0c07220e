export default {
  // 標籤管理頁面
  'callParam.labelManage': '標籤管理',
  'callParam.tagName': '標籤名稱',
  'callParam.paramIndex': '序號',
  'callParam.tagType': '標籤類型',
  'callParam.tagSource': '標籤來源',
  'callParam.tagId': '標籤ID',
  'callParam.tagDescription': '標籤描述',
  'callParam.tagAttribute': '標籤屬性',
  'callParam.labelType1': '還款意願',
  'callParam.labelType2': '還款能力',
  'callParam.labelType3': '逾期階段',
  'callParam.labelType4': '風險等級',
  'callParam.labelType5': '流轉判斷',
  'callParam.labelType6': '特殊標籤',
  'callParam.labelNameSrc1': '手動輸入',
  'callParam.labelNameSrc2': '批量導入',
  'callParam.labelNameSrc3': '實時接口',
  'callParam.attribute1': '常規標籤',
  'callParam.attribute2': '特殊標籤',
  'callParam.attribute3': '其他',
  'callParam.labelSource1': '手工導入',
  'callParam.labelSource2': '批量接口',
  'callParam.labelSource3': '實時接口',
  'callParam.labelDesc': '標籤描述',
  'callParam.updateUser': '更新人',
  'callParam.updater': '更新人',
  'callParam.updateTime': '更新時間',
  'callParam.createTime': '創建時間',
  'callParam.aduitUser': '審批人',
  'callParam.aduitTime': '審批時間',
  'callParam.status': '狀態',
  'callParam.ruleType': '規則類型',
  'callParam.pleaseSelectRuleType': '请选择规则类型',
  'callParam.rulePackageId': '規則包ID',
  'callParam.pleaseSelectRulePackageId': '请选择规则包ID',
  'callParam.selectList': '選擇列表',
  'callParam.pleaseSelectList': '请选择选择列表',
  'callParam.labelSetting': '標籤設置',

  // 維表管理頁面
  'callParam.dimensionalManage': '維表管理',
  'callParam.custName': '客戶名稱',
  'callParam.custPerson': '客群總數',
  'callParam.custDesc': '客群描述',
  'callParam.baseInfo': '基礎信息',
  'callParam.invalid': '失效',
  'callParam.effective': '有效',

  // 案件基本信息頁面
  'callParam.caseManage': '案件信息',
  'callParam.aiRulesCheck': 'AI輔助',
  'callParam.caseInfo': '案件概覽',
  'callParam.caseCode': '案件號',
  'callParam.chargoffFlag': '核銷標識',
  'callParam.countOdue': '入催次數',
  'callParam.icType': '證件類型',
  'callParam.custIc': '證件號',
  'callParam.dteIntoCollection': '入催時間',
  'callParam.dteOutCollection': '出催時間',
  'callParam.firstDate': '首次入催時間',
  'callParam.lastCaseCode': '上案件號',
  'callParam.lastCaseDate': '上次案件時間',
  'callParam.lawDate': '訴訟日期',
  'callParam.lawFlag': '訴訟標識',
  'callParam.model': '模塊',
  'callParam.orgCustNbr': '客戶號',
  'callParam.currState': '當前狀態',
  'callParam.caseState': '案件狀態',
  'callParam.teamCode': '風險池編號',
  'callParam.lastOpeTime': '最近行動時間',
  'callParam.inTeamDate': '入組日期',
  'callParam.reasonCode': '風險池原因碼',
  'callParam.actSerialId': '行動路徑編號',
  'callParam.noneContactRuleCode': '失聯決策代碼',
  'callParam.contactCode': '聯繫情況',
  'callParam.contactTimes': '人工聯繫次數',
  'callParam.resultCode': '結果代碼',
  'callParam.resultTime': '結果完成時間',
  'callParam.resultDirection': '呼入/呼出',
  'callParam.attemptCount': '嘗試聯繫這個帳戶的次數',
  'callParam.clientCode': '用戶定義的結果代碼',
  'callParam.voiceCallType': '信息傳達標記',
  'callParam.specialCaseNote': '特別案件備註',
  'callParam.callTotal': '有效催收總數',
  'callParam.lastModel': '上一模板',
  'callParam.ename': '英文名',
  'callParam.eusex': '性別',
  'callParam.statementTypeAll': '賬單類型',
  'callParam.billingCycle': '賬單日',
  'callParam.badnessCode': '不良標識域',
  'callParam.classIBalance': '總欠款',
  'callParam.riskRank': '風險等級',
  'callParam.delayDays': '延滯天數',
  'callParam.activeInstallmentFlag': '活躍分期標識',
  'callParam.productType': '大額賬戶標識',
  'callParam.status24': '24期狀況',
  'callParam.wcsOutcode': '委外標誌',
  'callParam.handType': '手別',
  'callParam.model1': '電催',
  'callParam.model2': '委外',
  'callParam.model3': '訴訟',
  'callParam.model4': '短信',
  'callParam.model5': '核銷',
  'callParam.model6': '專案',
  'callParam.dayNum': '延滯天數',
  'callParam.caseAmt': '逾期金額',
  'callParam.pending': '待辦',
  'callParam.completed': '結案',

  // 審批管理頁面
  'callParam.aduitManage': '審批管理',
  'callParam.aduitPage': '審批項',
  'callParam.aduitStatus': '審批狀態',
  'callParam.applyUser': '申請人',
  'callParam.applyTime': '申請時間',

  // 節點流程頁面
  'callParam.dispatchManage': '節點流程',
  'callParam.flowchartId': '節點ID',
  'callParam.flowchartName': '節點名稱',
  'callParam.description': '描述',
  'callParam.flowchartText': '節點流程圖',
  'callParam.nodeExecStatus': '節點執行狀態',
  'callParam.nodeExecCount': '節點執行次數',
  'callParam.lastExecDuration': '最後一次執行時長',
  'callParam.lastExecTime': '最後一次執行時間',
  'callParam.nextNode': '下一節點',
  'callParam.nodeProgram': '節點程序',
  'callParam.nodeProStrategy': '流轉策略',
  'callParam.nodeRule': '規則',
  'callParam.nodeRuleFactor': '規則因子',
  'callParam.nodeRuleFactorResult': '規則結果',
  'callParam.operator': '運算符',
  'callParam.nodeRuleValue': '值',

  // 電話催收工作台
  'callParam.phone': '電話催收工作台',

  // 短信催收工作台
  'callParam.message': '短信催收工作台',

  // 節點管理
  'callParam.nodeConfig': '節點管理',
  'callParam.nodeCode': '節點ID',
  'callParam.nodeNodeName': '節點名稱',
  'callParam.nodeFuncDesc': '節點說明',
  'callParam.nodeType': '節點類型',
  'callParam.nodeAuth': '節點權限',
  'callParam.priority': '優先級',
  'callParam.nodeAttriId': '節點處理程序',
  'callParam.programCode': '程序代碼',
  'callParam.cycleStartFlag': '循環起始標誌',
  'callParam.nodeAuth1': '業務員',
  'callParam.nodeAuth2': '組長',
  'callParam.nodeAuth3': '審批人',
  'callParam.nodeAuth4': '管理員',
  'callParam.nodeType0': '開始節點',
  'callParam.nodeType1': '技術節點',
  'callParam.nodeType2': '業務節點',
  'callParam.nodeStatus1': '編輯中',
  'callParam.nodeStatus2': '生效中',
  'callParam.nodeStatus3': '審批中',
  'callParam.nodeStatus4': '停用/失效',

  // 處理处理程序管理
  'callParam.nodeFlowManage': '節點處理程序',
  'callParam.nodeFlowId': '程序ID',
  'callParam.nodeFlowName': '程序名稱',
  'callParam.nodeFlowCode': '支持節點代碼',
  'callParam.nodeFlowDesc': '簡介',
  'callParam.nodeFlowUp': '實例上限',

  // 处理程序管理
  'callParam.nodeAttribute': '处理程序管理',
  'callParam.nodeAttriName': '程序名稱',
  'callParam.nodeAttriType': '程序類型',
  'callParam.nodeRuleype': '關聯規則',
  'callParam.nodeAttriDesc': '簡介',
  'callParam.nodeAttriCode': '支持節點代碼',
  'callParam.nodeAttriUp': '實例上限',
  'callParam.nodeAttriType1': '數據同步',
  'callParam.nodeAttriType2': '標籤處理',
  'callParam.nodeAttriType3': '第三方接口',
  'callParam.nodeAttriType4': '本系統接口',
  'callParam.department': '部門',
  'callParam.groups': '組別',

  // 綜合管理-工作台配置
  'callParam.integrateManageWorkBench': '工作台配置',
  'callParam.userId': '用戶Id',
  'callParam.userName': '用戶姓名',
  'callParam.workBenchName': '配置頁面',
  'callParam.workBenchModel': '展示模塊',
  'callParam.callFields': '催收信息字段',
  'callParam.customFields': '客戶信息字段',
  'callParam.contact': '聯繫信息字段',
  'callParam.auxiliaryFunctions': '輔助功能',
  'callParam.authPage1': '電話催收工作台',
  'callParam.authPage2': '短信催收工作台',
  'callParam.authPage3': '核銷催收工作台',
  'callParam.authPage4': '委外催收工作台',
  'callParam.authPage5': '法訴催收工作台',
  'callParam.authPage6': '專案工作台',
  'callParam.rightAction1': '催收信息',
  'callParam.rightAction2': '客戶信息',
  'callParam.rightAction3': '聯繫信息',
  'callParam.rightAction4': '賬戶信息',
  'callParam.rightAction5': '卡片信息',
  'callParam.leftAction1': '轉案',
  'callParam.leftAction2': '提前支付',
  'callParam.leftAction3': '止付',
  'callParam.leftAction4': '減免',
  'callParam.leftAction5': '保證金抵欠',
  'callParam.leftAction6': '同名賬戶調賬',
  'callParam.leftAction7': '上除強催標',
  'callParam.leftAction8': '協商分期',
  'callParam.leftAction9': '輕財訂單',
  'callParam.leftAction10': '信息修復',
  'callParam.leftAction11': '短信',
  'callParam.leftAction12': '微信',
  'callParam.leftAction13': 'APP',
  'callParam.leftAction14': '電郵',
  'callParam.leftAction15': '信函',

  // AI規則
  'callParam.aiRulesTitle': '智能規則檢查',
  'callParam.ruleTypeLabel': '請輸入要檢查的規則類型：',
  'callParam.ruleTypePlaceholder': '例如：節點流程規則',
  'callParam.promptLabel': '請輸入提示詞：',
  'callParam.promptPlaceholder': '請輸入需要檢查的內容',
  'callParam.startCheck': '開始檢查',
  'callParam.startCheckMessage': '開始檢查',
  'callParam.pleaseComplete': '請填寫完整信息',

  // 案件管理
  'callParam.noNodeInfo': '無節點信息',
  'callParam.transferDispatchSuccess': '轉派成功',
  'callParam.dispatchStrategy': '派案策略',
  'callParam.targetQueue': '目標隊列',
  'callParam.transfer': '轉案',
  'callParam.dispatch': '派案',
  'callParam.collectionRecord': '催收記錄',
  'callParam.casePool': '案件池',
  'callParam.caseMyCase': '我的案件',
  'callParam.reviewDate': '復核時間',
  'callParam.commitmentAmount': '承諾金額',
  'callParam.commitmentDate': '承諾時間',
  'callParam.dailFlag': '是否繼續撥號',
  'callParam.callDuration': '通話時長',
  'callParam.startTime': '通話開始時間',
  'callParam.endTime': '通話結束時間',
  'callParam.audioCode': '錄音編號',
  'callParam.actionDate': '行動日期',
  'callParam.obType': '外呼類型',
  'callParam.telType': '電話類型',
  'callParam.callType': '通話方向',
  'callParam.outbound': '外呼',
  'callParam.mobile': '手機',
  'callParam.callOut': '呼出',
  'callParam.id': '編號',
  'callParam.customerNo': '客戶號',
  'callParam.templateCode': '模板編號',
  'callParam.templateName': '模板名稱',
  'callParam.mobileNumber': '手機號碼',
  'callParam.sendContent': '發送內容',
  'callParam.receiverName': '接收人姓名',
  'callParam.senderCollector': '發送催收員',
  'callParam.sendTime': '發送時間',
  'callParam.taskDate': '任務日期',
  'callParam.creator': '創建人',
  'callParam.cardNumber': '卡號',
  'callParam.transactionCode': '交易代碼',
  'callParam.transactionCurrency': '交易貨幣',
  'callParam.transactionAmount': '交易金額',
  'callParam.postingCurrency': '入賬貨幣',
  'callParam.postingAmount': '入賬金額',
  'callParam.transactionDate': '交易日期',
  'callParam.postingDate': '入賬日期',
  'callParam.smsSuccess': '短信發送成功',
  'callParam.connected': '已接通',
  'callParam.rejected': '拒接',
  'callParam.selectPlaceholder': '請選擇',
  'callParam.amountBalance': '金額均衡',
  'callParam.caseBalance': '數量均衡',
  'callParam.amountAndCaseBalance': '金額 + 數量均衡',
  'callParam.teamA': '電催A組',
  'callParam.teamB': '電催B組',
  'callParam.teamC': '電催C組',
  'callParam.outsourcingQueue': '委外隊列',
  'callParam.litigationQueue': '訴訟隊列',

  // 工作台
  'callParam.collectionInfoOverview': '催收信息概覽',

  // 崗位管理
  'callParam.stationManagement': '崗位管理',
  'callParam.staffManagement': '人員管理',
  'callParam.stationId': 'ID',
  'callParam.position': '崗位',
  'callParam.staff': '人員',
  'callParam.preview': '結果',
  'callParam.weightCoefficient': '分案權重',
  'callParam.staffName': '姓名',
  'callParam.station1': '客戶經理',
  'callParam.station2': '授信QA',
  'callParam.station3': '催收QA',
  'callParam.customerManager': '客戶經理',
  'callParam.collectionQA': '催收QA',
  'callParam.collectionSpecialist': '催收專員',
  'callParam.seniorCollector': '高級催收員',
  'callParam.expertCollector': '資深催收員',
  'callParam.collectionSupervisor': '催收主管',
  'callParam.seniorSupervisor': '高級主管',

  'callParam.paramSettingTitle': '多標籤處理程序 - 參數設置',
  'callParam.paramName': '變量名',
  'callParam.paramDesc': '參數描述',
  'callParam.type': '類型',
  'callParam.value': '取值',
  'callParam.action': '操作',
  'callParam.add': '新增',
  'callParam.edit': '編輯',
  'callParam.delete': '刪除',
  'callParam.save': '保存',
  'callParam.cancel': '取消',
  'callParam.deleteConfirm': '確定刪除?',
  'callParam.pleaseFinishParamFirst': '請先填寫未完成的參數項',
  'callParam.input': '入參',
  'callParam.output': '出參',
  'callParam.optionPromiseRepayment': '承諾還款',
  'callParam.optionContactLost': '聯絡人失聯',
  'callParam.optionNoAnswer': '聯絡人電話無人接聽',
  'callParam.optionSevereIllness': '重病無力還款',
  // ... existing code ...
};
