export default {
  'strategy.strategyManage': '策略管理',
  'strategy.strategyId': '策略ID',
  'strategy.strategyName': '策略名稱',
  'strategy.strategyType': '策略類型',
  'strategy.considerInventory': '是否考慮庫存',
  'strategy.considerWeight': '是否考慮權重',
  'strategy.weightStrategyId': '當使用權重策略時，關聯的主策略ID',
  'strategy.description': '策略描述',
  'strategy.strategySetting': '策略設定',
  'strategy.factorId': '因子ID',
  'strategy.coefficient': '係數',
  'strategy.performance': '业绩',
  'strategy.factorType': '因子類型',
  'strategy.factorLevel': '因子級別',
  'strategy.performanceMin': '业绩最低值',
  'strategy.performanceMax': '业绩最高值',
  'strategy.weightingFactor': '加权係數',
  'strategy.weightRatio': '權重係數',
  'strategy.id': '主鍵ID',
  'strategy.weightStrategyName': '策略名稱',
  'strategy.effectiveStart': '策略生效開始日期',
  'strategy.effectiveEnd': '策略生效結束日期',
  // 排序模板
  'strategy.templateId': '排序模板ID',
  'strategy.templateName': '排序模板名稱',
  'strategy.positionId': '排序岗位ID',
  'strategy.positionName': '排序岗位名称',
  'strategy.status': '狀態',
  'strategy.priority': '優先級',
  'strategy.fieldName': '字段名稱',
  'strategy.fieldDisplay': '字段顯示名稱',
  'strategy.sortOrder': '排序順序',
  'strategy.sortSetting': '排序設定',
};
