import type { Rule } from 'antd/lib/form';
import { IUnknownProperties } from './ICommon';

/**
 * FormTemplate的FormItem字段（type为Row）
 */
interface IFormItem extends IUnknownProperties {
  /**
   * 表单FormItem类型
   */
  type: string;
  /**
   * 表单FormItem绑定字段
   */
  name: string;
  /**
   * 表单FormItem标签
   */
  label: string;
  /**
   * 表单FormItem校验规则
   */
  rules?: Array<Rule>;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 限制最大输入字符
   */
  maxLength?: number;
}

/**
 * 可编辑表格列约束接口
 */
export interface IEditableColumns extends IUnknownProperties {
  /**
   * 列名称
   */
  title: string;
  /**
   * 列key
   */
  dataIndex: string;
  /**
   * 列宽度
   */
  width?: number;
  /**
   * 是否只读
   */
  readonly?: boolean;
  /**
   * 需要格式化的形式
   */
  valueType?: string;
  /**
   * 传递给form.item的props
   */
  formItemProps?: object;
  /**
   * 传递给组件的props
   */
  fieldProps?: object;
}

/**
 * 可编辑表格类型约束接口
 */
export interface IEditableConfig extends IUnknownProperties {
  /**
   * 唯一索引
   */
  rowKey: string;
  /**
   * 列表配置
   */
  columns: IEditableColumns[];
  /**
   * 数据源
   */
  dataSource: any[];
  /**
   * 是否可编辑
   */
  canEdit: boolean;
  /**
   * 可编辑表格实例
   */
  editableFormRef: any;
  /**
   * 操作列按钮数量
   */
  optionCount: number;
  /**
   * 是否展示新增按钮
   */
  showCreate?: boolean;
}

/**
 * type为List的传参约束接口
 */
export interface IFormList {
  /**
   * List对应的名称
   */
  listName: string;
  /**
   * List字段配置
   */
  data: Array<any>;
  /**
   * 是否展示按钮
   */
  showButton?: boolean;
  /**
   * 渲染按钮
   */
  renderButton?: (field, add, remove) => React.ReactNode;
}

/**
 * FormTemplate公共组件的config枚举类型
 */
export interface IFormConfig extends IUnknownProperties {
  /**
   * 表单类型，FORMITEM_TYPE类型
   */
  type: string;
  /**
   * 表单标题
   */
  title?: string;
  /**
   * 单行
   */
  placeholder?: string;
  /**
   * 表单数据源，下拉框等组件可以赋值
   */
  data?: Array<IFormItem> | IFormList;
  /**
   * 如果有联动交互，比如B依赖A的值联动，A是主表B是从表，A需要有ref字段识别关联，则B需要有bind=A的绑定字段，同时B的集合需要对bind与A的值做过滤
   */
  ref?: string;
}
