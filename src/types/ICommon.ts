/**
 * Created by ca<PERSON><PERSON> on 2024/11/1.
 */

import { ReactNode } from 'react';
import { IFormConfig } from './IForm';

/**
 * 不确定属性，用来被继承和扩展
 */
export interface IUnknownProperties {
  [key: string]: unknown;
}

/**
 * 字典接口
 */
export interface IDictList {
  key?: string;
  label?: string;
}

/**
 * InputNumber属性约束接口
 */
export interface TInputNumberProperties extends IUnknownProperties {
  placeholder?: any;
  min: number;
  max: number;
}

/**
 * 分页属性约束接口
 */
export interface IPaniation extends IUnknownProperties {
  currentPage: number;
  pageSize: number;
}

/**
 * UrlObj请求约束接口
 */
export interface IUrlObj {
  list?: string;
  create?: string;
  edit?: string;
  detail?: string;
  delete?: string;
  getRequestData?: (data: object, type: string, rowData?: object) => void;
  customFunc?: object | undefined;
  /**
   * REQUEST_TYPE_OBJ里的公用方法不通用，传自定义的操作方法
   */
  requestFuncMap?: {
    list?: (params: object) => any;
    create?: (params: object) => any;
    edit?: (params: object) => any;
    detail?: (params: object) => any;
  };
}

/**
 * 搜索框searchSource约束接口
 */
export interface ISearchSource extends IUnknownProperties {
  /**
   * 子项绑定的字段
   */
  value: string;
  /**
   * 子项标签
   */
  label: string;
  /**
   * 子项类型
   */
  type: string;
  /**
   * select下拉框等数据
   */
  data?: Array<any>;
  /**
   * 子项宽度
   */
  width?: number;
  /**
   * 如果有联动交互，比如B依赖A的值联动，A是主表B是从表，A需要有ref字段识别关联，则B需要有bind=A的绑定字段，同时B的集合需要对bind与A的值做过滤
   */
  ref?: string;
  /**
   * 校验规则
   */
  rules?: Array<object>;
}

/**
 * 表格columns约束接口
 */
export interface IColumns {
  key: string;
  /**
   * 数据路径
   */
  dataIndex: string | string[];
  /**
   * 列表名称
   */
  title: string;
  /**
   * 对齐方式
   */
  align?: string | 'center' | 'left' | 'right';
  /**
   * 宽度
   */
  width?: number;
  /**
   * 需要格式化的数据类型，RENDER_TYPE
   */
  valueType?: string;
  /**
   * select下拉框等数据
   */
  data?: Array<any>;
  /**
   * 参数或字典从store的dict里拿的dictMap的key值
   */
  dictType?: string;
  /**
   * 当前列表自定义国际化前缀
   */
  prefix?: string;
  /**
   * select下拉框是否展示key
   */
  showKey?: boolean;
  /**
   * 数据渲染函数
   */
  render?: (_, data) => any;
}

/**
 * PageConfig页面配置约束接口，特殊页面可以extends该接口，再实现接口成员
 */
export interface IPageConfig {
  /**
   * 国际化前缀
   */
  prefix: string;
  /**
   * 标题
   */
  cardTitle: string;
  /**
   * 接口对象
   */
  urlObj: IUrlObj;
  /**
   * 查询条件配置
   */
  searchSource: Array<ISearchSource>;
  /**
   * 表格字段配置
   */
  columns: Array<IColumns>;
  /**
   * 表格操作列配置
   */
  optionList: Array<ICommonTableActionItem>;
  /**
   * 详情表单配置
   */
  formConfig?: Array<IFormConfig>;
  /**
   * 表格上方操作栏按钮权限配置
   */
  formActionPermissionObj?: IFormActionPermissionObj;
}

/**
 * DoublePageConfig页面配置约束接口，特殊页面可以extends该接口，再实现接口成员
 */
export interface IDoublePageConfig {
  prefix: string;
  cardTitle: string;
  urlObj: IUrlObj;
  searchSource: Array<ISearchSource>;
  outerFormConfig?: Array<IFormConfig>;
  formConfig?: Array<IFormConfig>;
  permissionObj?: IFormActionPermissionObj;
}

/**
 * 枚举接口
 */
export interface IEnumsData {
  key?: number | string;
  label?: string;
  value?: number | string;
}

/**
 * 列表公共字段
 */
export interface IPublicColumn {
  /**
   * 版本号
   */
  version: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 创建人
   */
  createUser: string;
  /**
   * 更新人
   */
  updateUser: string;
}

/**
 * 列表操作列约束接口
 */
export interface ICommonTableActionItem {
  /**
   * 类型
   */
  type: string;
  /**
   * 按钮提示文本
   */
  title?: string | Element;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 另外配置的图标
   */
  icon?: ReactNode | null;
  /**
   * 权限id
   */
  permissionId?: string;
  /**
   * 国际化前缀
   */
  prefix?: string;
}

/**
 * FormAction组件按钮权限对象约束接口
 */
export interface IFormActionPermissionObj {
  edit?: string;
  submit?: string;
  create?: string;
  delete?: string;
}

/**
 * 表格上方操作栏
 */
export type IFormActionConfig = {
  /**
   * 是否展示重置按钮
   */
  showReset?: boolean;
  /**
   * 是否展示提交按钮
   */
  showSubmit?: boolean;
  /**
   * 是否展示新增按钮
   */
  showCreate?: boolean;
  /**
   * 是否展示修改按钮
   */
  showEdit?: boolean;
  /**
   * 详情是否展示详情
   */
  detailShowCreate?: boolean;
  /**
   * 详情自定义新增方法
   */
  onDetailCreate?: () => void;
};
