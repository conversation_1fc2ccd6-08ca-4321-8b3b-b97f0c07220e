/**
 * Created by ca<PERSON><PERSON> on 2024/7/1.
 */

import { ReactNode } from 'react';

/**
 * 菜单子项
 */
export type TMenuItem = {
  code: string;
  parentCode?: string;
  name?: string;
  type?: string;
  url?: string;
  level: number;
  sort: number;
  children?: TMenuItem[];
};
export type TSysMenuItem = {
  key?: React.Key;
  menuId: string;
  menuName: string;
  menuType: string;
  orderSeq: string;
  menuStatus: string;
  parentMenuId?: string;
  parentMenu?: string;
  menuRouteUrl?: string;
  createUser?: string;
  updateUser?: string;
  createTs?: string;
  updateTs?: string;
  iconId?: string;
  level?: number;
  type?: string;
  permissionId?: string;
  children?: TSysMenuItem[];
};

/**
 * 下拉框、复选框子项
 */
export type TOptionItem = {
  key: string;
  value: string;
  label?: string | ReactNode;
  children?: [];
};

/**
 * 通知类型
 */
export type TNotification = 'success' | 'info' | 'warning' | 'error';

/**
 * 下拉菜单子项
 */
export type TDropDownMenuItem = {
  key: string;
  label: string | ReactNode;
  icon?: ReactNode | any;
  disabled?: boolean;
};
