// 规则因子select框hooks
import { ReactNode, useEffect, useState } from 'react';
import { Select } from 'antd';
import _ from 'lodash';
import { COMPONENT_TYPE } from '@/constants/publicConstant';
import { RULE_COND_MENU_TYPE, RULE_FAC_LIST } from '@/constants/rulesConstant';
// import useIntlCustom from '@/hooks/useIntlCustom';
import { TDropDownMenuItem, TOptionItem } from '@/types/TCommon';
import { getOpListByValueType } from '../../util/condNodeUtil';
import useOpTypeSelect from './useOpTypeSelect';
import { IRuleFac } from '../../types/IRules';
import styles from '../../index.module.css';

const useRuleFacSelect = ({
  nodeData,
  config,
  setIsOpExps,
  setComValue,
  setTempResult,
  setValueComType,
  getValueMenuList,
}) => {
  let { ruleFacTypeList = [], opMenuItems = [], booleanMenuItems = [], selectPlaceholder = '' } = config;
  // 规则因子值
  const [ruleFac, setRuleFac] = useState<string>('');
  // 规则因子类型
  const [valueType, setValueType] = useState<string>('');
  // 运算符菜单数据
  const [opMenuList, setOpMenuList] = useState<TOptionItem[]>(opMenuItems);
  // 值为下拉框时的下拉数据
  const [valueMenuList, setValueMenuList] = useState<TOptionItem[]>([]);
  // 规则因子菜单
  const ruleFacMenuOption: TOptionItem[] = ruleFacTypeList.map((item: IRuleFac) => ({
    ...item,
    value: item.ruleFacEnName,
    label: item.ruleFacCnName,
  }));
  const { setOpType } = useOpTypeSelect({
    nodeData,
    config,
    opMenuList,
    setComValue,
    setTempResult,
  });

  useEffect(() => {
    const { fieldBid, valueType } = nodeData;
    setDataByRuleFac(fieldBid);
    valueType && setValueType(RULE_FAC_LIST[valueType]);
  }, []);

  const setDataByRuleFac = async (ruleFac: string): Promise<void> => {
    setRuleFac(ruleFac);
    // 找出当前规则因子的其他配置
    const temp = _.find(ruleFacTypeList, { ruleFacEnName: ruleFac });
    if (temp) {
      const { ruleFacCnName: fieldName = '', ruleFacValueType = RULE_FAC_LIST.STRING } = temp || {};
      setValueType(ruleFacValueType);
      const res = { ...temp, fieldName, fieldBid: ruleFac };
      setTempResult((preValue) => ({ ...preValue, ...res }));
      // 不需要的属性需要清空
      let valueComType = '';
      let valueMenuList: TOptionItem[] = [];
      switch (ruleFacValueType) {
        // 字符型/整型/长整型/浮点型/金额型/数组型
        case RULE_FAC_LIST.STRING:
        case RULE_FAC_LIST.INTEGER:
        case RULE_FAC_LIST.LONGINTEGER:
        case RULE_FAC_LIST.FLOAT:
        case RULE_FAC_LIST.AMOUNT:
        case RULE_FAC_LIST.ARRAY: {
          valueComType = COMPONENT_TYPE.INPUT;
          // 根据规则因子类型筛选不同的符号枚举
          const opTypeList = getOpListByValueType(ruleFacValueType);
          setOpMenuList(opMenuItems.filter((item: TOptionItem) => opTypeList.includes(item.key)));
          break;
        }
        // 布尔型切换下拉菜单
        case RULE_FAC_LIST.BOOLEAN:
          valueComType = COMPONENT_TYPE.SELECT;
          valueMenuList = booleanMenuItems;
          break;
        // 参数型/字典型
        default:
          valueComType = COMPONENT_TYPE.SELECT;
          valueMenuList = await getValueMenuList(RULE_COND_MENU_TYPE.paramter);
          break;
      }
      setValueComType(valueComType);
      setValueMenuList(valueMenuList);
    }
  };
  // 获取国际化后的值菜单（比如布尔类型）
  const setValueMenu = async (type, data = {}) => {
    const valueMenuList = await getValueMenuList(type, data);
    setValueMenuList(valueMenuList);
  };

  // 规则因子change事件
  const handleRuleFacChange = (key: string): void => {
    // 选择的规则因子的valueType
    const ruleFacValueType = _.get(
      _.find(ruleFacTypeList, { ruleFacEnName: key }),
      'ruleFacValueType',
      RULE_FAC_LIST.STRING,
    );
    let valueType = '';
    for (const key in RULE_FAC_LIST) {
      if (ruleFacValueType === RULE_FAC_LIST[key]) {
        valueType = key;
        break;
      }
    }
    // 根据规则因子类型筛选不同的符号枚举
    const opTypeList = getOpListByValueType(ruleFacValueType);
    setOpMenuList(opMenuItems.filter((item: TDropDownMenuItem) => opTypeList.includes(item.key)));
    setDataByRuleFac(key);
    // 重置其他数据
    setOpType('');
    setIsOpExps(false);
    setComValue('');
    const res = {
      opName: '',
      operateType: '',
      value: '',
      wordSegBid: '',
      valueType,
    };
    setTempResult((preValue) => ({ ...preValue, ...res }));
  };
  // 渲染规则因子select组件
  const renderRuleFacSelectComp = (): ReactNode => {
    return (
      <Select
        value={ruleFac}
        showSearch
        optionFilterProp="label"
        options={ruleFacMenuOption}
        variant="borderless"
        placeholder={selectPlaceholder}
        className={styles.ruleFacSelect}
        onChange={handleRuleFacChange}
      />
    );
  };

  return {
    valueType,
    opMenuList,
    valueMenuList,
    renderRuleFacSelectComp,
    setValueMenu,
  };
};

export default useRuleFacSelect;
