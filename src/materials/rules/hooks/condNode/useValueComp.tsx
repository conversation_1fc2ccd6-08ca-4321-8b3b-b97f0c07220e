// 规则因子值输入框/下拉框/切换按钮hooks
import { ReactNode } from 'react';
import { Button, Input, Select } from 'antd';
import { SwapOutlined } from '@ant-design/icons';
import { COMPONENT_TYPE } from '@/constants/publicConstant';
import { RULE_COND_MENU_TYPE } from '@/constants/rulesConstant';
import styles from '../../index.module.css';

const useValueComp = ({
  config,
  comValue,
  setComValue,
  valueMenuList,
  setTempResult,
  setIsOpExps,
  setValueMenu,
  setValueComType,
  inputPlaceHolder,
}) => {
  let { selectPlaceholder = '' } = config;
  // select框change事件
  const handleValueChange = (key, option): void => {
    const value = option.label;
    const res = { value };
    setComValue(key);
    setTempResult((preValue) => ({ ...preValue, ...res }));
  };

  // input框change事件
  const handleChange = (e): void => {
    let { value } = e.target;
    setComValue(value);
    setTempResult((preValue) => ({ ...preValue, value }));
  };

  // 改变值类型（只有数值型的规则因子）
  const handleSwitchType = (): void => {
    setComValue('');
    setIsOpExps((preValue) => {
      const res = !preValue;
      if (res) {
        setValueMenu(RULE_COND_MENU_TYPE.opExps);
        setValueComType(COMPONENT_TYPE.SELECT);
        setTempResult((preValue) => ({ ...preValue, value: '' }));
      } else {
        setValueComType(COMPONENT_TYPE.INPUT);
        setTempResult((preValue) => ({ ...preValue, wordSegBid: '' }));
      }
      return res;
    });
  };

  // 值为input时渲染组件
  const renderValueInputComp = (): ReactNode => {
    return (
      <Input value={comValue} placeholder={inputPlaceHolder} className={styles.ruleFacInput} onChange={handleChange} />
    );
  };

  // 值为select时渲染组件
  const renderValueSelectComp = (): ReactNode => {
    return (
      <Select
        value={comValue}
        options={valueMenuList}
        variant="borderless"
        placeholder={selectPlaceholder}
        className={styles.ruleFacSelect}
        onChange={handleValueChange}
      />
    );
  };
  // 改变值类型按钮组件（只有数值型的规则因子展示）
  const renderSwitchButton = (): ReactNode => {
    return (
      <Button
        type="link"
        icon={<SwapOutlined />}
        className={`m-lr-s ${styles.ruleFacButton}`}
        onClick={handleSwitchType}
      />
    );
  };

  return { renderValueInputComp, renderValueSelectComp, renderSwitchButton };
};

export default useValueComp;
