// 运算符select框hooks
import { ReactNode, useEffect, useState } from 'react';
import { Select } from 'antd';
import { RULE_FAC_OP_TYPE } from '@/constants/rulesConstant';
import { TOptionItem } from '@/types/TCommon';
import { getValueRuleByOpType } from '../../util/condNodeUtil';
import styles from '../../index.module.css';

const useOpTypeSelect = ({ nodeData, config, opMenuList, setComValue, setTempResult }) => {
  // hooks变量
  // 运算符
  const [opType, setOpType] = useState<string>('');
  const [inputPlaceHolder, setInputPlaceHolder] = useState<string>('');
  let { inputPlaceholder = '', inputMultiplePlaceHolder = '', selectPlaceholder = '' } = config;

  // 副作用
  useEffect(() => {
    const { operateType } = nodeData;
    const valueRule = getValueRuleByOpType(operateType);
    setInputPlaceHolder(valueRule === 1 ? inputPlaceholder : inputMultiplePlaceHolder);
    setOpType(RULE_FAC_OP_TYPE[operateType]);
  }, []);

  // 运算符chang事件
  const handleOpChange = (key: string, option: TOptionItem): void => {
    const opName = option.label;
    const res = { opName, operateType: key };
    const valueRule = getValueRuleByOpType(key);
    setInputPlaceHolder(valueRule === 1 ? inputPlaceholder : inputMultiplePlaceHolder);
    setOpType(key);
    setComValue('');
    setTempResult((preValue) => ({
      ...preValue,
      ...res,
      value: '',
      wordSegBid: '',
    }));
  };

  // 渲染运算符select组件
  const renderOpTypeSelectComp = (): ReactNode => {
    return (
      <Select
        value={opType}
        options={opMenuList}
        variant="borderless"
        placeholder={selectPlaceholder}
        className={styles.ruleFacSelect}
        onChange={handleOpChange}
      />
    );
  };

  return { inputPlaceHolder, opType, setOpType, renderOpTypeSelectComp };
};

export default useOpTypeSelect;
