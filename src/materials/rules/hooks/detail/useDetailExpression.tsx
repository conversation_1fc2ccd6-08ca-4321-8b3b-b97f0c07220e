// 详情/运算表达式和条件表达式hooks
import { FormHearder } from '@/components';
import { I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import { Button, Collapse } from 'antd';
import { ReactNode, useEffect, useRef, useState } from 'react';
import RuleCondGraph from '../../components/RuleCondGraph';
import { prefix } from '../../pageConfig';

// 折叠面板样式
const collaspStyle = {
  body: {
    height: '20rem', // 增加高度以更好显示G6图表
    padding: '1rem', // 添加内边距
    backgroundColor: '#fafafa', // 添加背景色
    borderRadius: '8px', // 添加圆角
  },
};

const useDetailExpression = ({ canEdit, ruleType, ruleCondField, judgeRuleFacTypeList }) => {
  // hooks变量
  const { translate } = useIntlCustom();

  // 为每个RuleCondGraph创建独立的ref
  const graphRefs = useRef<{ [key: string]: any }>({});

  // 确保ruleCondField是一个数组
  const normalizedRuleCondField = Array.isArray(ruleCondField) ? ruleCondField : ruleCondField ? [ruleCondField] : [];

  // 添加状态管理Collapse组件列表，与ruleCondField数组同步
  const [collapseItems, setCollapseItems] = useState(() => {
    // 初始化时，使用传入的ruleCondField数组长度创建对应数量的Collapse项
    if (normalizedRuleCondField.length > 0) {
      return normalizedRuleCondField.map((_, index) => ({
        key: (index + 1).toString(),
        label: `judgeExpression ${index + 1}`,
      }));
    }
    // 如果没有数据，默认创建一个
    return [
      {
        key: '1',
        label: 'judgeExpression 1',
      },
    ];
  });

  // 当ruleCondField变化时，更新collapseItems以保持同步
  useEffect(() => {
    if (normalizedRuleCondField.length > 0) {
      const newCollapseItems = normalizedRuleCondField.map((_, index) => ({
        key: (index + 1).toString(),
        label: `judgeExpression ${index + 1}`,
      }));
      setCollapseItems(newCollapseItems);
    }
  }, [normalizedRuleCondField.length]);

  // 添加新的Collapse组件
  const handleAddCollapse = () => {
    const newKey = (collapseItems.length + 1).toString();
    setCollapseItems([
      ...collapseItems,
      {
        key: newKey,
        label: `judgeExpression ${newKey}`,
      },
    ]);
  };

  // 添加获取所有RuleCondGraph数据的方法
  const onSubmit = async () => {
    const results: any[] = [];
    // 遍历所有的ref，调用每个RuleCondGraph的onSubmit方法
    for (const key in graphRefs.current) {
      if (graphRefs.current[key] && graphRefs.current[key].onSubmit) {
        const result = await graphRefs.current[key].onSubmit();
        if (result) {
          results.push(result);
        }
      }
    }
    // 如果没有结果但有面板，至少返回一个空数组
    return results.length > 0 ? results : [];
  };

  // 渲染条件表达式组件
  const renderJudgeExps = (): ReactNode => {
    return (
      <div className="p-r">
        <FormHearder
          title={translate(prefix, 'JUDGE')}
          extraBtn={() => (
            <Button type="primary" onClick={handleAddCollapse}>
              {translate(I18N_COMON_PAGENAME.COMMON, 'create')}
            </Button>
          )}
        />
        <Collapse
          defaultActiveKey={['1']}
          items={collapseItems.map((item, index) => ({
            key: item.key,
            label: item.label,
            children: (
              <RuleCondGraph
                ref={(el) => (graphRefs.current[item.key] = el)}
                ruleType={ruleType}
                ruleCondField={normalizedRuleCondField[index] || null}
                ruleFacTypeList={judgeRuleFacTypeList}
                canEdit={canEdit}
              />
            ),
            styles: collaspStyle,
          }))}
          size="small"
        />
      </div>
    );
  };

  // 返回自定义的ref对象，包含onSubmit方法
  const customRef = useRef({
    onSubmit,
  });

  return { ruleCondGraphRef: customRef, renderJudgeExps };
};

export default useDetailExpression;
