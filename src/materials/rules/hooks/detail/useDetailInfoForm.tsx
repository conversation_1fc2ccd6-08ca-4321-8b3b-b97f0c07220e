// 详情/基本信息hooks
import { FormTemplate } from '@/components';
import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import { ReactNode, useRef } from 'react';
import { prefix } from '../../pageConfig';

const useDetailInfoForm = ({ type, canEdit, data, handleFormChange }) => {
  // hooks变量
  const ruleEditFormRef = useRef<any>();
  // 基本信息数据配置
  const config = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'ruleId',
          label: 'ruleId',
          rules: [{ required: true, max: 20 }],
          disabled: ![OPERATE_TYPE.copy, OPERATE_TYPE.create].includes(type),
          maxLength: 20,
        },
        {
          type: COMPONENT_TYPE.INPUT_NUMBER,
          name: 'rulePri',
          label: 'rulePri',
          rules: [{ required: true }],
          min: 0,
          max: 9999999999,
          decimalSeparator: '0',
          parser: (value) => Number(value),
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'ruleName',
          label: 'ruleName',
          rules: [{ required: true }],
          disabled: type === OPERATE_TYPE.edit,
          maxLength: 50,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'ruleType',
          label: 'ruleType',
          data: DICT_CONSTANTS.RULE_TYPE,
          rules: [{ required: true }],
          showKey: false,
        },
        // {
        //   type: COMPONENT_TYPE.SELECT,
        //   name: 'ruleOptionList',
        //   label: 'ruleExecuteType',
        //   data: DICT_CONSTANTS.RULE_OPTION_TYPE,
        //   showKey: false,
        //   mode: 'multiple',
        //   rules: [{ required: true }],
        // },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'ruleDesc',
          label: 'ruleDesc',
          rules: [{ required: true }],
          maxLength: 50,
        },
      ],
    },
  ];

  // 渲染表单组件
  const renderInfoForm = (): ReactNode => {
    return (
      <FormTemplate
        ref={ruleEditFormRef}
        config={config}
        intlPrefix={prefix}
        canEdit={canEdit}
        showMaintenance={false}
        initialData={data}
        loading={false}
        onChange={handleFormChange}
      />
    );
  };

  return { ruleEditFormRef, renderInfoForm };
};

export default useDetailInfoForm;
