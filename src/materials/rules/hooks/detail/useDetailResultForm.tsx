// 结果信息hooks

import { ReactNode, useRef, useState } from 'react';
import { Input, InputNumber, Select } from 'antd';
import useIntlCustom from '@/hooks/useIntlCustom';
import { FormTemplate } from '@/components';
import { EDITTABLE_VALUE_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import { RULE_BOOLEAN_TYPE, RULE_FAC_LIST } from '@/constants/rulesConstant';
import { IEditableColumns } from '@/types/IForm';
import { prefix } from '../../pageConfig';
import { getSelection } from '../../util/rulesUtil';
import { IRuleFac } from '../../types/IRules';

const useDetailResultForm = ({ canEdit, data, ruleResultField, resultRuleFacTypeList }) => {
  // hooks变量
  const { translate, getSelectOption } = useIntlCustom();
  const ruleResultFormRef = useRef<any>();
  const editableFormRef = useRef<any>();
  const [editForm, setEditForm] = useState<any>();
  const editTableRefList = { editTableData: editableFormRef };
  // 列表字段配置
  const columns: IEditableColumns[] = [
    {
      title: 'ruleFacField',
      dataIndex: 'ruleFac',
      valueType: EDITTABLE_VALUE_TYPE.SELECT,
      valueEnum: resultRuleFacTypeList,
      readonly: !canEdit,
      formItemProps: { rules: [{ required: true }] },
      fieldProps: (form, { rowIndex }) => {
        return {
          showSearch: true,
          onSelect: () => {
            // 每次选中重置
            editableFormRef.current?.setRowData?.(rowIndex, { ruleResult: [] });
          },
        };
      },
    },
    {
      title: 'ruleResult',
      dataIndex: 'ruleResult',
      valueType: EDITTABLE_VALUE_TYPE.TEXT,
      readonly: !canEdit,
      formItemProps: (form, { entity }) => {
        const { ruleFac = '' } = form?.getFieldValue()?.[entity?.id] || {};
        const ruleFacConf = resultRuleFacTypeList[ruleFac] || {};
        let { ruleFacMax, ruleFacMin, ruleFacValueType } = ruleFacConf;
        // 设置字符型的限制长度
        let otherRules =
          ruleFac && ruleFacValueType === RULE_FAC_LIST.STRING ? { min: ruleFacMin, max: ruleFacMax } : {};
        return { rules: [{ required: true, ...otherRules }] };
      },
      // renderFormItem: ({ entity, isEditable }) => {
      //   const { id, ruleResult } = entity;
      //   const { ruleFac = "" } = editForm?.getFieldValue()?.[id] || {};
      //   const ruleFacConf = resultRuleFacTypeList[ruleFac] || {};
      //   const comp = getResultValueCom(ruleFacConf);
      //   return isEditable ? comp : <span>{ruleResult}</span>;
      // },
      // render: (_, record) => {
      //   const { ruleFac, ruleResult } = record;
      //   const { ruleFacValueType } = resultRuleFacTypeList[ruleFac] || {};
      //   // 布尔值国际化
      //   return ruleFacValueType === RULE_FAC_LIST.BOOLEAN
      //     ? translate(prefix, ruleResult)
      //     : ruleResult;
      // },
    },
  ];
  // 获取可编辑表格form实例
  const afterMount = (form): void => {
    setEditForm(form);
  };
  // 根据规则因子类型渲染不同的组件
  const getResultValueCom = (ruleFacConf: IRuleFac): ReactNode => {
    let { ruleFacValueType, ruleFacMax, ruleFacMin, ruleFacFloat = 0 } = ruleFacConf;
    const optionMap = {
      [RULE_FAC_LIST.BOOLEAN]: getSelection(RULE_BOOLEAN_TYPE),
    };
    switch (ruleFacValueType) {
      // 数组型
      case RULE_FAC_LIST.ARRAY:
        return <Input />;
      // 字符型
      case RULE_FAC_LIST.STRING:
        return <Input maxLength={ruleFacMax} />;
      // 整型
      case RULE_FAC_LIST.INTEGER: {
        // 数值类型的由于返回的是可输入长度，因此要转换成最大最小值
        let minNum = Number(Array(ruleFacMin).fill('1').join(''));
        let maxNum = Number(Array(ruleFacMax).fill('9').join(''));
        return <InputNumber key={ruleFacValueType} min={minNum} max={maxNum} precision={0} />;
      }
      // 浮点型、金额型
      case RULE_FAC_LIST.FLOAT:
      case RULE_FAC_LIST.AMOUNT: {
        // 数值类型的由于返回的是可输入长度，因此要转换成最大最小值，最大值拼上浮点数
        let maxNum = Number(Array(ruleFacMax).fill('9').join(''));
        let maxFloat = Array(ruleFacFloat).fill('9').join('');
        let minNum = Number(Array(ruleFacMin).fill('1').join(''));
        maxNum = Number(`${maxNum}.${maxFloat}`);
        return <InputNumber key={ruleFacValueType} min={minNum} max={maxNum} precision={Number(ruleFacFloat)} />;
      }
      default:
        return <Select options={getSelectOption(optionMap[ruleFacValueType], false, prefix)} />;
    }
  };

  // 结果信息数据配置
  const ruleResultConfig = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'RESULT',
    },
    {
      type: FORMITEM_TYPE.EditTable,
      rowKey: 'id',
      columns,
      dataSource: ruleResultField,
      canEdit,
      editableFormRef,
      optionCount: 2,
      getOptionList: () => [
        { optionType: OPERATE_TYPE.edit, disabled: !canEdit },
        { optionType: OPERATE_TYPE.delete, disabled: !canEdit },
      ],
      onCreate: (index) => ({ id: Date.now() }),
      afterMount,
    },
  ];
  // 渲染结果信息组件
  const renderResultForm = (): ReactNode => {
    return (
      <FormTemplate
        ref={ruleResultFormRef}
        config={ruleResultConfig}
        intlPrefix={prefix}
        canEdit={canEdit}
        showMaintenance={false}
        initialData={data}
        loading={false}
        editTableRefList={editTableRefList}
      />
    );
  };

  return { ruleResultFormRef, renderResultForm };
};

export default useDetailResultForm;
