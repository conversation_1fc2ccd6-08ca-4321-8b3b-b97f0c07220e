// 物料层/规则详情页面
import { FormMaintenance } from '@/components';
import { I18N_COMON_PAGENAME, NOTIFICATION_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import { RULE_OPTION_TYPE, RULE_TYPE } from '@/constants/rulesConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import common from '@/services/common';
import { getRuleFacByRuleType } from '@/services/rules';
import { FC, forwardRef, memo, useEffect, useImperativeHandle, useState } from 'react';
import useDetailOpEpxs from './hooks/detail/useDetailExpression';
import useDetailInfoForm from './hooks/detail/useDetailInfoForm';
import useDetailResultForm from './hooks/detail/useDetailResultForm';
import useRuleUtil from './hooks/useRuleUtil';
import { IDetailData, IDetailProps, IRuleFac, IRuleResultFieldItem } from './types/IRules';

const Detail: FC<IDetailProps> = forwardRef(
  ({ type = OPERATE_TYPE.detail, data = {}, ruleType = RULE_TYPE.limit, urlObj }, ref = null) => {
    // hook变量
    const { openNotificationTip } = useIntlCustom();
    const { setListByRuleFac } = useRuleUtil();
    const [judgeRuleFacTypeList, setJudgeRuleFacTypeList] = useState<Array<IRuleFac>>([]);
    const [resultRuleFacTypeList, setResultRuleFacTypeList] = useState<object>({});
    const [ruleCondField, setRuleCondField] = useState<object>({});
    const [ruleResultField, setRuleResultField] = useState<Array<any>>([]);
    const canEdit = type !== OPERATE_TYPE.detail;
    // 副作用
    useEffect(() => {
      // 新增进入不查询
      if (type === OPERATE_TYPE.create) {
        return;
      }
      let { ruleType, ruleCondField, ruleResultField, ruleOptionList } = data as IDetailData;
      ruleType && getRuleFacType('node');
      setRuleCondField(ruleCondField);
      setRuleResultField(ruleResultField as IRuleResultFieldItem[]);
    }, [data]);

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => {
      return {
        async onSubmit() {
          try {
            // 获取基本信息、运算表达式、条件表达式、结果信息数据
            const { formData } = await ruleEditFormRef?.current?.onSubmit();
            // 在useImperativeHandle中的onSubmit方法内
            let ruleCondField: any = await ruleCondGraphRef?.current?.onSubmit();
            // 确保ruleCondField是一个数组
            if (!Array.isArray(ruleCondField)) {
              ruleCondField = ruleCondField ? [ruleCondField] : [];
            }
            console.log('2222---submitruleCondField', ruleCondField);
            // 如果需要将数组转换为JSON字符串传给后端
            ruleCondField = JSON.stringify(ruleCondField);

            let { editTableData = [] } = (await ruleResultFormRef?.current?.onSubmit()) || {};
            if (formData) {
              const { ruleOptionList = [] } = formData;
              if (
                (ruleOptionList.includes(RULE_OPTION_TYPE.judgeExps) && !ruleCondField) ||
                (ruleOptionList.includes(RULE_OPTION_TYPE.ruleResultField) && !editTableData.length)
              ) {
                throw Error('checkMsg');
              }
              // 赋默认值，不能传空
              ruleCondField = ruleCondField || ' ';
              // 接口
              delete data.ruleOptionList;
              delete formData.ruleOptionList;
              let ruleResultField = editTableData.map(({ ruleFac, ruleResult }) => ({ [ruleFac]: ruleResult }));
              ruleResultField = JSON.stringify(ruleResultField);
              const postData = {
                ...data,
                ...formData,
                ruleCondField,
                ruleResultField,
                // execType: "0",
                // ruleSts: "1",
              };
              // 修改/新增接口
              const res = await common.getEditPostBiz({
                ...postData,
                url: type === OPERATE_TYPE.edit ? urlObj.EDIT : urlObj.CREATE,
                type,
              });
              if (res) {
                openNotificationTip(I18N_COMON_PAGENAME.COMMON, NOTIFICATION_TYPE.SUCCESS, 'editSuccess', 1);
                return true;
              }
            }
            return null;
          } catch (error) {
            console.error(error);
            openNotificationTip(I18N_COMON_PAGENAME.COMMON, NOTIFICATION_TYPE.ERROR, 'checkMsg');
            return null;
          }
        },
      };
    });
    // 获取规则因子数据
    const getRuleFacType = async (ruleType: string): Promise<void> => {
      try {
        const res = await getRuleFacByRuleType({ ruleType });
        const { judgeRuleFactTypeList, resultRuleFactTypeList } = setListByRuleFac(res);
        setJudgeRuleFacTypeList(judgeRuleFactTypeList);
        setResultRuleFacTypeList(resultRuleFactTypeList);
      } catch (error) {
        console.error(error);
      }
    };

    // 事件处理
    // 表格值改变事件
    const handleFormChange = (resObj): void => {
      for (const key in resObj) {
        if (key === 'ruleType') {
          // 根据规则类型查询规则因子
          getRuleFacType(resObj[key]);
        }
      }
    };

    // 基本信息组件hooks
    const { ruleEditFormRef, renderInfoForm } = useDetailInfoForm({
      type,
      canEdit,
      data,
      handleFormChange,
    });

    // 运算表达式和条件表达式hooks
    const { ruleCondGraphRef, renderJudgeExps } = useDetailOpEpxs({
      canEdit,
      ruleType,
      ruleCondField,
      judgeRuleFacTypeList,
    });

    // 结果信息组件hooks
    const { ruleResultFormRef, renderResultForm } = useDetailResultForm({
      canEdit,
      data,
      ruleResultField,
      resultRuleFacTypeList,
    });

    return (
      <>
        {renderInfoForm()}
        {renderJudgeExps()}
        {renderResultForm()}
        {!canEdit && <FormMaintenance data={data as IDetailData} showVersion={false} />}
      </>
    );
  },
);

export default memo(Detail);
