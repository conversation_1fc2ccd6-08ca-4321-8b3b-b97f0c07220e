/**
 * 条件表达式的节点数据
 */
export interface ICondNodeData {
  /**
   * 节点id
   */
  nodeId: string;
  /**
   * 节点类型
   */
  nodeType: number;
  /**
   * 节点层级
   */
  nodeDim: number;
  /**
   * 节点编号
   */
  nodeNo: string;
  /**
   * 条件组值
   */
  stsfCondJudge?: number;
  /**
   * 运算符值
   */
  operateType: string;
  /**
   * 运算符中文
   */
  opName: string;
  /**
   * 规则因子类型
   */
  valueType: string;
  /**
   * 规则因子值
   */
  value: string[] | string;
  /**
   * 规则因子key
   */
  fieldBid: string;
  /**
   * 规则因子name
   */
  fieldName: string;
  /**
   * 表达式展示的结果
   */
  nodeCtx: string;
  /**
   * 数值型规则因子选择运算表达式
   */
  valueWorldSegType?: 'OOEXPS' | null;
  /**
   * 数值型规则因子选择运算表达式的值
   */
  wordSegBid?: 'opExps' | null;
}
