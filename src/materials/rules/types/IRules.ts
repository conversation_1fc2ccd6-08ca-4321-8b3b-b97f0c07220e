import { IPublicColumn } from '@/types/ICommon';
import { TOptionItem } from '@/types/TCommon';

/**
 * 请求约束接口
 */
export interface IUrlObj {
  LIST: string;
  EDIT: string;
  CREATE: string;
}

/**
 * 运算表达式约束接口
 */
export interface IOpExpsField {
  opExps: string;
}

/**
 * 结果信息约束接口
 */
export interface IRuleResultFieldItem {
  id: string;
  ruleFac: string;
  ruleResult: string;
}

/**
 * 规则详情数据约束接口
 */
export interface IDetailData extends IPublicColumn {
  incId: number;
  /**
   * 优先级
   */
  rulePri: number;
  /**
   * 机构号
   */
  crcdOrgNo: string;
  /**
   * 规则ID
   */
  ruleId: string;
  /**
   * 规则描述
   */
  ruleDesc: string;
  /**
   * 规则类型
   */
  ruleType: string;
  /**
   * 参数状态
   */
  paramSts: string;
  /**
   * 规则名称
   */
  ruleName: string;
  /**
   * 运算表达式
   */
  opExpsField: IOpExpsField | string;
  /**
   * 结果信息
   */
  ruleResultField: IRuleResultFieldItem[] | string;
  /**
   * 条件表达式
   */
  ruleCondField: any;
  /**
   * 规则配置类型
   */
  ruleOptionList: string[];
}

/**
 * rulesDetail的props约束接口
 */
export interface IDetailProps {
  /**
   * 组件实例
   */
  ref?: any;
  /**
   * 操作类型
   */
  type?: string;
  data: IDetailData;
  /**
   * 规则类型
   */
  ruleType?: 'limit' | 'auth';
  urlObj: IUrlObj;
}
/**
 * 运算表达式props约束接口
 */

export interface IOpExpsGraphProps {
  /**
   * 组件实例
   */
  ref?: any;
  /**
   * 规则类型
   */
  ruleType: 'limit' | 'auth';
  /**
   * 运算表达式数据
   */
  opExps: string;
  /**
   * 规则因子数据
   */
  ruleFacTypeList: Array<any>;
  canEdit: boolean;
}

/**
 * 规则因子数据约束接口
 */
export interface IRuleFac {
  /**
   * 配置类型
   */
  ruleExecuteType?: string[];
  ruleFacEnName: string;
  ruleFacCnName: string;
  /**
   * 规则因子类型
   */
  ruleFacValueType: string;
  /**
   * 最大长度
   */
  ruleFacMax: number;
  /**
   * 最小长度
   */
  ruleFacMin: number;
  /**
   * 小数位
   */
  ruleFacFloat?: number;
}

/**
 * 条件表达式props约束接口
 */
export interface IRuleCondGraphProps {
  /**
   * 组件实例
   */
  ref?: any;
  /**
   * 规则类型
   */
  ruleType: 'limit' | 'auth';
  /**
   * 条件表达式
   */
  ruleCondField: object;
  /**
   * 规则因子数据
   */
  ruleFacTypeList: Array<any>;
  canEdit: boolean;
}

/**
 * 条件表达式节点props约束
 */
export interface IRulesCondNodeProps {
  /**
   * 画布实例
   */
  graph?: any;
  /**
   * 数据
   */
  data?: any;
  canEdit?: boolean;
  /**
   * 菜单配置
   */
  getValueMenuList?: (type, data?) => TOptionItem[];
  /**
   * 校验失败响应
   */
  onError?: (errorList) => void;
  /**
   * 改变值事件回调
   */
  onChange?: (graph, tempResult, data) => void;
}

/**
 * 画布节点数据
 */
export interface INodeData {
  /**
   * 新增的节点默认数据
   */
  opNode: object;
  /**
   * 节点id
   */
  nodeId: string;
  /**
   * 节点编号
   */
  nodeNo: string;
}

/**
 * 画布节点数据约束接口
 */
export interface IGraphNodeData<T> {
  /**
   * 节点id
   */
  id: string;
  /**
   * 是否根节点
   */
  isRoot?: boolean;
  /**
   * 子节点编号
   */
  children?: string[];
  /**
   * 子节点数量
   */
  childrenNum?: number;
  /**
   * 节点数据
   */
  data: T;
  /**
   * 节点配置
   */
  config: object;
}
