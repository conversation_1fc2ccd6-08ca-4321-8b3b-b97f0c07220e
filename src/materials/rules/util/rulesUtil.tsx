import {
  COND_MENU_TYPE,
  RULE_GROUP_TYPE,
  OPEXPS_MENU_TYPE,
  MATH_NODE_TYPE,
  OPEXP_TYPE,
  RULE_OPTION_TYPE,
} from '@/constants/rulesConstant';
import { TOptionItem } from '@/types/TCommon';
import { OPERATE_TYPE } from '@/constants/publicConstant';
import { IDetailData, IOpExpsField } from '@/materials/rules/types/IRules';
import { INodeData } from '../types/IRules';
import { FormattedMessage } from 'react-intl';
import { prefix } from '../pageConfig';

/**
 * 获取下拉数据
 * @param list 将key-value的对象需要转换成select下拉数据
 * @param isIntl 是否需要国际化
 * @returns {array}
 */
export const getSelection = (list: object, isIntl = false): TOptionItem[] => {
  const res: TOptionItem[] = [];
  for (const key in list) {
    res.push({
      key,
      value: key,
      label: isIntl ? <FormattedMessage id={`${prefix}.${key}`} /> : key,
    });
  }
  return res;
};

/**
 * 设置详情数据
 * @param detailData 详情数据
 * @returns {object}
 */
export const setRulesDetailData = (detailData: IDetailData): IDetailData => {
  let result: IDetailData = {
    ...detailData,
    // opExpsField: { opExps: "" },
    ruleOptionList: [],
  };
  let { ruleCondField, ruleResultField } = detailData || {};
  const ruleOptionList: string[] = [];
  // 解析运算表达式
  // try {
  //   opExpsField = opExpsField ? JSON.parse(opExpsField as string) : {};
  //   const { opExps = "" } = (opExpsField as IOpExpsField) || {};
  //   result = { ...result, opExpsField: { opExps } };
  //   ruleOptionList.push(RULE_OPTION_TYPE.opExps);
  // } catch (error) {
  //   console.error("error=", error);
  // }
  // 解析条件表达式
  try {
    ruleCondField = JSON.parse(ruleCondField);
    result.ruleCondField = ruleCondField;
    ruleOptionList.push(RULE_OPTION_TYPE.judgeExps);
  } catch (error) {
    console.error('error=', error);
  }
  // 解析结果信息
  try {
    ruleResultField = JSON.parse(ruleResultField as string);
    if (ruleResultField.length) {
      result.ruleResultField = (
        ruleResultField as {
          id: string;
          ruleFac: string;
          ruleResult: string;
        }[]
      ).map((item, index) => {
        const ruleFac = Object.keys(item)[0];
        const ruleResult = item[ruleFac];
        return { id: `${index}${Date.now()}`, ruleFac, ruleResult };
      });
      ruleOptionList.push(RULE_OPTION_TYPE.ruleResultField);
    } else {
      result.ruleResultField = [];
    }
  } catch (error) {
    console.error('error=', error);
  }
  result.ruleOptionList = ruleOptionList;
  return result;
};

/**
 * 获取后代节点id
 * @param graph 画布实体
 * @param id 节点id
 * @returns {Array}
 */
export const getDescendantsData = (graph: any, id: string) => {
  const res: any = [];
  const relatedNodes = graph.getRelatedEdgesData(id, 'out');
  relatedNodes.forEach((node) => {
    const { target } = node;
    const temp = getDescendantsData(graph, target);
    res.push(target, ...temp);
  });
  return res;
};

/**
 * 随机生成几位数字字母
 * @param length
 * @returns
 */
export const generateRandomString = (length) => {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += Math.floor(Math.random() * 10); // 生成 0-9 的随机数字
  }
  return result;
};

/**
 * 菜单点击事件获取新增节点数据
 * @param graph 画布实例
 * @param e 点击事件目标对象
 * @param data 数据源
 * @returns {object | false}
 */
export const getEventNodeData = (graph, e, data): INodeData | false => {
  const { key } = e;
  const { id, childrenNum, data: parentData } = data;
  const { nodeNo: parentNodeNo, nodeDim } = parentData;
  let opNode = {};
  let nodeId = generateRandomString(8);
  let nodeNo = parentNodeNo
    ? `${parentNodeNo}.${childrenNum + 1}`
    : nodeDim
      ? `${nodeDim}.${childrenNum + 1}`
      : childrenNum + 1;
  switch (key) {
    // 删除节点
    case OPERATE_TYPE.delete:
      {
        const nodeIdList = [id];
        // 获取后代节点
        const relatedNodes = getDescendantsData(graph, id);
        nodeIdList.push(...relatedNodes);
        graph.removeNodeData(nodeIdList);
        graph.render();
      }
      return false;
    // 条件表达式-添加条件
    case COND_MENU_TYPE.addCond:
      opNode = { type: key, value: '' };
      break;
    // 条件表达式-添加条件组合
    case COND_MENU_TYPE.addCondGroup:
      opNode = { type: key, name: '', value: RULE_GROUP_TYPE.ALL };
      break;
    // 运算表达式-添加规则因子
    case OPEXPS_MENU_TYPE.ruleFacField:
      opNode = {
        type: MATH_NODE_TYPE.ConstantNode,
        name: OPEXP_TYPE.MIN,
        isNumber: false,
        value: '',
      };
      break;
    // 运算表达式-添加数值
    case OPEXPS_MENU_TYPE.number:
      // isNumber用于给getOpExpsData判断是数值还是规则因子
      opNode = {
        type: MATH_NODE_TYPE.ConstantNode,
        name: OPEXP_TYPE.MIN,
        isNumber: true,
        value: '',
      };
      break;
    // 运算表达式-添加函数
    case OPEXPS_MENU_TYPE.function:
      // 默认为MIN
      opNode = { type: MATH_NODE_TYPE.FunctionNode, name: OPEXP_TYPE.MIN };
      break;
    default:
      return false;
  }
  return { opNode, nodeId, nodeNo };
};
