// 运算表达式
import { FC, useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import _ from 'lodash';
import { Button } from 'antd';
import { GraphData } from '@antv/g6';
import { ReloadOutlined } from '@ant-design/icons';
import useIntlCustom from '@/hooks/useIntlCustom';
import { RulesGraph } from '@/components';
import { RULE_TYPE } from '@/constants/rulesConstant';
import { NOTIFICATION_TYPE } from '@/constants/publicConstant';
import { getEventNodeData } from '../util/rulesUtil';
import { getOpExpsGraphData, setOpExpsData, getOpExpsData, getMenuItems } from '../util/opExpsUtil';
import { INodeData, IOpExpsGraphProps } from '../types/IRules';
import { prefix } from '../pageConfig';
import styles from '../index.module.css';

const OpExpsGraph: FC<IOpExpsGraphProps> = forwardRef(
  ({ ruleType = RULE_TYPE.limit, opExps, ruleFacTypeList, canEdit }, ref = null) => {
    // 变量
    const { openNotificationTip } = useIntlCustom();
    const [graph, setGraph] = useState<any>(null);
    const [data, setData] = useState<GraphData>({});
    const [opExpression, setOpExpression] = useState<string>('');

    // 副作用
    useEffect(() => {
      if (graph) {
        getGraphData();
        setOpExpression(opExps);
      }
    }, [graph, opExps, ruleFacTypeList]);
    // 暴露给父组件的事件
    useImperativeHandle(ref, () => {
      return {
        onSubmit() {
          const opExps = setOpExpsData(graph, true);
          if (typeof opExps === 'object') {
            const { type } = opExps as any;
            openNotificationTip(prefix, NOTIFICATION_TYPE.ERROR, type);
            return null;
          } else {
            return JSON.stringify({ opExps });
          }
        },
      };
    });

    // 逻辑处理
    const getGraphData = (): void => {
      const graphData: GraphData = getOpExpsGraphData(opExps, ruleFacTypeList);
      setData(graphData);
      if (graph) {
        graph.setData(graphData);
        graph.render();
      }
    };
    // 获取画布实体
    const afterMounted = (g: any): void => {
      setGraph(g);
    };
    // 设置节点宽高
    const setNodeSize = (d: any): number[] => {
      const { type } = d?.data;
      return type ? [150, 48] : [220, 48];
    };
    // 菜单点击事件
    const handleMenuClcik = (graph, e, data): void => {
      const { id, childrenNum } = data;
      // 获取新增节点数据
      const nodeResult = getEventNodeData(graph, e, data);
      if (nodeResult) {
        const { opNode, nodeId, nodeNo } = nodeResult as INodeData;
        // 新增节点
        let nodeData = getOpExpsData(opNode, nodeId, nodeNo);
        graph.updateNodeData([{ id, childrenNum: childrenNum + 1 }]);
        graph.addChildrenData(id, [nodeData]);
        graph.render();
      }
    };
    // 更新画布数据
    const handleValueChange = _.debounce((graph, obj, data): void => {
      graph.updateNodeData([{ id: data.id, data: { ...data.data, ...obj } }]);
    }, 200);
    // 更新运算表达式
    const handleUpdateOpExps = (): void => {
      const opExps = setOpExpsData(graph) as string;
      setOpExpression(opExps);
    };
    // 传递给节点的参数
    const nodeProps = {
      canEdit,
      onMenuClick: handleMenuClcik,
      getMenuItems: (nodeData) => getMenuItems(nodeData, ruleType),
    };

    return (
      <div className={`flex-row ${styles.graphWrapper}`}>
        <div className={`flex-1 ${styles.opExpsGraph}`}>
          <RulesGraph
            id="opExpsGraph"
            data={data}
            nodeProps={nodeProps}
            afterMounted={afterMounted}
            setSize={setNodeSize}
            onValueChange={handleValueChange}
          />
        </div>
        <div className={styles.opExpress}>
          <p className={styles.textWrapper}>{opExpression}</p>
          <Button shape="circle" icon={<ReloadOutlined />} className={styles.updateBtn} onClick={handleUpdateOpExps} />
        </div>
      </div>
    );
  },
);

export default OpExpsGraph;
