// 条件表达式节点
import { FC, memo, useEffect, useState } from 'react';
import { Button } from 'antd';
import { FormattedMessage } from 'react-intl';
import _ from 'lodash';
import { CloseOutlined, CheckOutlined } from '@ant-design/icons';
import { COMPONENT_TYPE } from '@/constants/publicConstant';
import { RULE_COND_MENU_TYPE, RULE_FAC_LIST } from '@/constants/rulesConstant';
import { unShowValueComList, validateValue } from '../util/condNodeUtil';
import { prefix } from '../pageConfig';
import useRuleFacSelect from '../hooks/condNode/useRuleFacSelect';
import useOpTypeSelect from '../hooks/condNode/useOpTypeSelect';
import useValueComp from '../hooks/condNode/useValueComp';
import { IRulesCondNodeProps } from '../types/IRules';
import styles from '../index.module.css';

const RulesCondNode: FC<IRulesCondNodeProps> = ({
  graph,
  data,
  canEdit = true,
  getValueMenuList = () => [],
  onError = () => {},
  onChange = () => {},
}) => {
  // 变量
  const { config = {}, data: nodeData } = data;
  const [showSwitchButton, setShowSwitchButton] = useState<boolean>(false);
  const [editing, setEditing] = useState<boolean>(false);
  const [isHover, setIsHover] = useState<boolean>(false);
  // 规则因子值的组件类型
  const [valueComType, setValueComType] = useState<string>(COMPONENT_TYPE.INPUT);
  // 是否选择的是运算表达式
  const [isOpExps, setIsOpExps] = useState<boolean>(false);
  // 输入框或选择框的值
  const [comValue, setComValue] = useState<string>('');
  // 显示结果文本
  const [result, setResult] = useState<string>('');
  // 保存前的暂存结果对象
  const [tempResult, setTempResult] = useState<any>({});
  // 规则因子select框hooks
  const { valueType, opMenuList, valueMenuList, renderRuleFacSelectComp, setValueMenu } = useRuleFacSelect({
    nodeData,
    config,
    setIsOpExps,
    setComValue,
    setTempResult,
    setValueComType,
    getValueMenuList,
  });
  // 运算符select框hooks
  const { inputPlaceHolder, opType, renderOpTypeSelectComp } = useOpTypeSelect({
    nodeData,
    config,
    opMenuList,
    setComValue,
    setTempResult,
  });
  // 规则因子值输入框/下拉框/切换按钮hooks
  const { renderValueInputComp, renderValueSelectComp, renderSwitchButton } = useValueComp({
    config,
    comValue,
    setComValue,
    valueMenuList,
    setTempResult,
    setIsOpExps,
    setValueMenu,
    setValueComType,
    inputPlaceHolder,
  });

  // 副作用
  useEffect(() => {
    // 处理数据
    const { value, valueType, nodeCtx, wordSegBid } = nodeData;
    // 字符类型用,分隔
    if (Array.isArray(value) && valueType === RULE_FAC_LIST.STRING) {
      setComValue(value.join(','));
    } else if (wordSegBid) {
      // 数值型选了运算表达式
      setIsOpExps(true);
      setComValue(wordSegBid);
      setValueComType(COMPONENT_TYPE.SELECT);
      setValueMenu(RULE_COND_MENU_TYPE.opExps);
      setShowSwitchButton(true);
    } else {
      setComValue(value);
    }

    setTempResult(nodeData);
    setResult(nodeCtx);
  }, []);
  // 更新editing字段，在保存时判断是否有正在编辑的节点
  useEffect(() => {
    graph.updateNodeData([{ id: data.id, data: { ...nodeData, editing } }]);
  }, [editing]);
  // 数值型的规则因子可以选择运算表达式
  useEffect(() => {
    setShowSwitchButton(
      [RULE_FAC_LIST.INTEGER, RULE_FAC_LIST.LONGINTEGER, RULE_FAC_LIST.AMOUNT, RULE_FAC_LIST.ARRAY].includes(valueType),
    );
  }, [valueType]);

  // 事件处理
  const handleBtnHover = (hover: boolean): void => {
    if (!canEdit) {
      return;
    }
    setIsHover(hover);
  };
  // 点击节点改为编辑模式
  const handleClick = (): void => {
    canEdit && !editing && setEditing(true);
  };
  // 保存
  const handleSubmit = (): void => {
    let { fieldName, opName, value, wordSegBid } = tempResult;
    let tempValue = wordSegBid || value;
    // 校验格式
    const errorList = validateValue(tempResult);
    if (errorList.length) {
      onError(errorList);
      return;
    }
    // 拼接显示的结果文本
    let nodeCtx = `${fieldName}${opName} ${tempValue}`;
    setResult(nodeCtx);
    setEditing(false);
    setIsHover(false);
    const res = { ...tempResult, nodeCtx };
    if (isOpExps) {
      res.wordSegBid = tempValue;
      res.valueWorldSegType = 'OOEXPS';
      res.value = null;
      res.ruleFacValueType = null;
    } else {
      res.wordSegBid = null;
      res.valueWorldSegType = null;
    }
    onChange(graph, res, data);
  };
  // 关闭
  const handleClose = (): void => {
    setEditing(false);
    setIsHover(false);
  };

  return (
    <div
      className={`flex-1 flex-row flex-justify-center flex-align-center ${
        styles.condWrapper
      } ${!editing && isHover && styles.hoverText}`}
      onMouseEnter={() => handleBtnHover(true)}
      onMouseLeave={() => handleBtnHover(false)}
      onClick={handleClick}
    >
      {editing ? (
        <>
          {/* 规则因子下拉框 */}
          {renderRuleFacSelectComp()}
          {/* 布尔型不展示运算符下拉框 */}
          {valueType && valueType !== RULE_FAC_LIST.BOOLEAN && renderOpTypeSelectComp()}
          {/* unShowValueComList里的类型不展示值，规则因子类型需要展示输入框 */}
          {opType &&
            !unShowValueComList.includes(opType) &&
            valueComType === COMPONENT_TYPE.INPUT &&
            renderValueInputComp()}
          {/* 规则因子类型需要或布尔值的规则因子值展示下拉框 */}
          {(opType || valueType === RULE_FAC_LIST.BOOLEAN) &&
            valueComType === COMPONENT_TYPE.SELECT &&
            renderValueSelectComp()}
          {/* 数值型（如整数、金额等）可以切换规则因子值的输入框/下拉框 */}
          {opType && showSwitchButton && renderSwitchButton()}
          <Button
            type="link"
            icon={<CloseOutlined />}
            className={`m-lr-s ${styles.ruleFacButton}`}
            onClick={handleClose}
          />
          <Button
            type="link"
            icon={<CheckOutlined />}
            className={`m-r-s ${styles.ruleFacButton}`}
            onClick={handleSubmit}
          />
        </>
      ) : (
        <div className={`flex-row flex-justify-center flex-align-center p-lr ${styles.resultWrapper}`}>
          <div className={isHover ? `${styles.activeEditingText}` : `${styles.inactiveEditingText}`}>
            <FormattedMessage id={`${prefix}.editingText`} />
          </div>
          <div className={isHover ? `${styles.resultHoverText}` : `${styles.resultText}`}>{result}</div>
        </div>
      )}
    </div>
  );
};

export default memo(RulesCondNode);
