import { EDGE_TYPE, LAYOUT_TYPE, NODE_TYPE, PORTS_TYPE } from '@/constants/graphConstants';
import { LOCAL } from '@/constants/publicConstant';

// 主题色
const themeColor = localStorage.getItem(LOCAL.THEME) || '';

/**
 * 布局配置
 */
export const layoutConfig = {
  type: LAYOUT_TYPE.ANTV_DARGE,
  ranksep: 15,
  nodesep: 5,
};

/**
 * 节点配置
 */
export const nodeConfig = {
  type: NODE_TYPE.RECT,
  style: {
    size: [180, 42],
    // 标签取值
    labelText: (d) => d.label,
    // 标签位置
    labelPlacement: 'center',
    // 标签字体大小
    labelFontSize: 14,
    // 背景色
    fill: (d) => d.color,
    // 边框色
    stroke: themeColor,
    // 边框宽度
    lineWidth: 1,
    // 圆角
    radius: 8,
    // selected: {
    //   fill: themeColor,
    // },
    // 连接桩配置
    ports: [{ placement: PORTS_TYPE.TOP }, { placement: PORTS_TYPE.BOTTOM }],
  },
};

/**
 * 边配置
 */
export const edgeConfig = {
  type: EDGE_TYPE.CUBIC_VERTICAL,
  style: {
    endArrow: true,
    // 颜色
    stroke: themeColor,
    // 线宽度
    lineWidth: 1,
    // 虚线
    // lineDash: 3,
  },
};
