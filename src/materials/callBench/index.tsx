/**
 * 催收工作台组件
 */
import { CommonTable, Search } from '@/components';
import CommonModal from '@/components/CommonModal';
import { COMPONENT_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import useIntlCustom from '@/hooks/useIntlCustom';
import commonService from '@/services/common';
import { SettingOutlined } from '@ant-design/icons';
import { ProDescriptions } from '@ant-design/pro-components';
import { Anchor, Avatar, Button, Card, Form, Input, message, Segmented, Tooltip } from 'antd';
import { memo, useEffect, useRef, useState } from 'react';
import styles from './index.module.css';
import usePageConfig from './usePageConfig';

const CallBench = ({ dataSource, onSubmit, workBenchName, onCusLayout }) => {
  const { translate } = useIntlCustom();
  const [form] = Form.useForm();
  const formRef = useRef(null);
  const { descData, anchorData, segmentedData, resetValue, searchSource } = usePageConfig();
  const [open, setOpen] = useState<boolean>(false);
  const [workConfigData, setWorkConfigData] = useState<any>({
    callFields: descData,
    anchorData: Object.values(anchorData),
    segmentedData: Object.values(segmentedData),
  });

  const [searchValue, setSearchValue] = useState<any>({ ...resetValue });

  useEffect(() => {
    // getBenchConfig();
  }, []);

  // 查询事件
  const handleSearch = (searchValue: any): void => {
    setSearchValue(searchValue);
    // getTableData(searchValue);
  };

  // 获取页面配置信息
  // const getBenchConfig = async () => {
  //   const res: any = await commonService.getEditPostBiz({
  //     url: urlConstants.WORKBENCH_CONFIG.LIST,
  //     workBenchName,
  //   });
  //   const data = res.data?.[0] || [];
  //   // 催收员自己能看到的右侧模块信息-workBenchModel
  //   let newAnchorData: any = [];
  //   for (const id in anchorData) {
  //     if (data?.workBenchModel?.split(',')?.includes(id)) {
  //       newAnchorData.push(anchorData[id]);
  //     }
  //   }
  //   // 催收员自己能看到的左侧辅助信息-auxiliaryFunctions
  //   let newSegmentedData: any = [];
  //   for (const id in segmentedData) {
  //     if (data?.auxiliaryFunctions?.split(',')?.includes(id)) {
  //       newSegmentedData.push(segmentedData[id]);
  //     }
  //   }
  //   // 催收员自己能看到的催收信息表单项-callFields
  //   const newCallFields = descData[0].columns.filter((item) => data?.callFields?.split(',')?.includes(item.id));
  //   const oldCallfields = descData.filter((item) => data?.workBenchModel?.split(',')?.includes(item.id)).slice(1);

  //   setWorkConfigData({
  //     ...workConfigData,
  //     callFields: [
  //       {
  //         props: {
  //           title: translate('callParam', 'collectionInfoOverview'),
  //           column: 4,
  //         },
  //         type: COMPONENT_TYPE.FORM,
  //         id: 'infoForm1',
  //         columns: newCallFields,
  //       },
  //     ].concat(oldCallfields),
  //     anchorData: newAnchorData,
  //     segmentedData: newSegmentedData,
  //   });
  // };

  const handleSubmit = async () => {
    try {
      form.validateFields().then(async (values) => {
        const res: any = await commonService.getEditPostBiz({
          ...values,
          url: urlConstants.CASE_BASE_INFO.EDIT,
          id: dataSource.infoForm1?.id,
          model: dataSource.infoForm1?.model,
          prvBlockDate: dataSource.infoForm2?.blockDate,
        });
        if (res.header?.errorCode === '000000') {
          message.success(translate('callParam', 'blockCodeModifySuccess'));
          setOpen(false);
          onSubmit?.();
          form.resetFields();
        }
      });
    } catch (e) {
      console.info(e);
    }
  };

  const content = () => {
    return (
      <Form ref={formRef} form={form} name="basic" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} autoComplete="off">
        <Form.Item label={translate('callParam', 'accountLevel')} name="prvBlockcode" rules={[{ required: true }]}>
          <Input style={{ width: 200 }} disabled />
        </Form.Item>
        <Form.Item label={translate('callParam', 'accountBlockCode')} name="blockcode" rules={[{ required: true }]}>
          <Input style={{ width: 200 }} />
        </Form.Item>
      </Form>
    );
  };

  // 右侧锚点组件
  const anchorNode = () => {
    return (
      <Anchor
        className={styles.anchorNode}
        items={workConfigData?.anchorData}
        getContainer={() => document.querySelector('.app-container') as HTMLElement}
      />
    );
  };

  // 左侧操作按钮
  const segmentedNode = () => {
    const options = workConfigData?.segmentedData?.map((item) => {
      return {
        label: (
          <div
            className={styles.segmentedNode}
            onClick={() => {
              if (item.value !== 'C') return;
              setOpen(true);
              form.setFieldValue('prvBlockcode', dataSource.infoForm2?.blockcode);
            }}
          >
            <Tooltip title={item.label.title}>
              <Avatar style={{ backgroundColor: item.label.backgroundColor }}>{item.label.text}</Avatar>
            </Tooltip>
          </div>
        ),
        value: item.value,
      };
    });
    return <Segmented<string> vertical size="small" className={styles.segmented} options={options} />;
  };

  return (
    <>
      <Card className="app-container" style={{ paddingLeft: '36px' }}>
        <Tooltip title={translate('systemManage', 'customLayout')}>
          <Button
            type="primary"
            icon={<SettingOutlined />}
            style={{ position: 'fixed', top: 220, right: 50 }}
            onClick={() => onCusLayout(true)}
          />
        </Tooltip>
        {anchorNode?.()}
        {segmentedNode?.()}
        {open && (
          <CommonModal
            type="CALL_BLOCKCODE_MODAL"
            open={open}
            content={() => content()}
            onClose={() => setOpen(false)}
            onSubmit={handleSubmit}
          />
        )}
        {Array.isArray(workConfigData?.callFields) &&
          workConfigData?.callFields.map((item, index) => {
            const { id, type, columns: col, props } = item;
            const columns = Array.isArray(col) ? col : [];
            switch (type) {
              // 表格
              case COMPONENT_TYPE.TABLE:
                return (
                  <>
                    <Search
                      searchValue={resetValue}
                      resetValue={resetValue}
                      searchSource={searchSource}
                      intlPrefix="callParam"
                      onSearch={handleSearch}
                    />
                    <CommonTable
                      key={`${index}-${id}-table`}
                      columns={columns}
                      dataSource={dataSource[id]}
                      intlPrefix="callParam"
                      paginationConfig={{ showSizeChanger: true, showQuickJumper: true }}
                    />
                  </>
                );
              // 表单
              case COMPONENT_TYPE.FORM: {
                const tempColumns = columns.map((item) => ({
                  ...item,
                  ellipsis: true,
                  editable: item?.editable ? null : false,
                }));
                return (
                  <ProDescriptions
                    key={`${index}-${id}-form`}
                    id={id}
                    dataSource={dataSource[id]}
                    columns={tempColumns}
                    className="p-b"
                    {...props}
                  />
                );
              }
              default:
                return null;
            }
          })}
      </Card>
    </>
  );
};

export default memo(CallBench);
