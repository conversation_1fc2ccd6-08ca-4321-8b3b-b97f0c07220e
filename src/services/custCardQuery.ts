import urlConstants from '@/constants/urlConstants';
import { postData } from '@/utils/serviceUtil';
import _ from 'lodash';

// 获取持卡人信息查询列表
export async function getCardholderInfoList(val: object): Promise<any> {
  const res = await postData('/service/card/getCardholderListInfo', val);
  const { data } = res;
  const result = _.isNil(data) ? {} : { ...data, paramIndex: 1 };
  return result;
}

// 获取持卡人地址信息列表和详情数据
export async function getCardholderDetailList(val: object): Promise<any> {
  const res = await postData('/service/card/getCardholderListInfo/detail', val);
  const { data } = res;
  return data;
}

// 获取卡片信息列表
export async function getCardInfoList({ searchValue }): Promise<any> {
  const { idNo: oldCustNo, crcdCardholderNo, crcdNo } = searchValue;
  const body = { oldCustNo, crcdCardholderNo, crcdNo };
  const { data } = await postData(urlConstants.CARD_QUERY.LIST, { body });
  let itemArray: any = [];
  if (Array.isArray(data?.cardInfoList) && data.cardInfoList?.length) {
    let i = 1;
    itemArray = data?.cardInfoList.map((item: any) => ({
      ...item,
      paramIndex: i++,
    }));
  }
  return { data: itemArray };
}

export default {
  getCardholderInfoList,
  getCardholderDetailList,
};
