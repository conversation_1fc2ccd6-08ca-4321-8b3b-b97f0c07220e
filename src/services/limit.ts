import { postData } from '@/utils/serviceUtil';

// 额度节点查询页面
export async function getLimitNodeList(data) {
  const res = await postData('/api/nq/param/pageQuery', data);
  return { data: res, total: 100 };
}

// 额度节点查询规则引擎
export async function getRuleData(data) {
  const res = await postData('/api/rule', data);
  return res;
}

// 额度管控单元页面查询接口
export async function getLimitUnitList(data) {
  const res = await postData('/api/limitUnit', data);
  return { data: res, total: 68 };
}

export default {
  getLimitNodeList,
  getRuleData,
  getLimitUnitList,
};
