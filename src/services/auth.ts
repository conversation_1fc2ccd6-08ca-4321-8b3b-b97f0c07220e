import dayjs from 'dayjs';
import { deleteData, getData, postData } from '@/utils/serviceUtil';

// 根据卡片使用率组号或直接查询交易标签使用率映射参数列表数据
export async function getTradingListById(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/getTradingListById', data);
  let itemArray: any = [];
  if (res && res.length > 0) {
    let i = 1;
    res.map((item: any) => {
      itemArray.push({ ...item, id: i++ });
    });
  }
  // console.log(itemArray);
  return { data: itemArray, total: 9 };
}
// 授权交易类型参数查询接口
export async function getTradingTypeListById(data: object): Promise<any> {
  const res = await postData('/api/nq/param/pageQuery/getTradingTypeListById', data);
  let itemArray: any = [];
  if (res && res.length > 0) {
    let i = 1;
    res.forEach((item: any) => {
      itemArray.push({
        ...item,
        updateTime: dayjs(item.updateTime),
        updateTimeShow: item.updateTime,
        id: i++,
      });
    });
  }
  return { data: itemArray, total: 3 };
}
// 根据卡片使用率组号查询使用率检查编号，去重后并返回数据（待确认）
// 根据使用率检查编号查询交易限额检查参数表并返回数据
export async function getTradingDetailById(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/getTradingDetail', data);
  let itemArray: any = [];
  if (res && res.length > 0) {
    let i = 1;
    res.map((item: any) => {
      itemArray.push({ ...item, updateTime: dayjs(item.updateTime), id: i++ });
    });
  }
  // console.log(itemArray);
  return itemArray;
}
// 查询授权交易检查项控制参数表列表
export async function getAuthTransactionCheckList(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/getAuthTransactionCheckList', data);
  let itemArray: any = [];
  if (res && res.length > 0) {
    let i = 1;
    res.map((item: any) => {
      // itemArray.push({ ...item, id: i++ });
      itemArray.push({
        ...item,
        updateTime: dayjs(item.updateTime),
        updateTimeShow: item.updateTime,
        createTime: dayjs(item.createTime),
        createTimeShow: item.createTime,
        id: i++,
      });
    });
  }
  // console.log(itemArray);
  // return itemArray;
  return { data: itemArray, total: 3 };
}
// 根据组合编号查询授权交易检查项控制详细参数表数据
export async function getAuthTransCheckDetail(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/getAuthTransCheckDetail', data);
  let itemArray: any = [];
  if (res && res.length > 0) {
    let i = 1;
    res.map((item: any) => {
      itemArray.push({
        ...item,
        updateTime: dayjs(item.updateTime),
        updateTimeShow: item.updateTime,
        id: i++,
      });
    });
  }
  // console.log(itemArray);
  return { data: itemArray, total: 3 };
}

// 授权交易检查项控制基础、详细参数表数据编辑
export async function authTransCheckEdit(data: object): Promise<any> {
  // console.log(data);
  const baseRes = await postData('/anytxn-param/nq/param/pageQuery/authTransCheckBaseEdit', data);
  const detailRes = await postData('/anytxn-param/nq/param/pageQuery/authTransCheckDetailEdit', data);
  // console.log(baseRes, detailRes);
  return detailRes;
}

// 加密秘钥索引参数表列表查询
export async function getEncryptionKeyList(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/getEncryptionKeyList', data);
  let itemArray: any = [];
  if (res && res.length > 0) {
    let i = 1;
    res.map((item: any) => {
      itemArray.push({
        ...item,
        updateTime: dayjs(item.updateTime),
        updateTimeShow: item.updateTime,
        createTime: dayjs(item.createTime),
        createTimeShow: item.createTime,
        id: i++,
      });
    });
  }
  return { data: itemArray, total: 4 };
}

// 加密秘钥索引参数编辑
export async function encryptionKeyEdit(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/getEncryptionKeyDetailEdit', data);
  return res;
}

// 授权交易检查项参数表列表查询
export async function getAuthTranParamList(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/getAuthTranParamList', data);
  let itemArray: any = [];
  if (res && res.length > 0) {
    let i = 1;
    res.map((item: any) => {
      itemArray.push({
        ...item,
        updateTime: dayjs(item.updateTime),
        updateTimeShow: item.updateTime,
        createTime: dayjs(item.createTime),
        createTimeShow: item.createTime,
        id: i++,
      });
    });
  }
  return { data: itemArray, total: 3 };
}

// 授权交易检查项参数编辑
export async function authTranParamEdit(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/authTranParamEdit', data);
  return res;
}

// 风险特店参数表列表查询
export async function getRiskSpecialList(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/getRiskSpecialList', data);
  let itemArray: any = [];
  if (res.data && res.data.length > 0) {
    let i = 1;
    res.data.map((item: any) => {
      itemArray.push({ ...item, paramIndex: i++ });
    });
  }
  return { data: itemArray, total: 11 };
}

// 风险特店参数新增
export async function riskSpecialAdd(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/riskAdd', data);
  return res;
}

// 风险特店参数修改
export async function riskSpecialEdit(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/riskEdit', data);
  return res;
}

// 风险特店参数删除
export async function riskSpecialDelete(data: object): Promise<any> {
  const res = await deleteData('/anytxn-param/nq/param/pageQuery/riskDelete', data);
  return res;
}

// 紅利折抵特店参数列表查询
export async function getDividendDiscountList(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/getDividendDiscountList', data);
  let itemArray: any = [];
  if (res.data && res.data.length > 0) {
    let i = 1;
    res.data.map((item: any) => {
      itemArray.push({ ...item, paramIndex: i++ });
    });
  }
  return { data: itemArray, total: 11 };
}

// 紅利折抵特店参数新增或修改
export async function dividendDiscountEdit(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/dividendDiscountEdit', data);
  return res;
}

// 获取流量管控参数列表数据
export async function getTrafficControlParamList(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/getTrafficControlParamList', data);
  let itemArray: any = [];
  if (res.data && res.data.length > 0) {
    let i = 1;
    res.data.map((item: any) => {
      itemArray.push({ ...item, paramIndex: i++ });
    });
  }
  return { data: itemArray, total: 11 };
}

// 编辑或者新增流量管控参数
export async function trafficControlParamEdit(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/param/pageQuery/trafficControlParamEdit', data);
  return res;
}

export default {
  getTradingTypeListById,
  getAuthTransactionCheckList,
  getAuthTransCheckDetail,
  authTransCheckEdit,
  getEncryptionKeyList,
  encryptionKeyEdit,
  getAuthTranParamList,
  authTranParamEdit,
  getRiskSpecialList,
  riskSpecialAdd,
  riskSpecialEdit,
  riskSpecialDelete,
  getDividendDiscountList,
  dividendDiscountEdit,
  getTrafficControlParamList,
  trafficControlParamEdit,
};
