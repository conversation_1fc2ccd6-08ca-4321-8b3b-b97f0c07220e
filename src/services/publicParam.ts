import { postData, getData, deleteData } from '@/utils/serviceUtil';

// 获取机构参数列表
export async function getMechanismParamList(data: object): Promise<any> {
  const res = await getData('/anytxn-param/nq/service/pageQuery/getOrgList', data);
  let itemArray: any = [];
  if (res && res.length > 0) {
    let i = 1;
    res.map((item: any) => {
      // itemArray.push({ ...item, createTime: dayjs(item.createTime), createTimeShow: item.createTime, updateTime: dayjs(item.updateTime), updateTimeShow: item.updateTime, paramIndex: i++ });
      itemArray.push({ ...item, paramIndex: i++ });
    });
  }
  return { data: itemArray, total: 12 };
}

// 编辑机构参数
export async function editMechanismParam(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/service/pageQuery/editOrgParam', data);
  return res;
}

// 获取工作日参数列表
export async function getweekDayParamList(data: object): Promise<any> {
  const res = await getData('/anytxn-param/nq/service/pageQuery/getWeekdayList', data);
  let itemArray: any = [];
  if (res && res.length > 0) {
    let i = 1;
    res.map((item: any) => {
      // itemArray.push({ ...item, createTime: dayjs(item.createTime), createTimeShow: item.createTime, updateTime: dayjs(item.updateTime), updateTimeShow: item.updateTime, paramIndex: i++ });
      itemArray.push({ ...item, paramIndex: i++ });
    });
  }
  return { data: itemArray, total: 11 };
}

// 编辑工作日参数
export async function editWeekdayParam(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/service/pageQuery/editWeekdayParam', data);
  return res;
}

// 获取系统参数表单数据
export async function getSysParamData(): Promise<any> {
  const res = await getData('/anytxn-param/nq/service/pageQuery/getSysFormData');
  return res;
}

// 编辑系统参数表单
export async function editSysParamData(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/service/pageQuery/editSysFormData', data);
  return res;
}

// 获取假日参数列表数据
export async function getholidayParamList(data: object): Promise<any> {
  const res = await getData('/anytxn-param/nq/service/pageQuery/getHolidayList', data);
  let itemArray: any = [];
  if (res && res.length > 0) {
    let i = 1;
    res.map((item: any) => {
      itemArray.push({ ...item, paramIndex: i++ });
    });
  }
  return { data: itemArray, total: 11 };
}

// 编辑假日参数
export async function editHolidayParamData(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/service/pageQuery/editHolidayData', data);
  return res;
}

// 获取持卡人封锁码参数
export async function getCardHolderBlockCodeParamData(data: object): Promise<any> {
  const res = await postData('/api/anytxn-param/nq/service/pageQuery/getCardHolderBlockCodeParam', data);
  return { data: res, total: 11 };
}

// 获取汇率参数列表数据
export async function getExchangeRateParamList(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/service/pageQuery/getExchangeRateParamList', data);
  let itemArray: any = [];
  if (res.data && res.data.length > 0) {
    let i = 1;
    res.data.map((item: any) => {
      itemArray.push({ ...item, paramIndex: i++ });
    });
  }
  return { data: itemArray, total: 11 };
}

// 获取汇率参数历史列表数据
export async function getHistoryOfRateParamList(data: object): Promise<any> {
  const res = await postData('/anytxn-param/nq/service/pageQuery/getHistoryOfRateParamList', data);
  let itemArray: any = [];
  if (res.data && res.data.length > 0) {
    let i = 1;
    res.data.map((item: any) => {
      itemArray.push({ ...item, paramIndex: i++ });
    });
  }
  return { data: itemArray, total: 11 };
}

export default {
  getMechanismParamList,
  editMechanismParam,
  getweekDayParamList,
  editWeekdayParam,
  getSysParamData,
  editSysParamData,
  getholidayParamList,
  editHolidayParamData,
  getCardHolderBlockCodeParamData,
  getExchangeRateParamList,
  getHistoryOfRateParamList,
};
