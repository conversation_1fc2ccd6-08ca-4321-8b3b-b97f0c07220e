import { ILoginParams, ILoginResult } from '@/types/IUser';
import { getData, postData, deleteData } from '@/utils/serviceUtil';

export async function login(data: ILoginParams): Promise<ILoginResult> {
  const res = await postData('/api/login', data);
  return res;
}

export async function logout(data: object): Promise<ILoginResult> {
  const res = await postData('/api/logout', data);
  return res;
}

export async function toGetList(data: object): Promise<any> {
  const res = await getData('/api/user/list', data);
  return res;
}

export async function toGetSingle(data: object): Promise<any> {
  const res = await getData('/api/userInfo', data);
  return res;
}

export async function toCreate(data: object): Promise<any> {
  const res = await postData('/api/user/create', data);
  return res;
}

export async function toUpdate(data: object): Promise<any> {
  const res = await postData('/api/user/update', data);
  return res;
}

export async function toDelete(data: object): Promise<any> {
  const res = await deleteData('/api/user/delete', data);
  return res;
}
