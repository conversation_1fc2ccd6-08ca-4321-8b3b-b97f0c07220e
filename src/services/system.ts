import { getData, postData } from '@/utils/serviceUtil';

export async function getList(data: object): Promise<any> {
  const res = await getData('/api/RoleInfo', data);
  // return res;
  return { data: res, total: 3 };
}

export async function updateData(data: object): Promise<any> {
  const res = await postData('/api/RoleInfo', data);
  return res;
}

export async function updateMountData(data: object): Promise<any> {
  const res = await postData('/api/RoleInfo/updateMountData', data);
  return res;
}

export async function getMountDataById(data: object): Promise<any> {
  const res = await getData('/api/RoleInfo/MountDataById', data);
  let itemArray: any = [];
  if (res && res.menuInfoList && res.menuInfoList.length > 0) {
    res.menuInfoList.map((item) => {
      delete item.createUser;
      delete item.createTs;
      delete item.updateUser;
      delete item.updateTs;
      itemArray.push(item);
    });
  }
  const returnVal = {
    roleName: res.roleName,
    roleId: res.roleId,
    menuInfoList: itemArray,
  };
  // console.log(returnVal);
  return returnVal;
}

export default {
  getList,
  updateData,
  updateMountData,
  getMountDataById,
};
