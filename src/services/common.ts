import { IQueryData, IQueryParam, IResponseResult } from '@/types/IRequest';
import { postData } from '@/utils/serviceUtil';
import {
  setBusinessQueryParamData,
  setParamQueryUrlParamData,
  setParamUpdateUrlParamData,
  setResult,
} from '@/utils/urlUtil';

// 获取字典
const getDictionaryData = async (data) => await postData('/api/dictionary', data);

// 参数列表分页查询(connector:连接符，默认and,可传参覆盖；conditionKey：默认为searchValue对象属性名，可选择传参覆盖)
const getTableListData = async (param, connector: string = '', conditionKey: string = ''): Promise<IQueryData> => {
  const params = setParamQueryUrlParamData(param, connector, conditionKey);
  // const { URL } = param.url;
  const res = await postData(param.url, params);
  return setResult(res);
};

// 参数：新增，编辑，删除
const getEditPost = async (params) => {
  const postParams = setParamUpdateUrlParamData(params);
  // const { URL } = params.url;

  // 检查参数接口通过才调维护接口
  // const checkRes = await postData(PARAMS_URLOBJ.chekc, postParams);
  // if (checkRes.header?.errorCode === SUCCESS_CODE) {
  const res = await postData(params.url, postParams);
  return res ? { ...res } : false;
  // }
  // return false;
};
// 列表分页查询
const getTableListDataBiz = async (params: IQueryParam): Promise<IQueryData> => {
  const { body, url } = setBusinessQueryParamData(params);
  const res = await postData(url, { body });
  return setResult(res);
};

// 新增，编辑，删除
const getEditPostBiz = async (params): Promise<false | IResponseResult<any>> => {
  const newParams = {
    body: { ...params },
  };
  delete newParams.body.url;
  delete newParams.body?.type;
  const res = await postData(params.url, newParams);
  return res ? { ...res } : false;
};

// 通用业务查询接口，不处理返回数据
const getCommonListData = async (params: IQueryParam): Promise<any> => {
  const { body, url } = setBusinessQueryParamData(params);
  const res = await postData(url, { body });
  return res ? { ...res } : false;
};

export default {
  getDictionaryData,
  getTableListData,
  getEditPost,
  getTableListDataBiz,
  getEditPostBiz,
  getCommonListData,
};
