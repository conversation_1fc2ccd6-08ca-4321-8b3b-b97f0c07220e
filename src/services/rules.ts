import urlConstants from '@/constants/urlConstants';
import { postData } from '@/utils/serviceUtil';

// 额度节点查询页面
export async function getLimitRuleList(data) {
  const res = await postData('/api/nq/getLimitRuleList', data);
  return { data: res, total: 1 };
}

// 查询规则数据
export async function getRulesData(data) {
  const res = await postData('/api/nq/getRulesData', data);
  return res;
}

// 查询规则因子数据
export async function getRuleFac(data) {
  const res = await postData('/api/nq/getRuleFac', data);
  return res;
}

// 根据规则类型查询规则因子数据
export async function getRuleFacByRuleType(data) {
  const param = { body: { ...data } };
  const res = await postData(urlConstants.RULES_FACTOR.LIST_BY_RULE_TYPE, param);

  return Array.isArray(res?.data) ? res.data : [];
}

// 查询字典数据
export async function getDictByType(data) {
  const res = await postData('/api/nq/getDictByType', data);
  return res;
}

export default {
  getLimitRuleList,
};
