import { MutableRefObject } from 'react';
import { IFormConfig } from '@/types/IForm';

export type IPropsType = {
  disabled: boolean;
  style: object;
  width?: number;
  [key: string]: unknown;
};

export interface IFormTemplateProps {
  ref?: any;
  id?: string;
  formRef?: MutableRefObject<any> | undefined;
  config: Array<IFormConfig>;
  loading?: boolean;
  canEdit: boolean;
  showMaintenance: boolean;
  className?: string;
  formLayout?: object;
  initialData?: any;
  intlPrefix: string;
  /**
   * 列宽
   */
  colSpan?: number;
  editTableRefList?: object;
  onChange?: (obj: object, allValues?: object) => void;
  onExpand?: (key) => void;
  onCheck?: (key, form) => void;
  onLoad?: (form) => void;
  onSelect?: (key, info, form?) => void;
  onSearch?: (key, form) => void;
}
