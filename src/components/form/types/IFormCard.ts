export interface IFormCardProps {
  children?: React.ReactNode;
  title?: React.ReactNode | string;
  extra?: React.ReactNode | string; // 右侧扩展组件，优先级高于 showAction
  type?: string;
  showBack?: boolean;
  showAction?: boolean;
  resetTitle?: string;
  submitTitle?: string;
  submitLoading?: boolean;
  className?: string;
  intlPrefix?: string;
  shouldScroll?: boolean;
  onBack?: () => void;
  onReset?: () => void;
  onSubmit?: () => void;
}
