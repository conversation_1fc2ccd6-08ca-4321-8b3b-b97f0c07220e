.formMaintenance {
  position: sticky;
  bottom: 0;
  width: 100%;
  background-color: var(--gray-one);
  border-radius: 0 0 5px 5px;
}

/* FormHeader 组件样式 */
.formHeader {
  margin-bottom: 20px;
  border-radius: 8px;
  /* background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 50%, #f0f8ff 100%); */
  /* padding: 12px 16px; */
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06); */
}

.formDivider {
  margin: 0 !important;
  border-color: #e0e0e0;
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 让内容分布在两端 */
  gap: 8px;
  font-weight: 500;
  color: #2c3e50;
  width: 100%; /* 确保占满整个宽度 */
  position: relative; /* 为绝对定位的子元素提供参考 */
}

.extraBtn {
  position: absolute;
  left: 720%;
  display: flex;
  align-items: center;
  white-space: nowrap; /* 防止按钮内容换行 */
}

.headerIcon {
  display: flex;
  align-items: center;
  color: #1890ff;
  font-size: 16px;
}

.headerChildren {
  display: flex;
  align-items: center;
}

.headerTitle {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

/* 左侧内容容器 */
.headerLeft {
  display: flex;
  align-items: center;
  gap: 8px;
}
