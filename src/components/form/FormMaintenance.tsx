/**
 * Created by ca<PERSON><PERSON> on 2025/6/6.
 * 当前页面字段和数据库字段一致，允许耦合存在
 */

import React, { ReactNode } from 'react';
import { Row, Col } from 'antd';
import { renderValueByType } from '@/utils/comUtil';
import useIntlCustom from '@/hooks/useIntlCustom';
import styles from './form.module.css';
import { RENDER_TYPE } from '@/constants/publicConstant';
import { IFormMaintenance } from './types/IFormMaintenance';

const FormMaintenance: React.FC<IFormMaintenance> = ({ data, showVersion = false, showUserInfo = true, childNode }) => {
  const { version, createTime, updateTime, createUser, updateUser } = data || {};
  let child = {};
  if (showVersion) {
    child = { version };
  }
  if (showUserInfo) {
    child = {
      ...child,
      createUser,
      createTime: renderValueByType(createTime, {
        valueType: RENDER_TYPE.DateTime,
      }),
      updateTime: renderValueByType(updateTime, {
        valueType: RENDER_TYPE.DateTime,
      }),
      updateUser,
    };
  }
  const { formateHtmlText } = useIntlCustom();
  const renderChild = () => {
    const res: ReactNode[] = [];
    for (const key in child) {
      let span = key === 'version' ? 3 : 5;
      let className = key === 'version' ? 'error-color' : '';
      res.push(
        <Col key={key} span={span} className="m-r-s">
          <span className={className}>
            {formateHtmlText('common', key)} : {child[key]}
          </span>
        </Col>,
      );
    }
    return res;
  };

  return <Row className={`flex-row p-tb-s ${styles.formMaintenance}`}>{childNode || renderChild()}</Row>;
};

export default FormMaintenance;
