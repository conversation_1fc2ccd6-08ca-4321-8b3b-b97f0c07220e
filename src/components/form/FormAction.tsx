/**
 * Created by ca<PERSON>un on 2025/6/6.
 */

import { I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';
import styleConstant from '@/constants/styleConstant';
import useAuthButton from '@/hooks/useAuthButton';
import useIntlCustom from '@/hooks/useIntlCustom';
import { CloseOutlined, DeleteOutlined, FormOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import _ from 'lodash';
import React, { ReactNode } from 'react';
import { IFormActionProps } from './types/IFormAction';

const FormAction: React.FC<IFormActionProps> = ({
  title = '',
  extra,
  showEdit = false,
  showCreate = true,
  showCancel = false,
  showDelete = false,
  submitLoading = false,
  permissionObj,
  onEdit = () => {},
  onSubmit = () => {},
  onCreate = () => {},
  onCancel = () => {},
  onDelete = () => {},
}) => {
  const { translate } = useIntlCustom();
  const { hasPermission } = useAuthButton();
  // 获取按钮国际化名称
  const getBtnName = (key) => {
    return translate(I18N_COMON_PAGENAME.COMMON, key);
  };
  // 按钮组件
  const renderButtonComp = (actionType: string): ReactNode => {
    let buttonComp: ReactNode;
    switch (actionType) {
      case OPERATE_TYPE.create:
        buttonComp = (
          <Button type="primary" icon={<PlusOutlined />} style={styleConstant.mtbs} onClick={onCreate}>
            {getBtnName(actionType)}
          </Button>
        );
        break;
      case OPERATE_TYPE.edit:
        buttonComp = (
          <Button type="primary" icon={<FormOutlined />} style={styleConstant.mrs} onClick={onEdit}>
            {getBtnName(actionType)}
          </Button>
        );
        break;
      case OPERATE_TYPE.cancel:
        buttonComp = (
          <Button icon={<CloseOutlined />} style={styleConstant.mrs} onClick={onCancel}>
            {getBtnName(actionType)}
          </Button>
        );
        break;
      case OPERATE_TYPE.submit:
        buttonComp = (
          <Button type="primary" icon={<SaveOutlined />} loading={submitLoading} onClick={_.debounce(onSubmit)}>
            {getBtnName(actionType)}
          </Button>
        );
        break;
      case OPERATE_TYPE.delete:
        buttonComp = (
          <Button type="primary" icon={<DeleteOutlined />} style={styleConstant.mrs} onClick={onDelete}>
            {getBtnName(actionType)}
          </Button>
        );
        break;
      default:
        break;
    }
    // 是否有传permissiId
    const permissionId = permissionObj && permissionObj[actionType];
    return permissionId ? hasPermission(permissionId) && buttonComp : buttonComp;
  };
  return (
    <div className="form-action">
      <span>{title}</span>
      <div className="flex-row flex-align-center">
        {extra}
        {showCreate && renderButtonComp(OPERATE_TYPE.create)}
        {showEdit && renderButtonComp(OPERATE_TYPE.edit)}
        {showCancel && renderButtonComp(OPERATE_TYPE.cancel)}
        {showDelete && renderButtonComp(OPERATE_TYPE.delete)}
      </div>
    </div>
  );
};
export default FormAction;
