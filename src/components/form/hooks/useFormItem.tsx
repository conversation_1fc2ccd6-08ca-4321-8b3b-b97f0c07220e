import { EditTable, FormHearder } from '@/components';
import {
  COMPONENT_TYPE,
  DATE_FORMATE,
  DEFAULT_AMOUNT_PROPS,
  FORM_LAYOUT_TYPE,
  FORMITEM_TYPE,
  NOT_BELONG_COMPONENT,
} from '@/constants/publicConstant';
import styleConstant from '@/constants/styleConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import store from '@/store';
import { IUnknownProperties } from '@/types/ICommon';
import { formatformItemData, formatformItemNormalize, formatterAmountInput, parserAmountInput } from '@/utils/comUtil';
import { isEmpty } from '@/utils/mathUtil';
import {
  Button,
  Calendar,
  Cascader,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
  Space,
  TimePicker,
  Tree,
  TreeSelect,
} from 'antd';
import { Fragment, ReactNode } from 'react';
import { IPropsType } from '../types/IFormTemplate';
import useFormList from './useFormList';

const useFormItem = ({
  form,
  canEdit = true, // 是否可以编辑
  intlPrefix,
  colSpan = 11,
  onChange = (obj) => {},
  onExpand = (key) => {},
  onCheck = (key, form) => {},
  onLoad = (form) => {},
  onSelect = (key, info, form?) => {},
  onSearch = (e, form) => {},
}) => {
  const { translate, getSelectOption, defaultInputPlaceholder, defaultSelectPlaceholder } = useIntlCustom();
  const [dictState] = store.useModel('dict');
  // 一行两列
  const renderTwoFields = (data) => {
    const res: Array<ReactNode> = [];
    if (data && data.length > 0) {
      data.forEach((item, index) => {
        if (isEmpty(item)) {
          return;
        }
        const com = renderOneFields(item);
        const { name, label, rules, prefix, format, dependencies = [], colSpan: itemColSpan } = item;
        const formItemProp = {
          name,
          label: label ? translate(prefix || intlPrefix, label) : '',
          rules,
          dependencies,
        };
        const colProps =
          item.formlayoutType === FORM_LAYOUT_TYPE.ONE ? { span: 24 } : { span: itemColSpan || colSpan, offset: 1 };
        const otherFormItemProps = item.formlayoutType === FORM_LAYOUT_TYPE.ONE ? { ...styleConstant.formLayout } : {};
        res.push(
          <Col key={index} {...colProps}>
            <Form.Item
              {...formItemProp}
              {...otherFormItemProps}
              getValueProps={(value) => ({
                value: formatformItemData(item.type, value),
              })}
              normalize={(value) => formatformItemNormalize(item.type, value, format)}
            >
              {com}
            </Form.Item>
          </Col>,
        );
      });
    }
    return res;
  };

  // form 真正需要渲染的组件
  const renderOneFields = (data) => {
    if (isEmpty(data)) {
      return null;
    }
    // 遍历剔除不属于组件的属性
    const props: IPropsType = { disabled: false, style: {} };
    // 获取不属于组件的属性的值
    const notBelongArray = Object.values(NOT_BELONG_COMPONENT);
    for (const p in data) {
      if (!notBelongArray.includes(p)) {
        props[p] = data[p];
      }
    }
    // 详情页面或者传入值
    props.disabled = props.disabled || !canEdit;

    const { type, label, format, isint, showKey, name } = data;
    // 国际化前缀，可以使用自定义模块
    const prefix = data.prefix || intlPrefix;
    // 是否需要国际化，默认需要
    let isInt = !(isint && isint === '0');
    const tempProps: IUnknownProperties = { ...props };
    tempProps.style = {
      width: props?.width || styleConstant.width100.width,
      height: props?.height,
    };
    let formComResult: any;
    switch (type) {
      case COMPONENT_TYPE.INPUT:
        formComResult = <Input placeholder={defaultInputPlaceholder} {...tempProps} showCount={!!props?.maxLength} />;
        break;
      case COMPONENT_TYPE.SEARCH:
        formComResult = (
          <Input.Search
            placeholder={defaultInputPlaceholder}
            onSearch={(e) => handleSearch(e, form)}
            {...tempProps}
            enterButton={translate(intlPrefix, data.enterButton)}
          />
        );
        break;
      case COMPONENT_TYPE.EXTARA_BTN_INPUT:
        formComResult = (
          <Space.Compact style={styleConstant.width100}>
            <Input placeholder={defaultInputPlaceholder} {...tempProps} />
            <Button type="primary" onClick={() => onChange?.({ [label]: true })}>
              {translate(intlPrefix, data.enterButton)}
            </Button>
          </Space.Compact>
        );
        break;
      case COMPONENT_TYPE.TEXTAREA:
        formComResult = <Input.TextArea {...tempProps} showCount={!!props?.maxLength} />;
        break;
      case COMPONENT_TYPE.INPUT_NUMBER:
        formComResult = <InputNumber placeholder={defaultInputPlaceholder} {...tempProps} />;
        break;
      case COMPONENT_TYPE.AMOUNT_INPUT: {
        const tempAmountProps: IUnknownProperties = {
          ...DEFAULT_AMOUNT_PROPS,
          ...tempProps,
          formatter: (value) => formatterAmountInput(value, DEFAULT_AMOUNT_PROPS.decimal),
          parser: (value) => parserAmountInput(value),
        };
        formComResult = <InputNumber placeholder={defaultInputPlaceholder} {...tempAmountProps} />;
        break;
      }
      case COMPONENT_TYPE.RADIO: {
        const options = isInt ? getSelectOption(data?.data, showKey, prefix) : data?.data;
        formComResult = (
          <Radio.Group options={options} name={name} {...props} onChange={(e) => handleRadioChange(e, form)} />
        );
        break;
      }
      case COMPONENT_TYPE.TREE:
        onLoad(form);
        formComResult = (
          <Tree
            {...props}
            treeData={data.data}
            onExpand={(expandedKeys) => handleTreeExpand(expandedKeys)}
            onCheck={(key) => handleTreeCheck(key)}
            onSelect={(key, info) => handleTreeSelect(key, info)}
          />
        );
        break;
      case COMPONENT_TYPE.TREE_SELECT:
        formComResult = <TreeSelect {...props} treeData={data.data} />;
        break;
      case COMPONENT_TYPE.CHECK_BOX:
        formComResult = <Checkbox.Group options={data.data} />;
        break;
      case COMPONENT_TYPE.SELECT:
        {
          let selectSource = [];
          let isDict = false;
          // 有传枚举
          if (data?.data) {
            selectSource = data.data;
          } else {
            // 查字典或参数枚举
            selectSource = dictState.dictMap[data.name];
            isDict = true;
          }
          formComResult = (
            <Select
              placeholder={defaultSelectPlaceholder}
              {...tempProps}
              options={isInt && !isDict ? getSelectOption(selectSource, data.showKey, prefix) : selectSource}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
            />
          );
        }
        break;
      case COMPONENT_TYPE.CASCADER:
        formComResult = (
          <Cascader
            placeholder={defaultSelectPlaceholder}
            {...tempProps}
            options={getSelectOption(data?.data, data.showKey, prefix)}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
          />
        );
        break;
      case COMPONENT_TYPE.DATE_PICKER:
        formComResult = <DatePicker {...tempProps} format={format || DATE_FORMATE} />;
        break;
      case COMPONENT_TYPE.RANGE_PICKER:
        formComResult = <DatePicker.RangePicker {...tempProps} />;
        break;
      case COMPONENT_TYPE.TIME_RANGE_PICKER:
        const { RangePicker } = TimePicker;
        formComResult = <RangePicker {...tempProps} />;
        break;
      default:
        formComResult = null;
        break;
    }
    return formComResult;
  };

  // 渲染整个表单组件
  const renderFormItem = (data, index) => {
    let formItemResult: any;
    switch (data.type) {
      case FORMITEM_TYPE.FormHearder: // 弹层标题
        formItemResult = (
          <FormHearder key={index} title={translate(data.prefix || intlPrefix, data.title)} children={data.children} />
        );
        break;
      case FORMITEM_TYPE.Row: // 表单布局，一行两列
        formItemResult = <Row key={index}>{renderTwoFields(data.data)}</Row>;
        break;
      case FORMITEM_TYPE.EditTable: // 可编辑表格
        formItemResult = <EditTable key={index} {...data} intlPrefix={intlPrefix} ref={data.editableFormRef} />;
        break;
      case FORMITEM_TYPE.Calendar: // 日历组件
        formItemResult = (
          <Form.Item key={index} name={data.name} {...styleConstant.formLayout4}>
            <Calendar onSelect={(e, { source }) => handleCalSelect(e, { source }, form)} {...data.data} />
          </Form.Item>
        );
        break;
      case FORMITEM_TYPE.Single: // 一行一列
        {
          const { data: formData } = data;
          if (Array.isArray(formData) && formData?.length) {
            formItemResult = (
              <Fragment key={index}>
                {formData.map((formItemData, formIndex) => {
                  const { name, label, rules, shouldUpdate, setVisibleFunc } = formItemData;
                  const formItemProp = {
                    name,
                    label: label ? translate(intlPrefix, label) : '',
                    rules,
                    shouldUpdate,
                  };
                  // shouldUpdate：判断其他表单项是否改变
                  return shouldUpdate ? (
                    <Form.Item key={`single-${index}-${formIndex}`} shouldUpdate={shouldUpdate} noStyle>
                      {(formInstance) =>
                        // setVisibleFunc：根据表单项的字段决定是否展示表单项
                        setVisibleFunc(formInstance) ? (
                          <Form.Item
                            key={`single-${index}-${formIndex}`}
                            {...styleConstant.formLayout}
                            {...formItemProp}
                          >
                            {renderOneFields(formItemData)}
                          </Form.Item>
                        ) : null
                      }
                    </Form.Item>
                  ) : (
                    <Form.Item key={`single-${index}-${formIndex}`} {...styleConstant.formLayout} {...formItemProp}>
                      {renderOneFields(formItemData)}
                    </Form.Item>
                  );
                })}
              </Fragment>
            );
          }
        }
        break;
      // 字段数组化
      case FORMITEM_TYPE.List:
        // List组件的hooks
        const { renderFormList } = useFormList({ renderOneFields, intlPrefix });
        formItemResult = renderFormList(data);
        break;
      default:
        formItemResult = null;
        break;
    }
    return formItemResult;
  };

  const handleRadioChange = (e, form) => {
    if (onChange) {
      const obj = { e, form };
      onChange(obj);
    }
  };
  const handleSearch = (e, form) => {
    onSearch && onSearch(e, form);
  };
  const handleTreeExpand = (expandedKeys) => {
    onExpand && onExpand(expandedKeys);
  };
  const handleTreeCheck = (key) => {
    onCheck && onCheck(key, form);
  };
  const handleTreeSelect = (key, info) => {
    onSelect && onSelect(key, info);
  };
  const handleCalSelect = (e, { source }, form) => {
    onSelect && onSelect(e, { source }, form);
  };
  return {
    renderFormItem,
  };
};

export default useFormItem;
