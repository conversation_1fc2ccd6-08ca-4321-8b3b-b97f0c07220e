/**
 * Created by ca<PERSON><PERSON> on 2025/6/6.
 *
 */

import { DatabaseOutlined } from '@ant-design/icons';
import { Divider } from 'antd';
import React from 'react';
import styles from './form.module.css';
import { IFormHeaderProps } from './types/IFormHearder';

const FormHearder: React.FC<IFormHeaderProps> = ({ children, title, icon, extraBtn }) => {
  const defaultIcon = <DatabaseOutlined />;

  return (
    <div className={styles.formHeader}>
      <Divider orientation="left" className={styles.formDivider}>
        <div className={styles.headerContent}>
          <div className={styles.headerLeft}>
            <span className={styles.headerIcon}>{icon || defaultIcon}</span>
            {children && <span className={styles.headerChildren}>{children}</span>}
            {title && <span className={styles.headerTitle}>{title}</span>}
          </div>
          <div className={styles.extraBtn}>{extraBtn?.()}</div>
        </div>
      </Divider>
    </div>
  );
};

export default FormHearder;
