/**
 * Created by ca<PERSON><PERSON> on 2024/6/1.
 * 表单容器（功能和formDrawer一样）：如果有自定义extra，优先自定义，再判断showAction展示默认提交/重置
 */

import { OPERATE_TYPE } from '@/constants/publicConstant';
import styleConstant from '@/constants/styleConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import { RedoOutlined, SaveOutlined } from '@ant-design/icons';
import { Button, Card, Spin } from 'antd';
import _ from 'lodash';
import React, { MutableRefObject, Suspense, useEffect, useRef, useState } from 'react';
import { IFormCardProps } from './types/IFormCard';

const FormCard: React.FC<IFormCardProps> = ({
  children,
  extra,
  title = '',
  type = '', // OPERATE_TYPE
  showBack = false,
  showAction = true,
  resetTitle = '重置',
  submitTitle = '提交',
  submitLoading = false,
  className = '',
  intlPrefix,
  shouldScroll = true,
  onBack = () => {},
  onReset = () => {},
  onSubmit = () => {},
}) => {
  // 内部变量
  const { translate, formatActionTitle } = useIntlCustom();
  const cardRef: MutableRefObject<any> = useRef(null);
  const [cardHeight, setCardHeight] = useState(0);
  // 副作用
  useEffect(() => {
    // 获取当前Card组件Ref，用来设置高度
    if (cardRef.current) {
      const cardStyles = window.getComputedStyle(cardRef.current);
      setCardHeight(parseInt(cardStyles.height));
    }
  }, []);
  // 逻辑处理
  const getTitleNode = () => {
    let res = typeof title === 'string' && intlPrefix ? translate(intlPrefix, title) : title;

    return (
      <div className="flex-row flex-align-center">
        <div className="theme-color-main form-title-prefix" />
        {res}
      </div>
    );
  };
  const getActionNode = () => {
    const disabled = type === OPERATE_TYPE.detail;
    return disabled ? null : (
      <>
        <Button icon={<RedoOutlined />} style={styleConstant.mrs} onClick={onReset}>
          {resetTitle}
        </Button>
        <Button type="primary" icon={<SaveOutlined />} loading={submitLoading} onClick={_.debounce(onSubmit)}>
          {submitTitle}
        </Button>
      </>
    );
  };

  return (
    <Suspense fallback={<Spin size="large" />}>
      <Card
        ref={cardRef}
        title={getTitleNode()}
        styles={{
          body: {
            height: cardHeight - 72,
            overflow: shouldScroll ? 'auto' : 'hidden',
          },
        }}
        extra={extra || (showAction ? getActionNode() : null)}
        className={`app-container ${className}`}
      >
        {children || <div />}
      </Card>
    </Suspense>
  );
};
export default FormCard;
