import { Graph, GraphOptions } from '@antv/g6';

interface INodeItemProps {
  type: string;
  label: string;
  nodeType: string;
  prefix?: string;
  src?: string;
}
export interface INodePanelProps {
  nodeList: Array<INodeItemProps>;
  onDragNodeEnd?: (e) => void;
}

interface IBindEventsProps {
  onNodeClick?: (graph: Graph) => void | undefined;
  onNodeDbClick?: (graph: Graph) => void | undefined;
  // onNodeContextMenu?: (graph: Graph) => void | undefined;
  // onEdgeContextMenu?: (graph: Graph) => void | undefined;
  // onCanvasContextMenu?: (graph: Graph) => void | undefined;
  // 继续穷尽内部注册事件，注意命名规范
}
export interface IG6Props {
  dragNodeList?: Array<INodePanelProps> | null;
  options: GraphOptions | null;
  onRender?: (graph: Graph) => void;
  onDestroy?: () => void;
  bindEvents?: IBindEventsProps;
}
