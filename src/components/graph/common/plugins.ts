/**
 * 工具栏配置
 * G6 内置了11个 icon，分别是 zoom-in、zoom-out、redo、undo、edit、delete、auto-fit、export、reset、request-fullscreen、exit-fullscreen
 */
export const getDefaultToolBarItems = () => {
  return [
    { id: 'zoom-in', value: 'zoom-in' },
    { id: 'zoom-out', value: 'zoom-out' },
    { id: 'redo', value: 'redo' },
    { id: 'undo', value: 'undo' },
    { id: 'edit', value: 'edit' },
    { id: 'delete', value: 'delete' },
    { id: 'auto-fit', value: 'auto-fit' },
    { id: 'export', value: 'export' },
    { id: 'reset', value: 'reset' },
    { id: 'request-fullscreen', value: 'request-fullscreen' },
    { id: 'exit-fullscreen', value: 'exit-fullscreen' },
  ];
};

/**
 * 点击工具栏事件
 * todo 未完全实现
 */
export const handleDefaultToolBarClick = (item, graph) => {
  const history = graph.getPluginInstance('history');
  switch (item) {
    case 'zoom-in':
      graph.zoomTo(1.2);
      break;
    case 'zoom-out':
      graph.zoomTo(0.8);
      break;
    case 'redo':
      if (history.canRedo()) history.redo();
      break;
    case 'undo':
      if (history.canUndo()) history.undo();
      break;
    case 'edit':
      console.log('edit', item, graph);
      break;
    case 'delete':
      console.log('delete', item, graph);
      break;
    case 'auto-fit':
      graph.zoomTo(1);
      graph.fitCenter();
      break;
    case 'export':
      console.log('export', item, graph);
      break;
    case 'reset':
      console.log('reset', item, graph);
      break;
    case 'request-fullscreen':
      console.log('request-fullscreen', item, graph);
      break;
    case 'exit-fullscreen':
      console.log('exit-fullscreen', item, graph);
      break;
  }
};
