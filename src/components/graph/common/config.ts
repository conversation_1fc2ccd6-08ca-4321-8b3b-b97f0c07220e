import { PORTS_TYPE } from '@/constants/graphConstants';

/**
 * 节点默认配置
 */
export const nodeDefaultConfig = {
  type: 'circle',
  style: {
    size: 60,
    stroke: '#000',
    lineWidth: 2,
    portR: 3,
    ports: [
      { placement: PORTS_TYPE.LEFT },
      { placement: PORTS_TYPE.RIGHT },
      { placement: PORTS_TYPE.TOP },
      { placement: PORTS_TYPE.BOTTOM },
    ],
    portLineWidth: 1,
    portStroke: '#fff',
  },
};

/**
 * 边默认配置
 */
export const edgeDefaultConfig = {
  type: 'polyline',
  style: {
    stroke: '#8c8c8c',
    endArrow: true,
  },
};
