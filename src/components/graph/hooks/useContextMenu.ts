/**
 * 右键菜单hooks
 */
import { useState } from 'react';
import { ELE_TYPE, MENU_TYPE } from '@/constants/graphConstants';
import { addNode } from '../common/nodeUtil';

const useContextMenu = () => {
  // 右键菜单位置
  const [contextPointer, setContextPointer] = useState({});
  const [copyNodeId, setCopyNodeId] = useState(''); // todo 考虑多节点操作
  const menuItems = [
    { name: '复制', value: MENU_TYPE.COPY },
    { name: '粘贴', value: MENU_TYPE.PASTE },
    { name: '编辑', value: MENU_TYPE.EDIT },
    { name: '删除', value: MENU_TYPE.DELETE },
  ];

  // 菜单配置
  const getMenuDefaultItems = (e) => {
    console.log('右击类型', e.targetType);
    switch (e.targetType) {
      // 节点
      case ELE_TYPE.NODE:
        return menuItems.filter((item) => item.value !== MENU_TYPE.PASTE);
      // 边只展示删除
      case ELE_TYPE.EDGE:
        return menuItems.filter((item) => item.value === MENU_TYPE.DELETE);
      // 右击画布展示粘贴
      case ELE_TYPE.CANVAS:
        return copyNodeId ? menuItems.filter((item) => item.value === MENU_TYPE.PASTE) : [];

      default:
        return menuItems;
    }
  };

  // 菜单点击事件
  const handleMenuClick = (type, target, current, graph) => {
    const { id } = current;
    switch (type) {
      // 复制
      case MENU_TYPE.COPY: {
        setCopyNodeId(id);
        break;
      }
      // 粘贴 todo
      case MENU_TYPE.PASTE:
        let node = graph.getNodeData(id) || {};
        let { data, style } = node;
        const { x, y } = style;
        data = { ...data, x: x + 50, y: y + 50 };
        const model = addNode(graph, data, false);
        graph?.addNodeData([model]);
        graph?.render();
        console.log('没有来执行清空');
        setCopyNodeId('');
        break;
      // 触发节点/边的删除事件
      case MENU_TYPE.DELETE:
        current.type === ELE_TYPE.NODE ? graph.removeNodeData([id]) : graph.removeEdgeData([id]);
        graph?.render();
        break;
      default:
        break;
    }
  };

  // 画布右键菜单事件
  const handleContextMenu = (e) => {
    setContextPointer({ ...e.client });
  };

  return {
    getMenuDefaultItems,
    handleContextMenu,
    handleMenuClick,
  };
};

export default useContextMenu;
