import { ReactElement } from 'react';
import { GraphData } from '@antv/g6';
import { IUnknownProperties } from '@/types/ICommon';
import { TDropDownMenuItem } from '@/types/TCommon';

interface IConfig {
  /**
   * 边配置
   */
  edgeConfig?: object;
  /**
   * 节点配置
   */
  nodeConfig?: object;
  /**
   * 布局配置
   */
  layoutConfig?: object;
}

/**
 * RulesGraph组件约束接口
 */
export interface IRulesGraphProps {
  /**
   * 画布容器id
   */
  id: string;
  /**
   * 画布数据
   */
  data: GraphData;
  /**
   * 画布配置
   */
  config?: IConfig;
  /**
   * 传递到节点的props
   */
  nodeProps?: object;
  /**
   * 画布挂载回调事件
   */
  afterMounted: (graph: any) => void;
  /**
   * 设置节点大小
   */
  setSize?: (d) => any;
  /**
   * 节点点击事件
   */
  onNodeClick?: (obj: object) => void;
  /**
   * 节点数据改变事件
   */
  onValueChange?: (type: string, e: any) => void;
}

/**
 * 自定义节点数据约束接口
 */
interface ICustomNodeData {
  data: any;
  config?: any;
  rightChildren?: ReactElement;
}
/**
 * 自定义节点props约束接口
 */
export interface ICustomNodeProps extends IUnknownProperties {
  graph: any;
  data: ICustomNodeData;
  getMenuItems?: (data) => TDropDownMenuItem[];
  onChange: (graph, value, data) => void;
  onMenuClick?: (graph, e, data) => void;
}
