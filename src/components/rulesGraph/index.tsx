// todo: 与graph合并
import styleConstant from '@/constants/styleConstant';
import { getFontSize } from '@/utils/comUtil';
import { Graph } from '@antv/g6';
import { FC, useEffect, useRef, useState } from 'react';
import { IRulesGraphProps } from './IRulesGraph';
import './shape';
import CustomNode from './shape/CustomNode';

// 默认样式
const defaultConfig = {
  nodeSep: getFontSize('1.2rem'), // 适中的节点间距，既紧凑又美观
  rankSep: getFontSize('1.8rem'), // 适中的层级间距
  edgeStoke: styleConstant.grayMain,
};

const RulesGraph: FC<IRulesGraphProps> = ({
  id,
  data,
  config = {},
  nodeProps = {},
  afterMounted = () => {},
  setSize,
  onNodeClick = () => {},
  onValueChange = () => {},
}) => {
  const [siderBtnDom, setSiderBtnDom] = useState<any>();
  const ref = useRef<HTMLDivElement>(null);
  let graph;
  const { edgeConfig = {}, nodeConfig = {}, layoutConfig = {} } = config;

  // 副作用
  useEffect(() => {
    renderGraph();
    bindEvent();
    return () => {
      removeEvent();
    };
  }, []);

  // 逻辑处理
  const renderGraph = () => {
    if (!graph && ref.current) {
      graph = new Graph({
        container: ref.current, // 画布
        data, // 画布数据
        animation: false, // 动画
        // 布局
        layout: {
          type: 'dagre',
          rankdir: 'LR', // H / V / LR / RL / TB / BT
          rankSep: defaultConfig.rankSep,
          nodeSep: defaultConfig.nodeSep, // 最小节点间距
          nodesep: defaultConfig.nodeSep, // 兼容性设置
          edgesep: defaultConfig.nodeSep, // 边与节点的间距
          ...layoutConfig,
        },
        // 节点配置
        node: {
          type: 'react',
          style: (d: any) => {
            const size = setSize ? setSize(d) : [50, 28]; // 进一步减小节点尺寸，适应最小间距
            return {
              component: <CustomNode graph={graph} data={d} onChange={onValueChange} {...nodeProps} />,
              size: size, // 使用一致的节点大小
            };
          },
          ...nodeConfig,
        },
        // 边
        edge: {
          type: 'custom-polyline',
          style: {
            stroke: defaultConfig.edgeStoke,
            strokeWidth: 1.5, // 适中的边宽度
            lineDash: [0], // 实线
            endArrow: true, // 使用默认箭头
            cursor: 'pointer', // 鼠标悬停时显示手型
          },
          ...edgeConfig,
        },
        behaviors: ['drag-canvas'], // 画布交互事件
        autoResize: true, // 自动调整大小
      });
    }
    graph.render();
    afterMounted(graph);
  };

  // 绑定事件
  const bindEvent = () => {
    graph.on('node:click', handleNodeClick);
    const siderBtnDom = document.getElementsByClassName('sider-collapsed-button');
    if (siderBtnDom && siderBtnDom.length) {
      setSiderBtnDom(siderBtnDom[0]);
      siderBtnDom[0].addEventListener('click', handleSiderClick);
    }
  };

  // 移除事件
  const removeEvent = () => {
    graph.off('node:click', handleNodeClick);
    siderBtnDom && siderBtnDom.removeEventListener('click', handleSiderClick);
  };

  // 事件处理
  // 节点点击事件
  const handleNodeClick = (e) => {
    onNodeClick(e);
  };
  // 左侧菜单栏展开收缩事件
  const handleSiderClick = (e) => {
    graph?.onResize();
  };

  return <div ref={ref} id={id} style={{ ...styleConstant.width100, ...styleConstant.height100 }} />;
};

export default RulesGraph;
