.wrapper {
  /* width: max-content; */
  height: 100%;
  position: relative;
  border-radius: 8px; /* 增加圆角 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* 优化阴影效果 */
  border: 1px solid #e8e8e8; /* 添加边框 */
  background: white; /* 确保背景色 */
  .leftButton {
    width: 1.5rem; /* 进一步减小左侧按钮宽度 */
    height: 100%;
    border: 0;
    border-radius: 4px 0 0 4px; /* 调整圆角适应更小尺寸 */
    background-color: var(--gray-main);
    .hoverSetButton {
      font-size: 0.8rem; /* 进一步减小图标大小 */
      color: var(--gray-one);
    }
  }
  .rightWrapper {
    width: 100%;
    height: 100%;
    border-radius: 0 4px 4px 0; /* 调整圆角适应更小尺寸 */
    background: white; /* 确保右侧背景色 */
    padding: 0 0.08rem; /* 适中的内边距，内容更舒适 */
  }
  .rightInput {
    width: 100%;
    border: none;
    font-size: 0.7rem; /* 进一步减小字体大小 */
    padding: 0; /* 移除内边距 */
  }
  .rightSelect {
    border-width: 0;
    box-shadow: none;
    font-size: 0.7rem; /* 进一步减小字体大小 */
    padding: 0; /* 移除内边距 */
  }
}
