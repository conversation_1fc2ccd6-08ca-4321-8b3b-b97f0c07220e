// 直角折线
import { BaseEdge } from '@antv/g6';

class PolylineEdge extends BaseEdge {
  getKeyPath(): any {
    const { x: sourceX, y: sourceY, size: sourceSize } = this.sourceNode.attributes;
    const { x: targetX, y: targetY, size: targetSize } = this.targetNode.attributes;

    // 适中的偏移量，适应适中间距
    const offset = 3;

    // 计算起点：源节点的右边缘中心
    let startX = sourceX + sourceSize[0] + offset;
    let startY = sourceY + sourceSize[1] / 2;

    // 计算终点：目标节点的左边缘中心
    let endX = targetX - offset;
    let endY = targetY + targetSize[1] / 2;

    // 计算中间点，让连接线更平滑
    const midX = (startX + endX) / 2;

    // 创建直角折线路径
    return [
      ['M', startX, startY], // 移动到起点
      ['L', midX, startY], // 水平线到中间
      ['L', midX, endY], // 垂直线到目标高度
      ['L', endX, endY], // 水平线到终点
    ];
  }
}

export default PolylineEdge;
