// pageTemplate搜索框组件
import { Search } from '@/components';
import { useState } from 'react';
import { IUsePageSearch } from '../types/IPageTemplate';

const usePageSearch = ({ searchConfig, intlPrefix, pagination, getTableData }: IUsePageSearch) => {
  const [searchValue, setSearchValue] = useState<object>({
    ...(searchConfig.resetValue || {}),
  });

  // 搜索事件
  const handleSearch = (searchValue: object): void => {
    setSearchValue(searchValue);
    getTableData({ ...pagination }, searchValue);
    // 搜索后回调
    const { onAfterSearch } = searchConfig?.props || {};
    onAfterSearch && onAfterSearch(searchValue);
  };
  // 渲染搜索框
  const renderSearch = () => {
    const { searchSource, searchValue, resetValue, props = {} } = searchConfig;
    return searchSource.length ? (
      <Search
        searchValue={searchValue}
        resetValue={resetValue}
        searchSource={searchSource}
        intlPrefix={intlPrefix}
        // buttonDisabled={searchBtnDisabled}
        onSearch={handleSearch}
        {...props}
      />
    ) : null;
  };

  return {
    searchValue,
    renderSearch,
  };
};

export default usePageSearch;
