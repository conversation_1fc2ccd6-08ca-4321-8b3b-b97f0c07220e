import { CommonTable } from '@/components';
import { I18N_COMON_PAGENAME, NOTIFICATION_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import services from '@/services';
import { ICommonTableActionItem } from '@/types/ICommon';
import { IUsePageTable } from '../types/IPageTemplate';

const usePageTable = ({
  tableConfig,
  urlObj,
  pagination,
  paginationConfig,
  tableData,
  tableLoading,
  intlPrefix,
  setType,
  setRowData,
  setPagination,
  setDrawerOpen,
  getTableData,
  onAction,
}: IUsePageTable) => {
  const { openNotificationTip } = useIntlCustom();
  // 删除
  const deleteData = async (data: object, type: string): Promise<void> => {
    try {
      const { delete: deleteUrl, getRequestData } = urlObj;
      const deleteFunc = services.common.getEditPostBiz;
      const getPostData = getRequestData ? getRequestData(data, type) : data;
      if (!deleteFunc) {
        return;
      }
      const result = await deleteFunc({
        url: deleteUrl,
        ...getPostData,
      });
      if (result) {
        getTableData(pagination);
        openNotificationTip(I18N_COMON_PAGENAME.COMMON, NOTIFICATION_TYPE.SUCCESS, 'deleteSuccess', 1);
      } else {
        openNotificationTip(I18N_COMON_PAGENAME.COMMON, NOTIFICATION_TYPE.ERROR, 'deleteFailed');
      }
    } catch (error) {
      console.error(error);
    }
  };

  // 操作事件
  const handleAction = async (actionType: any, row: object, optionList: Array<string | ICommonTableActionItem>) => {
    // 表格操作列事件回调函数，返回操作类型和该条数据
    await onAction(actionType, row);
    // 删除
    if (actionType === OPERATE_TYPE.delete) {
      return deleteData(row, OPERATE_TYPE.delete);
    }

    setType(actionType);
    setRowData(row);
    // 展示自定义操作按钮内容,调用回调事件（否则会直接打开弹层）
    const isOnCustomize: any = optionList?.find((item: any) => item?.type === actionType);
    if (isOnCustomize?.onCustomize) {
      return isOnCustomize.onCustomize(true, row);
    }
    setDrawerOpen(true);
  };

  // 改变分页事件
  const handleTableChange = (pagination) => {
    const { current: currentPage, pageSize } = pagination;
    setPagination({ currentPage, pageSize });
    getTableData({ currentPage, pageSize });
  };

  // 渲染表格
  const renderTable = () => {
    const {
      rowKey,
      columns = [],
      optionList = [OPERATE_TYPE.detail, OPERATE_TYPE.edit, OPERATE_TYPE.delete],
      props,
      dataSource,
    } = tableConfig || {};

    return (
      <CommonTable
        rowKey={rowKey}
        columns={columns}
        optionList={optionList}
        paginationConfig={paginationConfig === false ? paginationConfig : { ...paginationConfig, ...pagination }}
        dataSource={dataSource || tableData}
        loading={tableLoading}
        intlPrefix={intlPrefix}
        onAction={handleAction}
        onChange={handleTableChange}
        props={props}
      />
    );
  };
  return { renderTable, handleAction };
};

export default usePageTable;
