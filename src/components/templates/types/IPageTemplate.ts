import {
  IColumns,
  ICommonTableActionItem,
  IFormActionPermissionObj,
  IPaniation,
  ISearchSource,
  IUnknownProperties,
  IUrlObj,
} from '@/types/ICommon';
import { TablePaginationConfig } from 'antd';

interface ISearchProps extends IUnknownProperties {
  /**
   * 触发搜索后回调事件
   */
  onAfterSearch?: (value: object) => void;
}

/**
 * 搜索框约束接口
 */
export interface ISearchConfig {
  /**
   * search表格配置
   */
  searchSource: Array<ISearchSource>;
  /**
   * search表格查询条件字段
   */
  searchValue: object;
  /**
   * search表格重置查询条件字段
   */
  resetValue: object;
  /**
   * 查询接口默认传参
   */
  defaultParam?: object;
  /**
   * 其余传递给search的props
   */
  props?: ISearchProps;
}

/**
 * 表格约束接口
 */
export interface ITableConfig {
  /**
   * 表格每行的唯一键
   */
  rowKey?: string;
  /**
   * 表格列表配置
   */
  columns: Array<IColumns>;
  /**
   * 操作列
   */
  optionList?: Array<string | ICommonTableActionItem>;
  /**
   * 是否展示分页
   */
  showPagination?: boolean;
  /**
   * 其余传递给table的props
   */
  props?: object;
  /**
   * 表格change事件回调
   */
  onChange?: (record: object | undefined) => void;
  /**
   * 表格写死mock数据
   */
  dataSource?: Array<string>;
}

/**
 * 详情表单配置
 */
export interface IFormConfig {
  /**
   * 表单配置
   */
  config: Array<any>;
  /**
   * 表单数据
   */
  data: object;
  /**
   * 国际化前缀
   */
  intlPrefix: string;
  /**
   * 表单底部是否显示维护信息
   */
  showMaintenance?: boolean;
  /**
   * 自定义详情组件
   */
  customChildren?: React.ReactNode;
  props?: {
    editTableRefList?: object;
  };
  /**
   * 表单change事件回调
   */
  onChange?: (e) => void;
}

/**
 * 自定义form组件
 */
export interface ICustomFormConfig {
  formRef;
  /**
   * 自定义组件
   */
  customChildren: React.ReactNode;
}

/**
 * 表格上方操作栏约束接口
 */
export interface IFormActionConfig {
  /**
   * 是否展示提交按钮
   */
  showSubmit?: boolean;
  /**
   * 是否展示新增按钮
   */
  showCreate?: boolean;
  /**
   * 详情是否展示详情
   */
  detailShowCreate?: boolean;
  /**
   * 按钮权限
   */
  permissionObj?: IFormActionPermissionObj;
  /**
   * 详情自定义新增方法
   */
  onDetailCreate?: () => void;
}

/**
 * pageTemplate组件props约束接口
 */
export interface IPageTemplateProps {
  /**
   * search组件属性
   */
  searchConfig: ISearchConfig;
  /**
   * table组件属性
   */
  tableConfig: ITableConfig;
  /**
   * form组件属性
   */
  formConfig?: IFormConfig | ICustomFormConfig | any;
  /**
   * formAction组件属性
   */
  formActionConfig?: IFormActionConfig;
  /**
   * table类型
   */
  tableType?: string; // commom/tree
  /**
   * 请求接口配置
   */
  urlObj: IUrlObj;
  /**
   * 下方组件标题
   */
  cardTitle?: React.ReactNode | string;
  /**
   * 国际化前缀
   */
  intlPrefix: string;
  /**
   * 页面需要查询的字典或参数
   */
  dictEnum?: any;
  /**
   * 下方Card组件的extra属性
   */
  cardExtra?: React.ReactNode;
  /**
   * form组件的extra属性
   */
  formExtra?: React.ReactNode;
  /**
   * 是否展示路由栏
   */
  isShowRouterBar?: boolean;
  /**
   * 操作列事件回调
   */
  onAction?: (type: string, row: object) => void;
}

/**
 * usePageForm的约束接口
 */
export interface IUsePageForm {
  /**
   * form组件实例
   */
  formRef: any;
  /**
   * form组件属性
   */
  formConfig?: IFormConfig | ICustomFormConfig;
  /**
   * 操作类型
   */
  type: string;
}

/**
 * usePageFormAction的约束接口
 */
export interface IUsePageFormAction {
  /**
   * form组件实例
   */
  formRef: any;
  /**
   * 操作类型
   */
  type: string;
  /**
   * 请求接口配置
   */
  urlObj: IUrlObj;
  /**
   * form组件属性
   */
  formConfig?: IFormConfig;
  /**
   * formAction组件属性
   */
  formActionConfig?: IFormActionConfig;
  /**
   * 操作列事件
   */
  handleAction: (type: string, row: any) => void;
  /**
   * 返回事件
   */
  handleCardBack: () => void;
}

/**
 * usePageSearch的约束接口
 */
export interface IUsePageSearch {
  /**
   * search组件属性
   */
  searchConfig: ISearchConfig;
  /**
   * 国际化前缀
   */
  intlPrefix: string;
  /**
   * 分页配置
   */
  pagination: IPaniation;
  /**
   * 查询事件
   */
  getTableData: (pagination: IPaniation, searchData: object) => void;
}

/**
 * usePageTable的约束接口
 */
export interface IUsePageTable extends IUnknownProperties {
  setType: any;
  setRowData: any;
  setDrawerOpen: (open: boolean) => void;
  setPagination: any;
  /**
   * table组件属性
   */
  tableConfig?: ITableConfig;
  /**
   * 请求接口配置
   */
  urlObj: IUrlObj;
  /**
   * 分页配置
   */
  pagination: IPaniation;
  /**
   * 分页配置
   */
  paginationConfig: false | TablePaginationConfig;
  /**
   * table是否加载中
   */
  tableLoading: boolean;
  /**
   * 表格数据
   */
  tableData: any[];
  /**
   * 国际化前缀
   */
  intlPrefix: string;
  /**
   * 查询事件
   */
  getTableData: (pagination?: IPaniation, searchData?: object) => void;
  /**
   * 获取请求方法
   */
  getRequestFunc: (type: string) => any;
  /**
   * 操作列回调
   */
  onAction: (type: string, row: object) => void;
}

/**
 * usePageUrl的约束接口
 */
export interface IUsePageUrl {
  /**
   * search组件属性
   */
  searchConfig?: ISearchConfig;
  /**
   * table组件属性
   */
  tableConfig?: ITableConfig;
  /**
   * 请求接口配置
   */
  urlObj: IUrlObj;
}
