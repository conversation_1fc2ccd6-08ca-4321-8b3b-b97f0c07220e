import RouterBar from '@/layouts/header/RouterBar';
import { Divider, Spin } from 'antd';
import { AnimatePresence, motion } from 'framer-motion';
import { useLocation } from 'ice';
import React, { Suspense } from 'react';
import { FormDrawer } from '../form';
import FormCard from '../form/FormCard';
import styles from './templates.module.css';
import { ILayoutTemplateProps } from './types/ILayoutTemplate';

const LayoutTemplate: React.FC<ILayoutTemplateProps> = ({
  searchChildren,
  tableChildren,
  formChildren,
  type = '',
  intlPrefix,
  shouldScroll = true,
  cardTitle = '',
  isShowRouterBar = true,
  cardExtra = <></>,
  formExtra,
  drawerOpen,
  handleClose,
  handleSubmit,
}) => {
  const location = useLocation();

  return (
    <AnimatePresence>
      <motion.div
        key={location.pathname}
        layoutScroll
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.8, ease: 'easeInOut' }}
        className="flex-col flex-1 height100"
      >
        <>
          <div className="app-block m-b">
            {isShowRouterBar && (
              <>
                <RouterBar />
                <Divider className="m-tb-0" />
              </>
            )}
            {searchChildren}
          </div>
          <Suspense fallback={<Spin size="large" />}>
            <FormCard
              title={cardTitle}
              type={type}
              extra={cardExtra}
              intlPrefix={intlPrefix}
              shouldScroll={shouldScroll}
              className={styles.layoutContent}
            >
              {tableChildren}
            </FormCard>
            <FormDrawer
              open={drawerOpen ?? false}
              title={cardTitle}
              type={type}
              extra={formExtra}
              onClose={handleClose}
              onSubmit={handleSubmit}
              intlPrefix={intlPrefix}
            >
              {formChildren}
            </FormDrawer>
          </Suspense>
        </>
      </motion.div>
    </AnimatePresence>
  );
};

export default LayoutTemplate;
