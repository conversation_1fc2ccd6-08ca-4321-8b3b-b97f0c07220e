import { I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import { getActionColumnWidth, renderColumnByType, showTotal } from '@/utils/comUtil';
import { Table } from 'antd';
import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import styles from './table.module.css';
import { ICommonTableProps } from './type/ICommonTable';

// 默认分页数据
const defaultPagination = { currentPage: 1, defaultPageSize: 50, pageSize: 50 };

// 提取 optionColumn 生成逻辑
const getOptionColumn = (
  optionList: any[],
  translate: (prefix: string, key: string) => string,
  renderActionColumn: any,
  onAction: (type: string, row: any, optionList: any[]) => void,
) => {
  return optionList.length
    ? [
        {
          title: translate(I18N_COMON_PAGENAME.COMMON, 'option'),
          dataIndex: 'option',
          key: 'option',
          align: 'center',
          fixed: 'right',
          width: getActionColumnWidth(optionList.length),
          render: (_: any, row: any) =>
            renderActionColumn(optionList, (type: string) => onAction(type, row, optionList)),
        },
      ]
    : [];
};

const CommonTable: FC<ICommonTableProps> = ({
  rowKey = 'id',
  columns = [],
  optionList = [],
  paginationConfig,
  dataSource = [],
  loading = false,
  intlPrefix,
  components,
  props = {},
  onAction,
  onChange,
}) => {
  const { translate, renderActionColumn } = useIntlCustom();
  const [tableData, setTableData] = useState<any[]>([]);

  // 保证 onAction、onChange 有默认值且稳定
  const handleAction = useCallback(onAction || (() => {}), [onAction]);
  const handleChange = useCallback(onChange || (() => {}), [onChange]);

  useEffect(() => {
    Array.isArray(dataSource) ? setTableData(dataSource) : setTableData([]);
  }, [dataSource]);

  // 处理数据
  const setColumns = useCallback(
    (columns: ICommonTableProps['columns']): any[] => {
      return columns.map((column: any) => {
        column = { width: 120, align: 'center', ellipsis: true, ...column };
        let { title, key, prefix, children } = column;
        column.prefix = prefix || intlPrefix;
        title = translate(column.prefix, title);
        // 根据valueType格式化数据
        const tempColumn = renderColumnByType(column);
        delete tempColumn.valueType;
        if (Array.isArray(children)) {
          tempColumn.children = setColumns(children);
        }
        return { ...tempColumn, title };
      });
    },
    [intlPrefix, translate],
  );

  // columns 计算用 useMemo 缓存
  const col = useMemo(() => {
    const result = setColumns(columns);
    const optionColumn = getOptionColumn(optionList, translate, renderActionColumn, handleAction);
    return [...result, ...optionColumn];
  }, [columns, optionList, translate, renderActionColumn, handleAction, setColumns]);

  // 分页配置合并
  const pagination = useMemo(() => {
    if (typeof paginationConfig === 'object') {
      return { ...defaultPagination, ...paginationConfig, showTotal };
    }
    return paginationConfig;
  }, [paginationConfig]);

  return (
    <>
      <Table
        className={styles.commonParamTable}
        tableLayout="fixed"
        rowKey={rowKey}
        components={components}
        dataSource={tableData}
        columns={col}
        loading={loading}
        pagination={pagination}
        scroll={{
          x: 'max-content',
          y: 2000,
        }}
        onChange={handleChange}
        {...props}
      />
    </>
  );
};

export default CommonTable;
