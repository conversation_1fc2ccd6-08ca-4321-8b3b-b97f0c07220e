import { ICommonTableActionItem } from '@/types/ICommon';

// treeTable组件入参约束
export interface ITreeTableProps {
  ref?: any;
  rowKey?: string;
  dataSource?: Array<any>;
  columns?: Array<any>;
  intlPrefix: string; // 国际化模块
  loading?: boolean;
  optionList?: Array<string | ICommonTableActionItem>; // 操作列
  onAction?: (type: string, row: object) => void;
  showCollapsed?: boolean;
  props?: object;
}
