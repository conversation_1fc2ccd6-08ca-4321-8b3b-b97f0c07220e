.paginationContent {
  justify-content: flex-end;
  padding: 0 12px 12px 0;
}
.paginationContent :global {
  .pageBtn {
    margin-right: 4px;
  }
}
.commonParamTable {
  height: calc(100% - 44px) !important;
}

.valueInputContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.valueInput {
  flex: 1;
}

.cascaderSelect {
  flex: 1;
}

.switchToggle {
  /* 开关样式 */
}

.paramSection {
  margin-bottom: 32px;
}

.paramSectionTitle {
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 12px;
}

.paramSectionTitleLast {
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 12px;
}

.typeSelect {
  width: 80px;
}
