// 可编辑表格组件
import { FORM_ERROR_CODE, I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import { renderButton } from '@/utils/comUtil';
import type { ActionType, EditableFormInstance } from '@ant-design/pro-components';
import { EditableProTable } from '@ant-design/pro-components';
import { Form } from 'antd';
import { FC, forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import useEditTable from './hooks/useEditTable';
import { IEditTableProps } from './type/IEditTable';

const EditTable: FC<IEditTableProps> = forwardRef(
  (
    {
      rowKey,
      columns,
      dataSource,
      intlPrefix,
      canEdit,
      editableType = 'single',
      showCreate = true,
      optionCount = 0,
      loading = false,
      orderFiled,
      editTableType,
      getOptionList = () => [],
      afterMount = () => {},
      onCreate = (index) => ({ id: index + 1 }),
      onAction = (optionType, row) => {},
      onFormChange = (key, row, originRow) => {},
      onFormSave = (key, row, originRow) => {},
    },
    ref = null,
  ) => {
    // hooks变量
    const { translate } = useIntlCustom();
    const [editableKeys, setEditableKeys] = useState<string[]>([]);
    const [tableData, setTableData] = useState<Array<any>>([]);
    const editableFormRef = useRef<EditableFormInstance>();
    const actionRef = useRef<ActionType>(null);
    const [form] = Form.useForm();
    const { getColumns } = useEditTable({
      intlPrefix,
      rowKey,
      optionCount,
      orderFiled,
      editableKeys,
      tableData,
      setTableData,
      getOptionList,
      onAction,
    });
    // 副作用
    // 暴露给父组件调用的方法
    useImperativeHandle(ref, () => {
      return {
        // 校验表单
        async validateFields() {
          try {
            // 判断是否正在编辑
            if (editableType === 'single' && editableKeys.length) {
              return { errorCode: FORM_ERROR_CODE.EDITING };
            }
            await editableFormRef?.current?.validateFields();
            return tableData;
          } catch (error) {
            return null;
          }
        },
        // 重置
        resetFields() {
          if (Array.isArray(editableKeys)) {
            setEditableKeys([]);
            form.resetFields();
          }
          setTableData(dataSource);
        },
        // 获取当前表格数据
        getFieldsValue() {
          return editableFormRef?.current?.getFieldsValue();
        },
        // 手动设置行数据
        setRowData(rowIndex, data) {
          editableFormRef.current?.setRowData?.(rowIndex, { ...data });
        },
        // 获取行数据
        getRowData(rowIndex: number) {
          return editableFormRef.current?.getRowData && editableFormRef.current?.getRowData(rowIndex);
        },
        // 手动设置可编辑的行
        setCustomEditableKeys(editTableKeys) {
          setEditableKeys(editTableKeys);
        },
        // 获取正在编辑的行
        getEditableKeys() {
          return editableKeys;
        },
      };
    });
    useEffect(() => {
      setTableData(Array.isArray(dataSource) ? dataSource : []);
    }, [dataSource]);
    useEffect(() => {
      afterMount(form);
    }, []);

    const props: any = {
      rowKey,
      loading,
      // 列表配置
      columns: getColumns(columns),
      // 数据源
      value: tableData,
      // 新增按钮配置
      recordCreatorProps:
        canEdit && showCreate
          ? {
              position: 'bottom',
              // 每次新增的时候需要rowKey
              record: onCreate,
              creatorButtonText: translate(I18N_COMON_PAGENAME.COMMON, 'create'),
              // style: createButtonStyle,
            }
          : false,
      // 可编辑表格实例
      editableFormRef,
      // action的实例
      actionRef,
      // 可编辑表格配置
      editable: {
        form,
        // 单行编辑还是多行编辑
        type: editableType,
        // 编辑中的行keys
        editableKeys,
        onChange: setEditableKeys,
        onSave: (key, row, originRow) => onFormSave(key, row, originRow),
        // 默认操作列的配置
        actionRender: (row, config, defaultDom) => [defaultDom.save, defaultDom.cancel],
        saveText: renderButton({ type: OPERATE_TYPE.save }),
        cancelText: renderButton({ type: OPERATE_TYPE.cancel }),
      },
      onChange: setTableData,
    };
    if (editTableType === 'caseDemo') delete props.onChange;
    return <EditableProTable {...props} />;
  },
);

export default EditTable;
