import { ISearchSource } from '@/types/ICommon';
import React from 'react';

// searchSource约束
// 搜索组件入参约束
export interface ISearchProps {
  children?: React.ReactNode;
  searchTitle?: string;
  resetTitle?: string;
  createTitle?: string;
  searchValue: object;
  resetValue: object;
  searchSource: Array<ISearchSource>;
  intlPrefix?: string;
  buttonDisabled?: boolean;
  onSearch: (obj: object) => void;
  onCreate?: () => void;
  onChange?: (changedValues: object, allValues: object) => void;
  onRest?: (searchSource?: object) => void;
}
