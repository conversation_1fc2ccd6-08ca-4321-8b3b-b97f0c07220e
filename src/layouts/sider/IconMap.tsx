/**
 * 菜单图标：可使用svg多样化扩展
 * Created by ca<PERSON><PERSON> on 2025/6/6.
 */

import {
  DesktopOutlined,
  FundOutlined,
  TrophyOutlined,
  TeamOutlined,
  TransactionOutlined,
  AreaChartOutlined,
  Pie<PERSON>hartOutlined,
  DatabaseOutlined,
  SettingOutlined,
  GoldOutlined,
  ReadOutlined,
} from '@ant-design/icons';

export default {
  DesktopOutlined: <DesktopOutlined />,
  FundOutlined: <FundOutlined />,
  TrophyOutlined: <TrophyOutlined />,
  TeamOutlined: <TeamOutlined />,
  TransactionOutlined: <TransactionOutlined />,
  AreaChartOutlined: <AreaChartOutlined />,
  PieChartOutlined: <PieChartOutlined />,
  DatabaseOutlined: <DatabaseOutlined />,
  SettingOutlined: <SettingOutlined />,
  GoldOutlined: <GoldOutlined />,
  ReadOutlined: <ReadOutlined />,
};
