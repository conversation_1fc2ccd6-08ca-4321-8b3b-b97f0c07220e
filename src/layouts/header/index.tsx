/**
 * Created by ca<PERSON><PERSON> on 2025/6/6.
 */

import { Layout } from 'antd';
import styles from '../index.module.css';
import LanguageBar from './LanguageBar';
import LogoBar from './LogoBar';
import ThemeBar from './ThemeBar';
import Topbar from './Topbar';
import UserBar from './UserBar';

export default function Header({ onSelect = (codeIndex: string): {} => ({}) }) {
  return (
    <Layout.Header className={styles.header}>
      <LogoBar />
      <Topbar onSelect={onSelect} />
      <div className={`${styles.rightbar} m-r-s`}>
        {/* <HelpBar /> */}
        <LanguageBar />
        <ThemeBar />
        <UserBar />
      </div>
    </Layout.Header>
  );
}
