/**
 * 用户信息组件
 */

import avatar from '@/assets/images/avatar.png';
import { I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import styleConstant from '@/constants/styleConstant';
import useIntlCustom, { intlConst } from '@/hooks/useIntlCustom';
import store from '@/store';
import { ImportOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Popover, Select } from 'antd';
import { FC, useState } from 'react';
import styles from '../index.module.css';

const UserBar: FC = () => {
  const { translate } = useIntlCustom();
  const [userState] = store.useModel('user');
  const userInfo = userState.currentUser;
  // 角色和岗位 options 及 state
  const roleOptions = [
    { value: 'role1', label: '角色1' },
    { value: 'role2', label: '角色2' },
  ];
  const positionOptions = [
    { value: 'position1', label: '岗位1' },
    { value: 'position2', label: '岗位2' },
  ];
  const [selectedRole, setSelectedRole] = useState(roleOptions[0].value);
  const [selectedPosition, setSelectedPosition] = useState(positionOptions[0].value);

  const getContent = () => {
    const {
      userName = intlConst.formatMessage('callParam', 'defaultUserName'),
      empName = intlConst.formatMessage('callParam', 'defaultEmpName'),
      branchName = intlConst.formatMessage('callParam', 'defaultBranchName'),
    } = userInfo;

    return (
      <div style={{ width: 250 }}>
        <div className={styles.popUser}>
          <span style={styleConstant.mr}>
            {translate(I18N_COMON_PAGENAME.COMMON, 'userName')}：{userName}
          </span>
        </div>
        <div className={styles.popUser}>
          {translate(I18N_COMON_PAGENAME.COMMON, 'branchName')}：{branchName}
        </div>
        <div className={styles.popUser}>
          {translate(I18N_COMON_PAGENAME.COMMON, 'empName')}：{empName}
        </div>
        <div className={styles.popUser} style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
          <span style={{ fontWeight: 500, minWidth: 48, color: '#333' }}>
            {translate(I18N_COMON_PAGENAME.LOGIN, 'role')}：
          </span>
          <Select style={{ flex: 1 }} value={selectedRole} onChange={setSelectedRole} options={roleOptions} />
        </div>
        <div className={styles.popUser} style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
          <span style={{ fontWeight: 500, minWidth: 48, color: '#333' }}>
            {translate(I18N_COMON_PAGENAME.LOGIN, 'station')}：
          </span>
          <Select
            style={{ flex: 1 }}
            value={selectedPosition}
            onChange={setSelectedPosition}
            options={positionOptions}
          />
        </div>
        <div className={styles.popLogout}>
          <Button type="dashed" danger icon={<ImportOutlined />} onClick={handleExit}>
            {translate(I18N_COMON_PAGENAME.COMMON, 'logout')}
          </Button>
        </div>
      </div>
    );
  };
  const handleExit = async () => {
    sessionStorage.clear();
    window.location.href = '/$';
  };
  return (
    <div className={styles.user}>
      <Popover placement="bottomRight" content={getContent()}>
        <Avatar src={avatar} size={36} icon={<UserOutlined />} className={styles.avatar} />
      </Popover>
    </div>
  );
};

export default UserBar;
