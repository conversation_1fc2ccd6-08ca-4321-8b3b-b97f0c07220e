export default {
  header: {
    requestId: '99999999',
    gid: '888888',
    errorCode: '000000',
    errorMsg: 'SUCCESS',
    success: true,
  },
  data: [
    // 工作台
    {
      menuId: 1,
      parentMenuId: 0,
      menuName: '工作台',
      menuType: '0',
      iconId: 'TeamOutlined',
      orderSeq: '1',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'workBench',
    },
    {
      menuId: 1001,
      parentMenuId: 1,
      menuName: '管理员工作台',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '1',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'workBench-admin',
      menuRouteUrl: '/workBench/messageBench',
      // 这里可加 menuRouteUrl
    },
    {
      menuId: 1002,
      parentMenuId: 1,
      menuName: '催收员工作台',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '2',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'workBench-collector',
      menuRouteUrl: '/workBench/collectorBench',
      // 这里可加 menuRouteUrl
    },
    // {
    //   menuId: 1003,
    //   parentMenuId: 1,
    //   menuName: '总览工作台',
    //   menuType: '0',
    //   iconId: 'SettingOutlined',
    //   orderSeq: '2',
    //   menuStatus: 'Y',
    //   createTime: '2024-6-20 12:12:12',
    //   updateTime: '2024-6-21 13:13:13',
    //   createUser: 'testUser1',
    //   updateUser: 'testUser2',
    //   permissionId: 'workBench-collector',
    //   menuRouteUrl: '/workBench/workBench',
    //   // 这里可加 menuRouteUrl
    // },
    // 案件管理
    {
      menuId: 2,
      parentMenuId: 0,
      menuName: '案件管理',
      menuType: '0',
      iconId: 'TeamOutlined',
      orderSeq: '2',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'caseManage',
    },
    {
      menuId: 2001,
      parentMenuId: 2,
      menuName: '案件查询',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '1',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'caseManage-query',
      menuRouteUrl: '/caseManage/caseQuery',
    },
    {
      menuId: 2002,
      parentMenuId: 2,
      menuName: '案件列表',
      menuType: '0',
      menuRouteUrl: '/caseManage/caseList',
      iconId: 'SettingOutlined',
      orderSeq: '2',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'caseManage-list',
    },
    {
      menuId: 2003,
      parentMenuId: 2,
      menuName: '核心查询',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '3',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'caseManage-core',
    },
    {
      menuId: 2004,
      parentMenuId: 2,
      menuName: '案件审批',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '4',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'caseManage-approve',
      menuRouteUrl: '/systemManage/aduitManage',
    },
    // 策略管理
    {
      menuId: 3,
      parentMenuId: 0,
      menuName: '策略管理',
      menuType: '0',
      iconId: 'TeamOutlined',
      orderSeq: '3',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'strategyManage',
    },
    {
      menuId: 3001,
      parentMenuId: 3,
      menuName: '派案策略管理',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '1',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'strategyManage-dispatch',
      menuRouteUrl: '/strategyManage/strategySetting',
    },
    {
      menuId: 3002,
      parentMenuId: 3,
      menuName: '权重策略管理',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '3',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'strategyManage-weight',
      menuRouteUrl: '/strategyManage/weightSetting',
    },
    {
      menuId: 3003,
      parentMenuId: 3,
      menuName: '排序模板管理',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '2',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'strategyManage-sort',
      menuRouteUrl: '/strategyManage/sortSetting',
    },
    {
      menuId: 3004,
      parentMenuId: 3,
      menuName: '工作台管理',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '2',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'strategyManage-sort',
      menuRouteUrl: '/strategyManage/workbenck',
    },
    // 流程管理
    {
      menuId: 4,
      parentMenuId: 0,
      menuName: '流程管理',
      menuType: '0',
      iconId: 'TeamOutlined',
      orderSeq: '4',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'flowManage',
    },
    {
      menuId: 4001,
      parentMenuId: 4,
      menuName: '节点管理',
      menuType: '0',
      menuRouteUrl: '/flowManage/nodeConfig',
      iconId: 'SettingOutlined',
      orderSeq: '1',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'flowManage-node',
    },
    {
      menuId: 4002,
      parentMenuId: 4,
      menuName: '处理程序管理',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '2',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'flowManage-process',
      menuRouteUrl: '/flowManage/program',
    },
    {
      menuId: 4003,
      parentMenuId: 4,
      menuName: '标签管理',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '3',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'flowManage-label',
      menuRouteUrl: '/flowManage/labelManage',
    },
    {
      menuId: 4004,
      parentMenuId: 4,
      menuName: '规则管理',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '4',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'flowManage-rule',
    },
    {
      menuId: 400401,
      parentMenuId: 4004,
      menuName: '规则因子',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '1',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'flowManage-rule-factor',
      menuRouteUrl: '/operationCenter/rules/ruleFactor',
    },
    {
      menuId: 400402,
      parentMenuId: 4004,
      menuName: '规则管理',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '2',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'flowManage-rule-manage',
      menuRouteUrl: '/operationCenter/rules/rulesConfig',
    },
    // 运营管理
    {
      menuId: 5,
      parentMenuId: 0,
      menuName: '运营管理',
      menuType: '0',
      iconId: 'TeamOutlined',
      orderSeq: '5',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'operationManage',
    },
    {
      menuId: 5001,
      parentMenuId: 5,
      menuName: 'XXX监控',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '1',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'operationManage-xxx',
    },
    // 数据管理
    {
      menuId: 6,
      parentMenuId: 0,
      menuName: '数据管理',
      menuType: '0',
      iconId: 'TeamOutlined',
      orderSeq: '6',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'dataManage',
      menuRouteUrl: '/dataManage/realtime',
    },
    {
      menuId: 6001,
      parentMenuId: 6,
      menuName: '实时数据流',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '1',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'dataManage-realtime',
      menuRouteUrl: '/dataManage/realtime',
    },
    {
      menuId: 6002,
      parentMenuId: 6,
      menuName: '离线数据管理',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '2',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'dataManage-offline',
      menuRouteUrl: '/dataManage/offline',
    },
    {
      menuId: 6003,
      parentMenuId: 6,
      menuName: '名单管理',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '3',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'dataManage-list',
      menuRouteUrl: '/dataManage/callList',
    },
    {
      menuId: 6004,
      parentMenuId: 6,
      menuName: '短信模版管理',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '4',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'dataManage-sms',
      menuRouteUrl: '/dataManage/smsTemplate',
    },
    // 分析中心
    {
      menuId: 7,
      parentMenuId: 0,
      menuName: '分析中心',
      menuType: '0',
      iconId: 'TeamOutlined',
      orderSeq: '7',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'analysisCenter',
    },
    {
      menuId: 7001,
      parentMenuId: 7,
      menuName: '客户洞察',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '1',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'analysisCenter-customer',
    },
    // 系统管理
    {
      menuId: 8,
      parentMenuId: 0,
      menuName: '系统管理',
      menuType: '0',
      iconId: 'TeamOutlined',
      orderSeq: '8',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'systemManage',
    },
    {
      menuId: 8001,
      parentMenuId: 8,
      menuName: '菜单管理',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '1',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'systemManage-menu',
      menuRouteUrl: '/systemManage/menu',
    },
    {
      menuId: 8002,
      parentMenuId: 8,
      menuName: '数据字典',
      menuType: '0',
      iconId: 'SettingOutlined',
      orderSeq: '2',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'systemManage-dict',
      menuRouteUrl: '/systemManage/dict',
    },
    {
      menuId: 8003,
      parentMenuId: 8,
      menuName: '机构管理',
      menuType: '0',
      menuRouteUrl: '/systemManage/org',
      iconId: 'SettingOutlined',
      orderSeq: '3',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'systemManage-org',
    },
    {
      menuId: 8004,
      parentMenuId: 8,
      menuName: '岗位管理',
      menuType: '0',
      menuRouteUrl: '/systemManage/position',
      iconId: 'SettingOutlined',
      orderSeq: '4',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'systemManage-position',
    },
    {
      menuId: 8005,
      parentMenuId: 8,
      menuName: '角色管理',
      menuType: '0',
      menuRouteUrl: '/systemManage/role',
      iconId: 'SettingOutlined',
      orderSeq: '5',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'systemManage-role',
    },
    {
      menuId: 8006,
      parentMenuId: 8,
      menuName: '用户管理',
      menuType: '0',
      menuRouteUrl: '/systemManage/user',
      iconId: 'SettingOutlined',
      orderSeq: '6',
      menuStatus: 'Y',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      createUser: 'testUser1',
      updateUser: 'testUser2',
      permissionId: 'systemManage-user',
    },
  ],
};
