/**
 * 接口常量
 */

const URL_CONSTANST = {
  // 机构号
  BASE_ORG_PARAM: {
    LIST: 'param/baseOrg/query',
  },
  // 规则因子管理
  RULE_FACTOR: {
    LIST: 'rule/variable/queryRuleVariable',
    EDIT: 'rule/variable/update',
    CREATE: 'rule/variable/save',
  },
  // 规则-额度
  RULES_LIMIT: {
    LIST: 'param/rules/limit/query',
    EDIT: 'param/rules/limit/update',
    CREATE: 'param/rules/limit/create',
  },
  // 规则-授权
  RULES_AUTH: {
    LIST: 'param/rules/pageQuery',
    EDIT: 'param/rules/update',
    CREATE: 'param/rules/create',
  },
  // 规则因子
  RULES_FACTOR: {
    LIST_BY_RULE_TYPE: 'rule/variable/queryByRuleType',
  },
  // 客群管理
  CUTTOMER_MANAGE: {
    LIST: 'param/dimensionalManage/query',
    EDIT: 'param/dimensionalManage/update',
    CREATE: 'param/dimensionalManage/create',
  },
  // 标签管理
  LABEL_MANAGE: {
    LIST: 'api/tags/query',
    EDIT: 'api/tags/update',
    CREATE: 'api/tags/add',
    DELETE: 'api/tags/delete',
  },
  // 节点管理
  NODE_MANAGE: {
    LIST: 'param/nodeManage/pageQuery',
    EDIT: 'param/nodeManage/update',
    CREATE: 'param/nodeManage/create',
  },
  // 节点程序
  NODE_FOW_MANAGE: {
    LIST: 'param/nodeFlowManage/query',
    EDIT: 'param/nodeFlowManage/update',
    CREATE: 'param/nodeFlowManage/create',
  },
  // 处理程序管理
  PROGRAM_MANAGE: {
    LIST: 'api/processingProgram/query',
    EDIT: 'api/processingProgram/update',
    CREATE: 'api/processingProgram/add',
    DELETE: 'api/processingProgram/delete',
  },
  // 案件基础信息
  CASE_BASE_INFO: {
    LIST: 'api/wcsCasState/query',
    EDIT: 'api/wcsCasState/update',
    FLOW_NODE: 'api/taskNodeInfo/query',
    ACTION_DETAIL: 'api/taskNodeActionInfo/query',
  },
  // 审批管理
  ADUIT_MANAGE: {
    LIST: 'param/aduitManage/query',
    EDIT: 'param/aduitManage/update',
    CREATE: 'param/aduitManage/create',
  },
  // 审批管理
  WORKBENCH_CONFIG: {
    LIST: 'api/workBenchConfig/query',
    EDIT: 'api/workBenchConfig/edit',
    CREATE: 'api/workBenchConfig/add',
  },
  // 卡户人查询-卡片查询
  CARD_QUERY: {
    LIST: 'service/cardQuery/query',
    DETAIL: 'service/cardQuery/queryDetailInfo',
  },
  // 岗位管理
  STATION_MANAGEMENT: {
    LIST: 'param/stationManagement/query',
    EDIT: 'param/stationManagement/update',
    CREATE: 'param/stationManagement/create',
    DELETE: 'param/stationManagement/delete',
  },
  // 人员管理
  STAFF_MANAGEMENT: {
    LIST: 'param/staffManagement/query',
    EDIT: 'param/staffManagement/update',
    CREATE: 'param/staffManagement/create',
    DELETE: 'param/staffManagement/delete',
  },
  // 用户管理
  USER_MANAGEMENT: {
    LIST: 'param/staffManagement/query',
    EDIT: 'param/staffManagement/update',
    CREATE: 'param/staffManagement/create',
    DELETE: 'param/staffManagement/delete',
  },
  // 岗位管理
  POSITION_MANAGEMENT: {
    LIST: 'param/stationManagement/query',
    EDIT: 'param/stationManagement/update',
    CREATE: 'param/stationManagement/create',
    DELETE: 'param/stationManagement/delete',
  },
};

export default URL_CONSTANST;
