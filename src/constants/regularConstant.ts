/**
 * 校验规则常量
 */

const locale: any = localStorage.getItem('locale');
const message: any = {
  'zh-tw': {
    PHONE: '請輸入正確手機號碼',
    TELEPHONE: '請輸入正確電話號碼',
    IDENTITY: '請輸入正確證件號碼',
    EMAIL: '請輸入正確郵箱格式',
    EN: '請輸入英文字母',
    EN_NUM: '請輸入英文字母或數字',
    ZN_ZH_NUM: '請輸入英文、中文或數字',
    NUM_INT: '請輸入正整數',
    NUM_DECIMAL2: '請輸入正確數值，最多保留兩位小數',
  },
  'zh-cn': {
    PHONE: '请输入正确手机号码',
    TELEPHONE: '请输入正确电话号码',
    IDENTITY: '请输入正确证件号码',
    EMAIL: '请输入正确邮箱格式',
    EN: '请输入英文字母',
    EN_NUM: '请输入英文字母或数字',
    ZN_ZH_NUM: '请输入英文、中文或数字',
    NUM_INT: '请输入正整数',
    NUM_DECIMAL2: '请输入正确数值，最多保留两位小数',
  },
  en: {
    PHONE: 'Please enter the correct mobile phone number',
    TELEPHONE: 'Please enter the correct phone number',
    IDENTITY: 'Please enter the correct ID number',
    EMAIL: 'Please enter the correct email format',
    EN: 'Please enter the English alphabet',
    EN_NUM: 'Please enter English letters or numbers',
    ZN_EN_NUM: 'Please enter English, Chinese or numbers',
    NUM_INT: 'Please enter a positive integer',
    NUM_DECIMAL2: 'Please enter the correct value and keep up to two decimal places',
  },
};

const PATTERN_RULES = {
  // 个人信息
  PHONE: {
    pattern: /^1(3|4|5|6|7|8|9)\d{9}$/,
    message: message[locale]?.PHONE,
  },
  TELEPHONE: {
    pattern: /^(\(\d{3,4}\)|\d{3,4}-|\s)\d{7,14}$/,
    message: message[locale]?.TELEPHONE,
  },
  IDENTITY: {
    pattern: /^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{4}$/,
    message: message[locale]?.IDENTITY,
  },
  EMAIL: {
    pattern: /^[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*\.[a-z]{2,}$/,
    message: message[locale]?.EMAIL,
  },
  // 字符串
  EN: {
    pattern: /^[a-zA-Z]*$/,
    message: message[locale]?.EN,
  },
  EN_NUM: {
    pattern: /^[a-zA-Z0-9]*$/,
    // message: '请输入英文字母或数字',
    message: message[locale]?.EN_NUM,
  },
  ZN_ZH_NUM: {
    pattern: /^[(\u4E00-\u9FA5|a-zA-Z0-9)]+$/,
    message: message[locale]?.ZN_ZH_NUM,
  },
  NUM_INT: {
    pattern: /^[1-9][0-9]*$/,
    message: message[locale]?.NUM_INT,
  },
  NUM_DECIMAL2: {
    pattern: /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/,
    message: message[locale]?.NUM_DECIMAL2,
  },
};
export default PATTERN_RULES;
