export default {
  header: {
    requestId: '99999999',
    gid: '888888',
    errorCode: '000000',
    errorMsg: 'SUCCESS',
    success: true,
  },
  data: {
    'zh-cn': [
      {
        menuId: 1,
        parentMenuId: 0,
        menuName: '用户中心',
        menuType: '0',
        iconId: 'TeamOutlined',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter',
      },
      {
        menuId: 1001,
        parentMenuId: 1,
        menuName: '工作台',
        menuType: '0',
        iconId: 'SettingOutlined',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 10011002,
        parentMenuId: 1001,
        menuName: '案件监控台',
        menuType: '0',
        menuRouteUrl: '/userCenter/workBench/workBench',
        iconId: 'SettingOutlined',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 10011003,
        parentMenuId: 1001,
        menuName: '电话催收工作台',
        menuType: '0',
        menuRouteUrl: '/userCenter/workBench/phoneBench',
        iconId: 'SettingOutlined',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 10011004,
        parentMenuId: 1001,
        menuName: '短信催收工作台',
        menuType: '0',
        menuRouteUrl: '/userCenter/workBench/messageBench',
        iconId: 'SettingOutlined',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 10011009,
        parentMenuId: 1001,
        menuName: '审批管理',
        menuType: '0',
        menuRouteUrl: '/userCenter/systemManage/aduitManage',
        iconId: 'SettingOutlined',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 1002,
        parentMenuId: 1,
        menuName: '系统管理',
        menuType: '0',
        iconId: 'SettingOutlined',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 10021001,
        parentMenuId: 1002,
        menuName: '用户管理',
        menuType: '0',
        menuRouteUrl: '/userCenter/systemManage/user',
        iconId: 'SettingOutlined',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 10021002,
        parentMenuId: 1002,
        menuName: '角色管理',
        menuType: '0',
        menuRouteUrl: '/userCenter/systemManage/role',
        iconId: 'SettingOutlined',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 10021003,
        parentMenuId: 1002,
        menuName: '岗位管理',
        menuType: '0',
        menuRouteUrl: '/userCenter/systemManage/position',
        iconId: 'SettingOutlined',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 10021004,
        parentMenuId: 1002,
        menuName: '机构管理',
        menuType: '0',
        menuRouteUrl: '/userCenter/systemManage/org',
        iconId: 'SettingOutlined',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 2,
        parentMenuId: 0,
        menuName: '运营中心',
        menuType: '0',
        iconId: 'TeamOutlined',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter',
      },
      {
        menuId: 2000,
        parentMenuId: 2,
        menuName: '案件列表',
        menuType: '0',
        menuRouteUrl: '/operationCenter/flowManage/caseManage',
        iconId: 'TeamOutlined',
        level: 4,
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param-callParam-authR',
      },
      {
        menuId: 2001,
        parentMenuId: 2,
        menuName: '规则管理',
        menuType: '0',
        iconId: 'SettingOutlined',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 2001001,
        parentMenuId: 2001,
        menuName: '规则因子管理',
        menuType: '0',
        menuRouteUrl: '/operationCenter/rules/ruleFactor',
        iconId: 'SettingOutlined',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules-ruleF',
      },
      {
        menuId: 2001002,
        parentMenuId: 2001,
        menuName: '规则配置',
        menuType: '0',
        menuRouteUrl: '/operationCenter/rules/rules/auth',
        iconId: 'TeamOutlined',
        level: 4,
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules-rules-limit',
      },
      {
        menuId: 2002,
        parentMenuId: 2,
        menuName: '流程管理',
        menuType: '0',
        iconId: 'SettingOutlined',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param',
      },
      {
        menuId: 2002002,
        parentMenuId: 2002,
        menuName: '节点管理',
        menuType: '0',
        menuRouteUrl: '/operationCenter/flowManage/nodeConfig',
        iconId: 'TeamOutlined',
        level: 4,
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param-callParam-authR',
      },
      {
        menuId: 2002003,
        parentMenuId: 2002,
        menuName: '标签管理',
        menuType: '0',
        menuRouteUrl: '/operationCenter/flowManage/labelManage',
        iconId: 'TeamOutlined',
        level: 4,
        orderSeq: '3',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param-callParam-authR',
      },
      {
        menuId: 2002004,
        parentMenuId: 2002,
        menuName: '节点流程',
        menuType: '0',
        menuRouteUrl: '/operationCenter/flowManage/dispatchManage',
        iconId: 'TeamOutlined',
        level: 4,
        orderSeq: '4',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param-callParam-authR',
      },
      {
        menuId: 2002005,
        parentMenuId: 2002,
        menuName: '处理程序管理',
        menuType: '0',
        menuRouteUrl: '/operationCenter/flowManage/program',
        iconId: 'TeamOutlined',
        level: 4,
        orderSeq: '4',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param-callParam-authR',
      },
      {
        menuId: 2003,
        parentMenuId: 2,
        menuName: '案件管理',
        menuType: '0',
        iconId: 'SettingOutlined',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param',
      },
      {
        menuId: 2003002,
        parentMenuId: 2003,
        menuName: '岗位管理',
        menuType: '0',
        menuRouteUrl: '/operationCenter/strategyManage/stationManagement',
        iconId: 'TeamOutlined',
        level: 4,
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param-callParam-authR',
      },
      {
        menuId: 2003003,
        parentMenuId: 2003,
        menuName: '人员管理',
        menuType: '0',
        menuRouteUrl: '/operationCenter/strategyManage/staffManagement',
        iconId: 'TeamOutlined',
        level: 4,
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param-callParam-authR',
      },
      {
        menuId: 2003004,
        parentMenuId: 2003,
        menuName: '策略设置',
        menuType: '0',
        menuRouteUrl: '/operationCenter/strategyManage/strategySetting',
        iconId: 'SettingOutlined',
        level: 4,
        orderSeq: '3',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param-callParam-authR',
      },
      {
        menuId: 2003005,
        parentMenuId: 2003,
        menuName: '权重设置',
        menuType: '0',
        menuRouteUrl: '/operationCenter/strategyManage/weightSetting',
        iconId: 'SettingOutlined',
        level: 4,
        orderSeq: '4',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param-callParam-authR',
      },
      {
        menuId: 2003006,
        parentMenuId: 2003,
        menuName: '手工派案',
        menuType: '0',
        menuRouteUrl: '/operationCenter/strategyManage/manualDispatch',
        iconId: 'SettingOutlined',
        level: 4,
        orderSeq: '5',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param-callParam-authR',
      },
      {
        menuId: 2003007,
        parentMenuId: 2003,
        menuName: '手工转案',
        menuType: '0',
        menuRouteUrl: '/operationCenter/strategyManage/manualTransfer',
        iconId: 'SettingOutlined',
        level: 4,
        orderSeq: '6',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-param-callParam-authR',
      },
      {
        menuId: 3,
        parentMenuId: 0,
        menuName: '数据管理',
        menuType: '0',
        iconId: 'TeamOutlined',
        orderSeq: '2',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter',
      },
      {
        menuId: 3001,
        parentMenuId: 3,
        menuName: '案件数据',
        menuType: '0',
        iconId: 'SettingOutlined',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 30011001,
        parentMenuId: 3001,
        menuName: '基本信息数据',
        menuType: '0',
        menuRouteUrl: '/sourceManage/case/caseInfo',
        iconId: 'SettingOutlined',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
      {
        menuId: 30011002,
        parentMenuId: 3001,
        menuName: '账户信息数据',
        menuType: '0',
        menuRouteUrl: '/sourceManage/case/base',
        iconId: 'SettingOutlined',
        orderSeq: '1',
        menuStatus: 'Y',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        permissionId: 'operationCenter-rules',
      },
    ],
  },
};
