// 展示常量
/**
 * 默认每页条数
 */
export const PAGE_SIZE = 50;
/**
 * 分页每页条数
 */
export const PAGE_SIZE_OPTIONS: Array<number> = [10, 20, 50, 100];
/**
 * 无数据展示
 */
export const NODATA = '- -';
/**
 * 日期（年月）格式化常量
 */
export const MONTH_FORMATE = 'YYYY-MM';
/**
 * 日期格式化常量
 */
export const DATE_FORMATE = 'YYYY-MM-DD';
/**
 * 日期格式化常量
 */
export const DATE_FORMATE_SUP = 'YYYYMMDD';
/**
 * 时间格式化常量
 */
export const TIME_FORMATE = 'YYYY-MM-DD HH:mm:ss';
/**
 * 时间格式化常量（只展示时分秒）
 */
export const TIME_FORMATE_ONLY = 'HHmmss';
/**
 * 时间格式化常量（只展示时分秒）常规
 */
export const TIME_FORMATE_ONLY_ONE = 'HH:mm:ss';
/**
 * 头部和左侧菜单主题，实际被颜色主题覆盖
 */
export const MENU_THEMEN = 'dark';
/**
 * 响应成功码
 */
export const SUCCESS_CODE = '000000';
/**
 * token过期，认证失败码
 */
export const AUTH_FAILED = 'B1300002';
/**
 * 菜单类型枚举
 */
export const MENUTYPE_KEY = {
  directory: 'directory',
  link: 'link',
  blank: 'blank',
  api: 'api',
};

/**
 * 操作类型
 */
export const OPERATE_TYPE = {
  create: 'create',
  detail: 'detail',
  copy: 'copy',
  edit: 'edit',
  delete: 'delete',
  list: 'list',
  cancel: 'cancel',
  save: 'save',
  up: 'up',
  down: 'down',
  submit: 'submit',
  flowNode: 'flowNode',
  dispatch: 'dispatch',
  dowload: 'dowload',
  aiHelp: 'aiHelp',
  menuAuth: 'menuAuth',
  userDetail: 'userDetail',
  fieldManage: 'fieldManage',
};

/**
 * sessionStorage枚举
 */
export const SESSION = {
  token: 'token',
  codeIndex: 'codeIndex',
  userInfo: 'userInfo',
  menuData: 'menuData',
  menuTree: 'menuTree',
  menuSelected: 'menuSelected',
};

/**
 * localStorage枚举
 */
export const LOCAL = {
  LOCALE: 'locale',
  THEME: 'theme',
};

/**
 * 语言枚举
 */
export const LANGUAGE_LIST = {
  TW: { label: '繁體中文', locale: 'zh-tw' },
  CN: { label: '简体中文', locale: 'zh-cn' },
  EN: { label: 'English', locale: 'en' },
};

/**
 * 常用组件名称枚举
 */
export const COMPONENT_TYPE = {
  // antd 组件
  INPUT: 'Input',
  EXTARA_BTN_INPUT: 'ExtarBtnInput',
  INTERVAL_INPUT: 'IntervalInput',
  INPUT_NUMBER: 'InputNumber',
  AMOUNT_INPUT: 'AmountInput',
  CHECK_BOX: 'Checkbox',
  SELECT: 'Select',
  RADIO: 'Radio',
  TREE: 'Tree',
  TREE_SELECT: 'TreeSelect',
  CASCADER: 'Cascader',
  DATE_PICKER: 'DatePicker',
  RANGE_PICKER: 'RangePicker',
  TIME_RANGE_PICKER: 'TimeRangePicker',
  SWITCH: 'Switch',
  RATE: 'Rate',
  SLIDER: 'Slider',
  UPLOAD: 'Upload',
  // LayoutTemplate 子组件
  TABLE: 'Table',
  FORM: 'Form',
  EDITTABLE: 'EditTable',
  TEXTAREA: 'TextArea',
  DROPDOWN: 'Dropdown',
  SEARCH: 'Search',
};

/**
 * 渲染需要二次处理的类型
 */
export const RENDER_TYPE = {
  Amount: 'Amount',
  Dictionary: 'Dictionary',
  MockDictionary: 'MockDictionary',
  Ellipsis: 'ellipsis',
  DateTime: 'DateTime',
  Date: 'Date',
};

/**
 * 列表子项类型
 */
export const FORMITEM_TYPE = {
  FormHearder: 'FormHearder',
  Single: 'Single',
  Row: 'Row',
  List: 'List',
  CommonTable: 'CommonTable',
  EditTable: 'EditTable',
  Calendar: 'Calendar',
  ParamTable: 'ParamTable',
};

/**
 * 通知类型
 */
export const NOTIFICATION_TYPE = {
  SUCCESS: 'success',
  INFO: 'info',
  ERROR: 'error',
  WARNING: 'warning',
};

/**
 * 表格类型
 */
export const TABLE_TYPE = {
  CommonTable: 'CommonTable',
  TreeTable: 'TreeTable',
};

/**
 * 提交表单类型
 */
export const SUBMIT_TYPE = {
  create: 'A',
  edit: 'U',
  delete: 'D',
  copy: 'A',
};

/**
 * 提交表单类型
 */
export const NOTIFICATION_PROMPT = {
  create: 'createFailed',
  edit: 'editFailed',
  delete: 'deleteFailed',
};

/**
 * dayjs日期类型
 */
export const DATE_TYPE = {
  day: 'day',
  week: 'week',
  month: 'month',
  year: 'year',
};

/**
 * 参数接口增删改差名称（临时使用，后期后管出接口了在拿掉）
 */
export const PARAMS_URLOBJ = {
  list: 'nq/param/pageQuery',
  chekc: 'nm/param/check',
  edit: 'nm/param/maintain',
};

/**
 * 金额常量，默认13位整数，2位小数
 */
export const DEFAULT_AMOUNT_PROPS = {
  min: 0,
  max: 9999999999999.99,
  decimal: 2,
  step: 1, // 设置步长为0.1
  precision: 2,
  // stringMode: true,
};

/**
 * 请求类型
 */
export const REQUEST_TYPE = {
  mock: 'mock',
  business: 'business',
  param: 'param',
};

/**
 * EditTable可编辑表格的valueType展示类型
 */
export const EDITTABLE_VALUE_TYPE = {
  TEXT: 'text', // 文本
  DIGIT: 'digit', // 数字
  SELECT: 'select', // 选择
  TIME: 'time', // 时间
  DATE: 'date', // 日期
};

/**
 * 表单提交错误提示
 */
export const FORM_ERROR_CODE = {
  CHECK_MSG: 'checkMsg',
  EDITING: 'formEditing',
};

/**
 * 默认分页参数
 */
export const DEFAULT_PAGINATION = {
  currentPage: 1,
  pageSize: PAGE_SIZE,
};

/**
 * 页面国际化常量
 */
export const I18N_COMON_PAGENAME = {
  COMMON: 'common',
  CALL_PARAM: 'callParam',
  SYSTEM_MANAGE: 'systemManage',
  AUTH_MODULE: 'authModule',
  LIMIT: 'limit',
  LIMIT_PARAM: 'limitParam',
  RULES: 'rules',
  PUBLIC_PARAM: 'publicParam',
  CARD_PARAM: 'cardParam',
  SYSTEM: 'system',
  CARD_MODELE: 'cardModule',
  API_GATEWAY: 'apiGateway',
  LOGIN: 'login',
  STRATEGY: 'strategy',
  DATA_MANAGE: 'dataManage',
};

/**
 * 不属于组件的属性常量
 */
export const NOT_BELONG_COMPONENT = {
  TYPE: 'type',
  VALUE: 'value',
  LABEL: 'label',
  RULES: 'rules',
  DATA: 'data',
  SHOWKEY: 'showKey',
  REF: 'ref',
  BIND: 'bind',
  PREFIX: 'prefix',
  HIDE: 'hide',
  DEPENDENCIES: 'dependencies',
  DICTtYPE: 'dictType',
  FORMLAYOUTTYPE: 'formlayoutType',
  COL_SPAN: 'colSpan',
  PARSER: 'parser',
  SHOULD_UPDATE: 'shouldUpdate',
  SET_VISIBLE_FUNC: 'setVisibleFunc',
};

/**
 * 表单项渲染布局类型
 */
export const FORM_LAYOUT_TYPE = {
  ONE: 'one',
};
