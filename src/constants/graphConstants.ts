/**
 * 菜单类型
 */
export const MENU_TYPE = {
  COPY: 'COPY',
  PASTE: 'PASTE',
  EDIT: 'EDIT',
  DELETE: 'DELETE',
};

/**
 * 元素类型
 */
export const ELE_TYPE = {
  NODE: 'node',
  EDGE: 'edge',
  CANVAS: 'canvas',
};

/**
 * 节点类型（可参考G6节点类型扩展）
 */
export const NODE_TYPE = {
  // 图片
  IMAGE: 'image',
  // 圆形
  CIRCLE: 'circle',
  // 矩形
  RECT: 'rect',
  // 菱形
  DIAMOND: 'diamond',
  // 椭圆形
  ELLIPSE: 'ellipse',
  // 三角形
  TRIANGLE: 'triangle',
};

/**
 * 边类型（可参考G6线类型扩展）
 */
export const EDGE_TYPE = {
  // 折线
  POLYlINE: 'polyline',
  // 直线
  LINE: 'line',
  // 垂直三次贝塞尔曲线
  CUBIC_VERTICAL: 'cubic-vertical',
};

/**
 * 连接桩类型
 */
export const PORTS_TYPE = {
  LEFT: 'left',
  RIGHT: 'right',
  TOP: 'top',
  BOTTOM: 'bottom',
};

/**
 * 布局类型（可参考G6布局类型扩展）
 */
export const LAYOUT_TYPE = {
  // 布局
  ANTV_DARGE: 'antv-dagre',
};
