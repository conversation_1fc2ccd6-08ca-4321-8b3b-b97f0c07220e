/**
 * 样式常量
 */

const rem1 = '1rem';
const rem2 = '1.5rem';
const rem05 = '.5rem';
export default {
  // 宽高
  width100: { width: '100%' },
  width80: { width: '80%' },
  width60: { width: '60%' },
  width50: { width: '50%' },
  width40: { width: '40%' },
  width20: { width: '20%' },
  height100: { height: '100%' },
  height80: { height: '80%' },
  height60: { height: '60%' },
  height50: { height: '50%' },
  height40: { height: '40%' },
  height20: { height: '20%' },
  // 字体
  iconSize: { fontSize: 16 },
  // 间隔
  mt: { marginTop: rem1 },
  mb: { marginBottom: rem1 },
  mb2: { marginBottom: rem2 },
  ml: { marginLeft: rem1 },
  mr: { marginRight: rem1 },
  mtb: { marginTop: rem1, marginBottom: rem1 },
  mlr: { marginLeft: rem1, marginRight: rem1 },
  mts: { marginTop: rem05 },
  mbs: { marginBottom: rem05 },
  mls: { marginLeft: rem05 },
  mrs: { marginRight: rem05 },
  mtbs: { marginTop: rem05, marginBottom: rem05 },
  mlrs: { marginLeft: rem05, marginRight: rem05 },
  // 内边距
  pt: { paddingTop: rem1 },
  pb: { paddingBottom: rem1 },
  pb2: { paddingBottom: rem2 },
  pl: { paddingLeft: rem1 },
  pr: { paddingRight: rem1 },
  ptb: { paddingTop: rem1, paddingBottom: rem1 },
  plr: { paddingLeft: rem1, paddingRight: rem1 },
  pts: { paddingTop: rem05 },
  pbs: { paddingBottom: rem05 },
  pls: { paddingLeft: rem05 },
  prs: { paddingRight: rem05 },
  ptbs: { paddingTop: rem05, paddingBottom: rem05 },
  plrs: { paddingLeft: rem05, paddingRight: rem05 },
  // 颜色，和global保持一致
  grayLight: '#fafafa',
  grayOne: '#fff',
  grayTwo: '#f5f5f5',
  grayThree: '#d9d9d9',
  grayMain: '#8c8c8c',
  grayFive: '#434343',
  graySix: '#000',
  warningColor: '#faad14',
  errorColor: '#f5222d',
  successColor: '#52c41a',
  // antd表单布局
  formLayout: { labelCol: { span: 6 }, wrapperCol: { span: 12 } },
  formLayout2: { labelCol: { span: 8 }, wrapperCol: { span: 16 } },
  formLayout3: { labelCol: { span: 4 }, wrapperCol: { span: 20 } },
  formLayout4: { labelCol: { span: 1 }, wrapperCol: { span: 24 } },
};

// 主题颜色 primaryColor 应该直接获取下面的值
const greenTheme = {
  colorOne: '#00994e2b',
  colorTwo: '#00994e4d',
  colorThree: '#62c082',
  colorMain: '#00994e',
  colorFive: '#008545ec',
  colorSix: '#01723bcf',
};
const blueTheme = {
  colorOne: '#a8bee5',
  colorTwo: '#6e90cd',
  colorThree: '#5479ba',
  colorMain: '#345da7',
  colorFive: '#2b4c87',
  colorSix: '#153265',
};
const redTheme = {
  colorOne: '#fff1f0',
  colorTwo: '#ffa39e',
  colorThree: '#ff4d4f',
  colorMain: '#f5222d',
  colorFive: '#cf1322',
  colorSix: '#820014',
};
const darkTheme = {
  colorOne: '#9ca0a3',
  colorTwo: '#5c646a',
  colorThree: '#384046',
  colorMain: '#282a36',
  colorFive: '#15191e',
  colorSix: '#191e24',
};
const themeColor = {
  green: '#00994e',
  blue: '#345da7',
  // red: '#f5222d',
  // dark: '#282a36',
};
const themeMap = {
  '#00994e': greenTheme,
  '#345da7': blueTheme,
  '#f5222d': redTheme,
  '#282a36': darkTheme,
};
const themeGradientButton = {
  [themeColor.green]: ['#62c082', '#13c2c2', '#52c41a'],
  [themeColor.blue]: ['#5479ba', '#2f54eb', '#345da7'],
  // [themeColor.red]: ['#ff4d4f', '#fa541c', '#f5222d'],
  white: ['#d9d9d9', '#8c8c8c', '#434343'],
};
export { greenTheme, blueTheme, redTheme, themeColor, themeMap, themeGradientButton };
