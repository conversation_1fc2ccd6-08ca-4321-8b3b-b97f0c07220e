import { useEffect } from 'react';
import _ from 'lodash';
import store from '@/store';

/**
 * 判断store里是否有对应字典或参数表值，无则查询并放入store
 * @param dictEnum 参数，字典字段及配置
 */
const useDictStore = (dictEnum) => {
  const [dictState, dictDispatchers] = store.useModel('dict');
  useEffect(() => {
    getDictList();
  }, []);

  // 获取字典/参数枚举
  const getDictList = async () => {
    for (const key in dictEnum) {
      const keyList = key.split(',');
      let firstKey = '';
      // 用逗号分隔key的表示几个下拉框都用同一个字典表的，调一次接口
      keyList.forEach(async (item, index) => {
        if (_.isNil(dictState.dictMap[item])) {
          if (index === 0) {
            await dictDispatchers.getDictList({ key: item, ...dictEnum[key] });
            firstKey = item;
          } else {
            const res = dictState.dictMap[firstKey];
            dictDispatchers.updateDictMap(res);
          }
        }
      });
    }
  };
};

export default useDictStore;
