/**
 * @description 页面按钮权限 hooks
 * */
import store from '@/store';

const useAuthButton = () => {
  const [userState] = store.useModel('user');
  // 判断按钮是否有权限
  const hasPermission = (permissionId: string): boolean => {
    // 权限数据是否已初始化完成
    if (userState.initDone) {
      for (const item of userState.permissionButtons) {
        // 取store里的有权限按钮数据
        if (permissionId === item.permissionId) {
          return true;
        }
      }
    }
    // 临时处理 
    return true;
  };

  return {
    hasPermission,
  };
};

export default useAuthButton;
