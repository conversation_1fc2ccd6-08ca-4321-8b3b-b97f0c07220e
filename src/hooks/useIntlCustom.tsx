// 国际化hooks
import { LOCAL, OPERATE_TYPE } from '@/constants/publicConstant';
import { messages } from '@/locales';
import { ICommonTableActionItem } from '@/types/ICommon';
import { TNotification, TOptionItem } from '@/types/TCommon';
import { renderButton } from '@/utils/comUtil';
import { Button, notification, Space, Tooltip } from 'antd';
import { ReactNode } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import useAuthButton from './useAuthButton';

// 主题
const theme = localStorage.getItem(LOCAL.THEME) || '';

// 获取当前语言
export const getCurrentLocale = () => {
  return localStorage.getItem(LOCAL.LOCALE) || 'zh-cn';
};

// 创建独立的翻译函数
const getTranslateMessage = (prefix: string, key: string, params?: Record<string, any>): string => {
  const locale = getCurrentLocale();
  const message = messages[locale];
  const id = prefix ? `${prefix}.${key}` : key;
  let translatedText = message?.[id.toString()] || '';

  // 如果有参数，进行模板替换
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      translatedText = translatedText.replace(`{${key}}`, String(value));
    });
  }

  return translatedText;
};

// 替换原来的 useIntl hook
export const intlConst = {
  formatMessage: (prefix: string, id: string, params?: Record<string, any>) => getTranslateMessage(prefix, id, params),
  formatDate: (date: Date, options?: Intl.DateTimeFormatOptions) => {
    return new Intl.DateTimeFormat(getCurrentLocale(), options).format(date);
  },
  formatNumber: (value: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat(getCurrentLocale(), options).format(value);
  },
};

const useIntlCustom = () => {
  const intl = useIntl();
  const { hasPermission } = useAuthButton();

  // 获取翻译文本
  const translate = (prefix: string, key: string, params?: Record<string, any>): string => {
    const id = `${prefix}.${key}`;

    let translatedText = intl.formatMessage({ id }) || '';

    // 如果有参数，进行模板替换
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        translatedText = translatedText.replace(`{${key}}`, String(value));
      });
    }

    return translatedText;
  };

  /** 获取通知
   * @param prefix 国际化key
   * @param type 'success' | 'info' | 'warning' | 'error'
   * @param context 通知内容
   * @param duration 选填，设置通知几秒自动关闭
   */
  const openNotificationTip = (
    prefix: string,
    type: TNotification | string,
    context: string,
    duration?: number,
  ): void => {
    notification[type]({
      message: translate('common', 'tip'),
      duration: duration || 3,
      description: translate(prefix, context),
    });
  };

  // 获取标题
  const formatActionTitle = (actionType: string, prefix: string, title: string = ''): string => {
    const actionLabel =
      OPERATE_TYPE[actionType] && actionType !== OPERATE_TYPE.list ? translate('common', OPERATE_TYPE[actionType]) : '';
    const titleLabel = title ? translate(prefix, title) : '';
    return `${actionLabel}${titleLabel}`;
  };

  // 获取操作结果提示
  const getActionResultTitle = (title: string, prefix: string, type: string, isSuccess: boolean): string => {
    let suffixName = isSuccess ? translate('common', 'success') : translate('common', 'fail');
    return formatActionTitle(type, prefix, title) + suffixName;
  };

  // 格式化html中的文本
  const formateHtmlText = (prefix: string, key: string): React.ReactNode => {
    const id = prefix ? `${prefix}.${key}` : key;
    return <FormattedMessage id={id} />;
  };

  // 格式化日期
  const formatLocalDate = (date, options) => {
    return intl.formatDate(date, options);
  };

  // 格式化数字
  const formatLocalNumber = (value, options) => {
    return intl.formatNumber(value, options);
  };

  /**
   * 返回下拉框选项数据
   * @param data [{key,value}]
   * @param showKey 是否展示key
   * @param prefix 国际化前缀
   * @returns {TOptionItem[]}
   */
  const getSelectOption = (data: Array<TOptionItem>, showKey: boolean = true, prefix: string = ''): TOptionItem[] => {
    const translateSelectDict = (data) => {
      return data.map((item) => {
        const label = prefix ? translate(prefix, item.value) : item.value;
        const translatedNode = {
          ...item,
          key: item.key,
          value: item.key,
          label: showKey && item.key?.trim() ? `${item.key} - ${label}` : label,
        };
        // 递归处理子节点
        if (item.children && item.children.length > 0) {
          translatedNode.children = translateSelectDict(item.children);
        }
        return translatedNode;
      });
    };
    if (data && data.length > 0) {
      return translateSelectDict(data);
    }
    return [];
  };

  /**
   * 返回可编辑表格下拉框选项数据
   * @param data [{key,value}]
   * @param showKey
   * @param prefix
   * @returns {*[]}
   */
  const getEditTableSelectOption = (data: Array<TOptionItem>, showKey: boolean = true, prefix: string = ''): object => {
    const res = {};
    data.forEach((item) => {
      const label = prefix ? translate(prefix, item.value) : item.value;
      res[item.key] = {
        text: showKey && item.key ? `${item.key} - ${label}` : label,
      };
    });
    return res;
  };

  /**
   * 渲染表格的操作列
   * @param data 操作列配置
   * @param onClick 事件回调
   * @returns {JSX}
   */
  const renderActionColumn = (
    data: Array<string | ICommonTableActionItem> = [],
    onClick = (type) => {},
  ): ReactNode | null => {
    if (data && data.length > 0) {
      const btns: Array<ReactNode> = [];
      data.forEach((item: ICommonTableActionItem | string) => {
        // 每种操作类型的属性提取出来
        let type;
        let title;
        let disabled = false;
        let icon;
        let permissionId;
        if (typeof item === 'object') {
          type = item.type;
          title = item.title || item.type;
          title = item.prefix && title ? translate(item.prefix, title) : title;
          disabled = item?.disabled || false;
          icon = item?.icon;
          permissionId = item.permissionId;
        } else if (typeof item === 'string') {
          type = item;
        }
        // 有传permissionId且无权限则不渲染
        if (permissionId && !hasPermission(permissionId)) {
          return;
        }
        // 判断是否有icon
        if (icon) {
          btns.push(
            <Tooltip key={type} placement="top" title={title}>
              <Button
                type="link"
                icon={icon}
                disabled={disabled ?? false}
                style={{ color: theme }}
                onClick={() => onClick(item)}
              />
            </Tooltip>,
          );
        } else if (type) {
          btns.push(renderButton({ type, disabled, permissionId, onClick }));
        }
      });
      return <Space>{btns}</Space>;
    } else {
      return null;
    }
  };

  const defaultInputPlaceholder = translate('common', 'inputPlaceholder');
  const defaultSelectPlaceholder = translate('common', 'selectPlaceholder');

  return {
    translate,
    formatActionTitle,
    formateHtmlText,
    formatLocalDate,
    formatLocalNumber,
    getSelectOption,
    getEditTableSelectOption,
    getActionResultTitle,
    openNotificationTip,
    defaultInputPlaceholder,
    defaultSelectPlaceholder,
    renderActionColumn,
  };
};

export default useIntlCustom;
