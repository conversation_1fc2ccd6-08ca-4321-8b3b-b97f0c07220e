/**
 * 字典/参数表字段store
 */
import common from '@/services/common';
import { IDictParam, IState } from '@/types/IDict';
import { createModel } from 'ice';

export default createModel({
  state: {
    dictMap: {},
  } as IState,
  reducers: {
    // 更新store的值
    updateDictMap(state, payload) {
      state.dictMap = { ...state.dictMap, ...payload };
    },
  },
  effects: (dispatch) => ({
    // 获取字典/参数数据
    async getDictList(dictParam: IDictParam) {
      const { url, key, param, optionKey, optionValue, showKey = false } = dictParam;
      // let res: any;
      // if (serviceType === 'business') {
      //   res = await common.getTableListDataBiz({ url, ...param });
      // } else {
      // }
      const res = await common.getTableListData({ url, param });
      const { data } = res;
      const result = data.map((item) => {
        const value = showKey ? `${item[optionKey]} - ${item[optionValue]}` : item[optionValue];
        return { ...item, key: item[optionKey], value };
      });
      this.updateDictMap({ [key]: result });
    },
  }),
});
