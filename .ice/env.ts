// This is build-time generated prebuilt for universal-env impl.
const isServer = import.meta.renderer === 'server';
const isClient = import.meta.renderer === 'client';
export const isNode = isServer;
export const isWeb = isClient && import.meta.target === 'web';
export const isWeex = isClient && import.meta.target === 'weex';
export const isAliMiniApp = isClient && import.meta.target === 'ali-miniapp';
export const isByteDanceMicroApp = isClient && import.meta.target === 'bytedance-microapp';
export const isBaiduSmartProgram = isClient && import.meta.target === 'baidu-smartprogram';
export const isKuaiShouMiniProgram = isClient && import.meta.target === 'kuaishou-miniprogram';
export const isWeChatMiniProgram = isClient && import.meta.target === 'wechat-miniprogram';
export const isQuickApp = false; // Now ice.js will not implement quick app target.
export const isMiniApp = isAliMiniApp; // in universal-env, isMiniApp is equals to isAliMiniApp
export const isKraken = isClient && import.meta.target === 'kraken';

// Following variables are runtime executed envs.
// See also @uni/env.
export const isPHA = isWeb && typeof pha === 'object';
const ua = typeof navigator === 'object' ? navigator.userAgent || navigator.swuserAgent : '';
export const isThemis = /Themis/.test(ua);
export const isWindVane = /WindVane/i.test(ua) && isWeb && typeof WindVane !== 'undefined' && typeof WindVane.call !== 'undefined';
// WindVane.call is a function while page importing lib-windvane
export const isFRM = isMiniApp && isWeb && typeof my !== 'undefined' && typeof my.isFRM !== 'undefined';

// Compatible with default export, for example: import env from '@ice/env'; env.isWeb;
export default {
  isWeb,
  isNode,
  isWeex,
  isKraken,
  isPHA,
  isThemis,
  isMiniApp,
  isByteDanceMicroApp,
  isBaiduSmartProgram,
  isKuaiShouMiniProgram,
  isWeChatMiniProgram,
  isQuickApp,
  isWindVane,
  isFRM,
};
