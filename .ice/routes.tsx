import { createRouteLoader, WrapRouteComponent, RouteErrorComponent } from '@ice/runtime';
import type { CreateRoutes } from '@ice/runtime';
const createRoutes: CreateRoutes = ({
  requestContext,
  renderMode,
}) => ([
  {
    path: '',
    async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_layout" */ '@/pages/layout');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'layout',
          isLayout: true,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'layout',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
    errorElement: <RouteErrorComponent />,
    componentName: 'layout',
    index: undefined,
    id: 'layout',
    exact: true,
    exports: ["default"],
    layout: true,
    children: [{
      path: 'operationCenter/rules/rulesConfig/hooks/usePageFormAction',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-rules-rulesconfig-hooks-usepageformaction" */ '@/pages/operationCenter/rules/rulesConfig/hooks/usePageFormAction');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/rules/rulesConfig/hooks/usePageFormAction',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/rules/rulesConfig/hooks/usePageFormAction',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-rules-rulesconfig-hooks-usepageformaction',
      index: undefined,
      id: 'operationCenter/rules/rulesConfig/hooks/usePageFormAction',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/collectorBench/components/customLayoutConstants',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-collectorbench-components-customlayoutconstants" */ '@/pages/workBench/collectorBench/components/customLayoutConstants');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/collectorBench/components/customLayoutConstants',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/collectorBench/components/customLayoutConstants',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-collectorbench-components-customlayoutconstants',
      index: undefined,
      id: 'workBench/collectorBench/components/customLayoutConstants',
      exact: true,
      exports: ["COLLECTION_FIELDS","CUSTOMER_FIELDS","DEFAULT_LAYOUT"],
    },{
      path: 'caseManage/caseList/components/DispatchDrawer/mockData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_casemanage-caselist-components-dispatchdrawer-mockdata" */ '@/pages/caseManage/caseList/components/DispatchDrawer/mockData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'caseManage/caseList/components/DispatchDrawer/mockData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'caseManage/caseList/components/DispatchDrawer/mockData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'casemanage-caselist-components-dispatchdrawer-mockdata',
      index: undefined,
      id: 'caseManage/caseList/components/DispatchDrawer/mockData',
      exact: true,
      exports: ["tableColumns","tableData"],
    },{
      path: 'operationCenter/rules/rulesConfig/components/AiContent',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-rules-rulesconfig-components-aicontent" */ '@/pages/operationCenter/rules/rulesConfig/components/AiContent');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/rules/rulesConfig/components/AiContent',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/rules/rulesConfig/components/AiContent',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-rules-rulesconfig-components-aicontent',
      index: undefined,
      id: 'operationCenter/rules/rulesConfig/components/AiContent',
      exact: true,
      exports: ["default"],
    },{
      path: 'operationCenter/rules/ruleFactor/componnets/DictModal',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-rules-rulefactor-componnets-dictmodal" */ '@/pages/operationCenter/rules/ruleFactor/componnets/DictModal');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/rules/ruleFactor/componnets/DictModal',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/rules/ruleFactor/componnets/DictModal',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-rules-rulefactor-componnets-dictmodal',
      index: undefined,
      id: 'operationCenter/rules/ruleFactor/componnets/DictModal',
      exact: true,
      exports: ["default"],
    },{
      path: 'operationCenter/rules/rulesConfig/hooks/usePageSearch',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-rules-rulesconfig-hooks-usepagesearch" */ '@/pages/operationCenter/rules/rulesConfig/hooks/usePageSearch');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/rules/rulesConfig/hooks/usePageSearch',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/rules/rulesConfig/hooks/usePageSearch',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-rules-rulesconfig-hooks-usepagesearch',
      index: undefined,
      id: 'operationCenter/rules/rulesConfig/hooks/usePageSearch',
      exact: true,
      exports: ["default"],
    },{
      path: 'operationCenter/rules/rulesConfig/hooks/usePageTable',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-rules-rulesconfig-hooks-usepagetable" */ '@/pages/operationCenter/rules/rulesConfig/hooks/usePageTable');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/rules/rulesConfig/hooks/usePageTable',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/rules/rulesConfig/hooks/usePageTable',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-rules-rulesconfig-hooks-usepagetable',
      index: undefined,
      id: 'operationCenter/rules/rulesConfig/hooks/usePageTable',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/workBench/components/ContentCom/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-workbench-components-contentcom-pageconfig" */ '@/pages/workBench/workBench/components/ContentCom/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/workBench/components/ContentCom/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/workBench/components/ContentCom/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-workbench-components-contentcom-pageconfig',
      index: undefined,
      id: 'workBench/workBench/components/ContentCom/pageConfig',
      exact: true,
      exports: ["pageConfig"],
    },{
      path: 'caseManage/caseList/components/DispatchDrawer',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_casemanage-caselist-components-dispatchdrawer-index" */ '@/pages/caseManage/caseList/components/DispatchDrawer/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'caseManage/caseList/components/DispatchDrawer',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'caseManage/caseList/components/DispatchDrawer',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'casemanage-caselist-components-dispatchdrawer-index',
      index: true,
      id: 'caseManage/caseList/components/DispatchDrawer',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/nodeConfig/components/ParamSettingDrawer',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-nodeconfig-components-paramsettingdrawer" */ '@/pages/flowManage/nodeConfig/components/ParamSettingDrawer');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/nodeConfig/components/ParamSettingDrawer',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/nodeConfig/components/ParamSettingDrawer',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-nodeconfig-components-paramsettingdrawer',
      index: undefined,
      id: 'flowManage/nodeConfig/components/ParamSettingDrawer',
      exact: true,
      exports: ["default"],
    },{
      path: 'operationCenter/rules/rulesConfig/components/IRules',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-rules-rulesconfig-components-irules" */ '@/pages/operationCenter/rules/rulesConfig/components/IRules');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/rules/rulesConfig/components/IRules',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/rules/rulesConfig/components/IRules',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-rules-rulesconfig-components-irules',
      index: undefined,
      id: 'operationCenter/rules/rulesConfig/components/IRules',
      exact: true,
      exports: [],
    },{
      path: 'workBench/collectorBench/components/CustomLayoutCom',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-collectorbench-components-customlayoutcom" */ '@/pages/workBench/collectorBench/components/CustomLayoutCom');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/collectorBench/components/CustomLayoutCom',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/collectorBench/components/CustomLayoutCom',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-collectorbench-components-customlayoutcom',
      index: undefined,
      id: 'workBench/collectorBench/components/CustomLayoutCom',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/program/components/ParamSettingDrawer',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-program-components-paramsettingdrawer" */ '@/pages/flowManage/program/components/ParamSettingDrawer');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/program/components/ParamSettingDrawer',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/program/components/ParamSettingDrawer',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-program-components-paramsettingdrawer',
      index: undefined,
      id: 'flowManage/program/components/ParamSettingDrawer',
      exact: true,
      exports: ["default"],
    },{
      path: 'caseManage/caseList/components/CaseDrawer',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_casemanage-caselist-components-casedrawer-index" */ '@/pages/caseManage/caseList/components/CaseDrawer/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'caseManage/caseList/components/CaseDrawer',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'caseManage/caseList/components/CaseDrawer',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'casemanage-caselist-components-casedrawer-index',
      index: true,
      id: 'caseManage/caseList/components/CaseDrawer',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/components/DebtCollectionCom/mockData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-components-debtcollectioncom-mockdata" */ '@/pages/workBench/components/DebtCollectionCom/mockData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/components/DebtCollectionCom/mockData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/components/DebtCollectionCom/mockData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-components-debtcollectioncom-mockdata',
      index: undefined,
      id: 'workBench/components/DebtCollectionCom/mockData',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/workBench/components/ContentCom',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-workbench-components-contentcom-index" */ '@/pages/workBench/workBench/components/ContentCom/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/workBench/components/ContentCom',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/workBench/components/ContentCom',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-workbench-components-contentcom-index',
      index: true,
      id: 'workBench/workBench/components/ContentCom',
      exact: true,
      exports: ["default"],
    },{
      path: 'caseManage/caseList/components/CaseModal',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_casemanage-caselist-components-casemodal-index" */ '@/pages/caseManage/caseList/components/CaseModal/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'caseManage/caseList/components/CaseModal',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'caseManage/caseList/components/CaseModal',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'casemanage-caselist-components-casemodal-index',
      index: true,
      id: 'caseManage/caseList/components/CaseModal',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/labelManage/componnets/LabelSetting',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-labelmanage-componnets-labelsetting" */ '@/pages/flowManage/labelManage/componnets/LabelSetting');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/labelManage/componnets/LabelSetting',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/labelManage/componnets/LabelSetting',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-labelmanage-componnets-labelsetting',
      index: undefined,
      id: 'flowManage/labelManage/componnets/LabelSetting',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/labelManage/hooks/usePageFormAction',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-labelmanage-hooks-usepageformaction" */ '@/pages/flowManage/labelManage/hooks/usePageFormAction');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/labelManage/hooks/usePageFormAction',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/labelManage/hooks/usePageFormAction',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-labelmanage-hooks-usepageformaction',
      index: undefined,
      id: 'flowManage/labelManage/hooks/usePageFormAction',
      exact: true,
      exports: ["default"],
    },{
      path: 'operationCenter/rules/ruleFactor/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-rules-rulefactor-useformconfig" */ '@/pages/operationCenter/rules/ruleFactor/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/rules/ruleFactor/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/rules/ruleFactor/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-rules-rulefactor-useformconfig',
      index: undefined,
      id: 'operationCenter/rules/ruleFactor/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/stationManagement/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-stationmanagement-useformconfig" */ '@/pages/strategyManage/stationManagement/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/stationManagement/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/stationManagement/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-stationmanagement-useformconfig',
      index: undefined,
      id: 'strategyManage/stationManagement/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/workBench/components/HeaderCom',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-workbench-components-headercom-index" */ '@/pages/workBench/workBench/components/HeaderCom/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/workBench/components/HeaderCom',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/workBench/components/HeaderCom',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-workbench-components-headercom-index',
      index: true,
      id: 'workBench/workBench/components/HeaderCom',
      exact: true,
      exports: ["default"],
    },{
      path: 'operationCenter/rules/rulesConfig/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-rules-rulesconfig-pageconfig" */ '@/pages/operationCenter/rules/rulesConfig/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/rules/rulesConfig/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/rules/rulesConfig/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-rules-rulesconfig-pageconfig',
      index: undefined,
      id: 'operationCenter/rules/rulesConfig/pageConfig',
      exact: true,
      exports: ["cardTitle","defOptionList","formActionPermissionObj","prefix","resetValue","tableProps","urlObj"],
    },{
      path: 'strategyManage/staffManagement/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-staffmanagement-useformconfig" */ '@/pages/strategyManage/staffManagement/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/staffManagement/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/staffManagement/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-staffmanagement-useformconfig',
      index: undefined,
      id: 'strategyManage/staffManagement/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/strategySetting/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-strategysetting-useformconfig" */ '@/pages/strategyManage/strategySetting/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/strategySetting/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/strategySetting/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-strategysetting-useformconfig',
      index: undefined,
      id: 'strategyManage/strategySetting/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/components/DebtCollectionCom',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-components-debtcollectioncom-index" */ '@/pages/workBench/components/DebtCollectionCom/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/components/DebtCollectionCom',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/components/DebtCollectionCom',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-components-debtcollectioncom-index',
      index: true,
      id: 'workBench/components/DebtCollectionCom',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/nodeConfig/components/ParamTable',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-nodeconfig-components-paramtable" */ '@/pages/flowManage/nodeConfig/components/ParamTable');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/nodeConfig/components/ParamTable',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/nodeConfig/components/ParamTable',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-nodeconfig-components-paramtable',
      index: undefined,
      id: 'flowManage/nodeConfig/components/ParamTable',
      exact: true,
      exports: ["default"],
    },{
      path: 'operationCenter/rules/ruleFactor/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-rules-rulefactor-pageconfig" */ '@/pages/operationCenter/rules/ruleFactor/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/rules/ruleFactor/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/rules/ruleFactor/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-rules-rulefactor-pageconfig',
      index: undefined,
      id: 'operationCenter/rules/ruleFactor/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'strategyManage/stationManagement/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-stationmanagement-pageconfig" */ '@/pages/strategyManage/stationManagement/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/stationManagement/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/stationManagement/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-stationmanagement-pageconfig',
      index: undefined,
      id: 'strategyManage/stationManagement/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'flowManage/labelManage/hooks/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-labelmanage-hooks-useformconfig" */ '@/pages/flowManage/labelManage/hooks/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/labelManage/hooks/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/labelManage/hooks/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-labelmanage-hooks-useformconfig',
      index: undefined,
      id: 'flowManage/labelManage/hooks/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/program/hooks/usePageFormAction',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-program-hooks-usepageformaction" */ '@/pages/flowManage/program/hooks/usePageFormAction');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/program/hooks/usePageFormAction',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/program/hooks/usePageFormAction',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-program-hooks-usepageformaction',
      index: undefined,
      id: 'flowManage/program/hooks/usePageFormAction',
      exact: true,
      exports: ["default"],
    },{
      path: 'operationCenter/intelligence/aiRuels',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-intelligence-airuels-index" */ '@/pages/operationCenter/intelligence/aiRuels/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/intelligence/aiRuels',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/intelligence/aiRuels',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-intelligence-airuels-index',
      index: true,
      id: 'operationCenter/intelligence/aiRuels',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/weightSetting/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-weightsetting-useformconfig" */ '@/pages/strategyManage/weightSetting/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/weightSetting/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/weightSetting/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-weightsetting-useformconfig',
      index: undefined,
      id: 'strategyManage/weightSetting/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/staffManagement/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-staffmanagement-pageconfig" */ '@/pages/strategyManage/staffManagement/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/staffManagement/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/staffManagement/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-staffmanagement-pageconfig',
      index: undefined,
      id: 'strategyManage/staffManagement/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'strategyManage/stationManagement/mockData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-stationmanagement-mockdata" */ '@/pages/strategyManage/stationManagement/mockData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/stationManagement/mockData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/stationManagement/mockData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-stationmanagement-mockdata',
      index: undefined,
      id: 'strategyManage/stationManagement/mockData',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/strategySetting/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-strategysetting-pageconfig" */ '@/pages/strategyManage/strategySetting/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/strategySetting/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/strategySetting/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-strategysetting-pageconfig',
      index: undefined,
      id: 'strategyManage/strategySetting/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'systemManage/menu/hooks/usePageFormAction',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-menu-hooks-usepageformaction" */ '@/pages/systemManage/menu/hooks/usePageFormAction');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/menu/hooks/usePageFormAction',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/menu/hooks/usePageFormAction',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-menu-hooks-usepageformaction',
      index: undefined,
      id: 'systemManage/menu/hooks/usePageFormAction',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/org/components/OrgDetailCard',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-org-components-orgdetailcard" */ '@/pages/systemManage/org/components/OrgDetailCard');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/org/components/OrgDetailCard',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/org/components/OrgDetailCard',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-org-components-orgdetailcard',
      index: undefined,
      id: 'systemManage/org/components/OrgDetailCard',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/nodeConfig/ParamSettingDrawer',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-nodeconfig-paramsettingdrawer" */ '@/pages/flowManage/nodeConfig/ParamSettingDrawer');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/nodeConfig/ParamSettingDrawer',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/nodeConfig/ParamSettingDrawer',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-nodeconfig-paramsettingdrawer',
      index: undefined,
      id: 'flowManage/nodeConfig/ParamSettingDrawer',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/program/components/ParamTable',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-program-components-paramtable" */ '@/pages/flowManage/program/components/ParamTable');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/program/components/ParamTable',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/program/components/ParamTable',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-program-components-paramtable',
      index: undefined,
      id: 'flowManage/program/components/ParamTable',
      exact: true,
      exports: ["default"],
    },{
      path: 'sourceManage/case/caseInfo/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_sourcemanage-case-caseinfo-useformconfig" */ '@/pages/sourceManage/case/caseInfo/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'sourceManage/case/caseInfo/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'sourceManage/case/caseInfo/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'sourcemanage-case-caseinfo-useformconfig',
      index: undefined,
      id: 'sourceManage/case/caseInfo/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/sortSetting/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-sortsetting-useformconfig" */ '@/pages/strategyManage/sortSetting/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/sortSetting/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/sortSetting/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-sortsetting-useformconfig',
      index: undefined,
      id: 'strategyManage/sortSetting/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/aduitManage/hooks/useSearch',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-aduitmanage-hooks-usesearch" */ '@/pages/systemManage/aduitManage/hooks/useSearch');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/aduitManage/hooks/useSearch',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/aduitManage/hooks/useSearch',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-aduitmanage-hooks-usesearch',
      index: undefined,
      id: 'systemManage/aduitManage/hooks/useSearch',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/role/components/MenuAuthCom',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-role-components-menuauthcom" */ '@/pages/systemManage/role/components/MenuAuthCom');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/role/components/MenuAuthCom',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/role/components/MenuAuthCom',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-role-components-menuauthcom',
      index: undefined,
      id: 'systemManage/role/components/MenuAuthCom',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/nodeFlowManage/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-nodeflowmanage-useformconfig" */ '@/pages/flowManage/nodeFlowManage/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/nodeFlowManage/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/nodeFlowManage/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-nodeflowmanage-useformconfig',
      index: undefined,
      id: 'flowManage/nodeFlowManage/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'operationCenter/rules/rulesConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-rules-rulesconfig-index" */ '@/pages/operationCenter/rules/rulesConfig/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/rules/rulesConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/rules/rulesConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-rules-rulesconfig-index',
      index: true,
      id: 'operationCenter/rules/rulesConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/staffManagement/mockData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-staffmanagement-mockdata" */ '@/pages/strategyManage/staffManagement/mockData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/staffManagement/mockData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/staffManagement/mockData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-staffmanagement-mockdata',
      index: undefined,
      id: 'strategyManage/staffManagement/mockData',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/weightSetting/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-weightsetting-pageconfig" */ '@/pages/strategyManage/weightSetting/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/weightSetting/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/weightSetting/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-weightsetting-pageconfig',
      index: undefined,
      id: 'strategyManage/weightSetting/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'systemManage/aduitManage/hooks/useTable',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-aduitmanage-hooks-usetable" */ '@/pages/systemManage/aduitManage/hooks/useTable');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/aduitManage/hooks/useTable',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/aduitManage/hooks/useTable',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-aduitmanage-hooks-usetable',
      index: undefined,
      id: 'systemManage/aduitManage/hooks/useTable',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/org/components/AddOrgModal',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-org-components-addorgmodal" */ '@/pages/systemManage/org/components/AddOrgModal');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/org/components/AddOrgModal',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/org/components/AddOrgModal',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-org-components-addorgmodal',
      index: undefined,
      id: 'systemManage/org/components/AddOrgModal',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/labelManage/hooks/useSearch',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-labelmanage-hooks-usesearch" */ '@/pages/flowManage/labelManage/hooks/useSearch');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/labelManage/hooks/useSearch',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/labelManage/hooks/useSearch',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-labelmanage-hooks-usesearch',
      index: undefined,
      id: 'flowManage/labelManage/hooks/useSearch',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/program/hooks/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-program-hooks-useformconfig" */ '@/pages/flowManage/program/hooks/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/program/hooks/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/program/hooks/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-program-hooks-useformconfig',
      index: undefined,
      id: 'flowManage/program/hooks/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'operationCenter/rules/ruleFactor',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-rules-rulefactor-index" */ '@/pages/operationCenter/rules/ruleFactor/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/rules/ruleFactor',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/rules/ruleFactor',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-rules-rulefactor-index',
      index: true,
      id: 'operationCenter/rules/ruleFactor',
      exact: true,
      exports: ["default"],
    },{
      path: 'operationCenter/rules/ruleFactor/utill',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_operationcenter-rules-rulefactor-utill" */ '@/pages/operationCenter/rules/ruleFactor/utill');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'operationCenter/rules/ruleFactor/utill',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'operationCenter/rules/ruleFactor/utill',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'operationcenter-rules-rulefactor-utill',
      index: undefined,
      id: 'operationCenter/rules/ruleFactor/utill',
      exact: true,
      exports: ["convertToArray"],
    },{
      path: 'strategyManage/stationManagement',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-stationmanagement-index" */ '@/pages/strategyManage/stationManagement/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/stationManagement',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/stationManagement',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-stationmanagement-index',
      index: true,
      id: 'strategyManage/stationManagement',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/workbenck/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-workbenck-useformconfig" */ '@/pages/strategyManage/workbenck/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/workbenck/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/workbenck/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-workbenck-useformconfig',
      index: undefined,
      id: 'strategyManage/workbenck/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/labelManage/hooks/useTable',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-labelmanage-hooks-usetable" */ '@/pages/flowManage/labelManage/hooks/useTable');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/labelManage/hooks/useTable',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/labelManage/hooks/useTable',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-labelmanage-hooks-usetable',
      index: undefined,
      id: 'flowManage/labelManage/hooks/useTable',
      exact: true,
      exports: ["default"],
    },{
      path: 'sourceManage/case/caseInfo/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_sourcemanage-case-caseinfo-pageconfig" */ '@/pages/sourceManage/case/caseInfo/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'sourceManage/case/caseInfo/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'sourceManage/case/caseInfo/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'sourcemanage-case-caseinfo-pageconfig',
      index: undefined,
      id: 'sourceManage/case/caseInfo/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'strategyManage/sortSetting/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-sortsetting-pageconfig" */ '@/pages/strategyManage/sortSetting/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/sortSetting/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/sortSetting/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-sortsetting-pageconfig',
      index: undefined,
      id: 'strategyManage/sortSetting/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'dataManage/smsTemplate/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_datamanage-smstemplate-useformconfig" */ '@/pages/dataManage/smsTemplate/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'dataManage/smsTemplate/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'dataManage/smsTemplate/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'datamanage-smstemplate-useformconfig',
      index: undefined,
      id: 'dataManage/smsTemplate/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/dispatchManage/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-dispatchmanage-pageconfig" */ '@/pages/flowManage/dispatchManage/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/dispatchManage/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/dispatchManage/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-dispatchmanage-pageconfig',
      index: undefined,
      id: 'flowManage/dispatchManage/pageConfig',
      exact: true,
      exports: ["pageConfig"],
    },{
      path: 'flowManage/nodeFlowManage/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-nodeflowmanage-pageconfig" */ '@/pages/flowManage/nodeFlowManage/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/nodeFlowManage/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/nodeFlowManage/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-nodeflowmanage-pageconfig',
      index: undefined,
      id: 'flowManage/nodeFlowManage/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'strategyManage/staffManagement',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-staffmanagement-index" */ '@/pages/strategyManage/staffManagement/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/staffManagement',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/staffManagement',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-staffmanagement-index',
      index: true,
      id: 'strategyManage/staffManagement',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/strategySetting',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-strategysetting-index" */ '@/pages/strategyManage/strategySetting/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/strategySetting',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/strategySetting',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-strategysetting-index',
      index: true,
      id: 'strategyManage/strategySetting',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/components/HeaderCom',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-components-headercom-index" */ '@/pages/workBench/components/HeaderCom/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/components/HeaderCom',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/components/HeaderCom',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-components-headercom-index',
      index: true,
      id: 'workBench/components/HeaderCom',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/messageBench/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-messagebench-useformconfig" */ '@/pages/workBench/messageBench/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/messageBench/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/messageBench/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-messagebench-useformconfig',
      index: undefined,
      id: 'workBench/messageBench/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/workBench/constansts',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-workbench-constansts-index" */ '@/pages/workBench/workBench/constansts/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/workBench/constansts',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/workBench/constansts',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-workbench-constansts-index',
      index: true,
      id: 'workBench/workBench/constansts',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/nodeConfig/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-nodeconfig-useformconfig" */ '@/pages/flowManage/nodeConfig/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/nodeConfig/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/nodeConfig/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-nodeconfig-useformconfig',
      index: undefined,
      id: 'flowManage/nodeConfig/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'sourceManage/case/caseInfo/mockData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_sourcemanage-case-caseinfo-mockdata" */ '@/pages/sourceManage/case/caseInfo/mockData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'sourceManage/case/caseInfo/mockData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'sourceManage/case/caseInfo/mockData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'sourcemanage-case-caseinfo-mockdata',
      index: undefined,
      id: 'sourceManage/case/caseInfo/mockData',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/workbenck/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-workbenck-pageconfig" */ '@/pages/strategyManage/workbenck/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/workbenck/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/workbenck/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-workbenck-pageconfig',
      index: undefined,
      id: 'strategyManage/workbenck/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'systemManage/aduitManage/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-aduitmanage-pageconfig" */ '@/pages/systemManage/aduitManage/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/aduitManage/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/aduitManage/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-aduitmanage-pageconfig',
      index: undefined,
      id: 'systemManage/aduitManage/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'systemManage/org/components/OrgTree',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-org-components-orgtree" */ '@/pages/systemManage/org/components/OrgTree');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/org/components/OrgTree',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/org/components/OrgTree',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-org-components-orgtree',
      index: undefined,
      id: 'systemManage/org/components/OrgTree',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/position/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-position-useformconfig" */ '@/pages/systemManage/position/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/position/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/position/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-position-useformconfig',
      index: undefined,
      id: 'systemManage/position/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'caseManage/caseQuery/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_casemanage-casequery-useformconfig" */ '@/pages/caseManage/caseQuery/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'caseManage/caseQuery/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'caseManage/caseQuery/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'casemanage-casequery-useformconfig',
      index: undefined,
      id: 'caseManage/caseQuery/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/dispatchManage/mockData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-dispatchmanage-mockdata" */ '@/pages/flowManage/dispatchManage/mockData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/dispatchManage/mockData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/dispatchManage/mockData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-dispatchmanage-mockdata',
      index: undefined,
      id: 'flowManage/dispatchManage/mockData',
      exact: true,
      exports: ["data1","data2","data3"],
    },{
      path: 'flowManage/program/hooks/useSearch',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-program-hooks-usesearch" */ '@/pages/flowManage/program/hooks/useSearch');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/program/hooks/useSearch',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/program/hooks/useSearch',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-program-hooks-usesearch',
      index: undefined,
      id: 'flowManage/program/hooks/useSearch',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/weightSetting',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-weightsetting-index" */ '@/pages/strategyManage/weightSetting/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/weightSetting',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/weightSetting',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-weightsetting-index',
      index: true,
      id: 'strategyManage/weightSetting',
      exact: true,
      exports: ["default"],
    },{
      path: 'caseManage/caseList/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_casemanage-caselist-useformconfig" */ '@/pages/caseManage/caseList/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'caseManage/caseList/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'caseManage/caseList/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'casemanage-caselist-useformconfig',
      index: undefined,
      id: 'caseManage/caseList/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'dataManage/callList/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_datamanage-calllist-useformconfig" */ '@/pages/dataManage/callList/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'dataManage/callList/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'dataManage/callList/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'datamanage-calllist-useformconfig',
      index: undefined,
      id: 'dataManage/callList/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'dataManage/realtime/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_datamanage-realtime-useformconfig" */ '@/pages/dataManage/realtime/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'dataManage/realtime/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'dataManage/realtime/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'datamanage-realtime-useformconfig',
      index: undefined,
      id: 'dataManage/realtime/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'dataManage/smsTemplate/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_datamanage-smstemplate-pageconfig" */ '@/pages/dataManage/smsTemplate/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'dataManage/smsTemplate/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'dataManage/smsTemplate/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'datamanage-smstemplate-pageconfig',
      index: undefined,
      id: 'dataManage/smsTemplate/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'flowManage/labelManage/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-labelmanage-pageconfig" */ '@/pages/flowManage/labelManage/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/labelManage/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/labelManage/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-labelmanage-pageconfig',
      index: undefined,
      id: 'flowManage/labelManage/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'flowManage/program/hooks/useTable',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-program-hooks-usetable" */ '@/pages/flowManage/program/hooks/useTable');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/program/hooks/useTable',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/program/hooks/useTable',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-program-hooks-usetable',
      index: undefined,
      id: 'flowManage/program/hooks/useTable',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/menu/hooks/useSearch',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-menu-hooks-usesearch" */ '@/pages/systemManage/menu/hooks/useSearch');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/menu/hooks/useSearch',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/menu/hooks/useSearch',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-menu-hooks-usesearch',
      index: undefined,
      id: 'systemManage/menu/hooks/useSearch',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/collectorBench/mockData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-collectorbench-mockdata" */ '@/pages/workBench/collectorBench/mockData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/collectorBench/mockData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/collectorBench/mockData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-collectorbench-mockdata',
      index: undefined,
      id: 'workBench/collectorBench/mockData',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/messageBench/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-messagebench-pageconfig" */ '@/pages/workBench/messageBench/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/messageBench/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/messageBench/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-messagebench-pageconfig',
      index: undefined,
      id: 'workBench/messageBench/pageConfig',
      exact: true,
      exports: ["anchorData","descData","segmentedData"],
    },{
      path: 'dataManage/offline/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_datamanage-offline-useformconfig" */ '@/pages/dataManage/offline/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'dataManage/offline/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'dataManage/offline/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'datamanage-offline-useformconfig',
      index: undefined,
      id: 'dataManage/offline/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/dispatchManage/Detail',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-dispatchmanage-detail" */ '@/pages/flowManage/dispatchManage/Detail');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/dispatchManage/Detail',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/dispatchManage/Detail',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-dispatchmanage-detail',
      index: undefined,
      id: 'flowManage/dispatchManage/Detail',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/nodeConfig/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-nodeconfig-pageconfig" */ '@/pages/flowManage/nodeConfig/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/nodeConfig/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/nodeConfig/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-nodeconfig-pageconfig',
      index: undefined,
      id: 'flowManage/nodeConfig/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'sourceManage/case/caseInfo',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_sourcemanage-case-caseinfo-index" */ '@/pages/sourceManage/case/caseInfo/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'sourceManage/case/caseInfo',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'sourceManage/case/caseInfo',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'sourcemanage-case-caseinfo-index',
      index: true,
      id: 'sourceManage/case/caseInfo',
      exact: true,
      exports: ["default"],
    },{
      path: 'strategyManage/sortSetting',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-sortsetting-index" */ '@/pages/strategyManage/sortSetting/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/sortSetting',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/sortSetting',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-sortsetting-index',
      index: true,
      id: 'strategyManage/sortSetting',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/menu/hooks/useTable',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-menu-hooks-usetable" */ '@/pages/systemManage/menu/hooks/useTable');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/menu/hooks/useTable',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/menu/hooks/useTable',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-menu-hooks-usetable',
      index: undefined,
      id: 'systemManage/menu/hooks/useTable',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/position/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-position-pageconfig" */ '@/pages/systemManage/position/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/position/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/position/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-position-pageconfig',
      index: undefined,
      id: 'systemManage/position/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'caseManage/caseQuery/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_casemanage-casequery-pageconfig" */ '@/pages/caseManage/caseQuery/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'caseManage/caseQuery/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'caseManage/caseQuery/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'casemanage-casequery-pageconfig',
      index: undefined,
      id: 'caseManage/caseQuery/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'flowManage/dispatchManage',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-dispatchmanage-index" */ '@/pages/flowManage/dispatchManage/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/dispatchManage',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/dispatchManage',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-dispatchmanage-index',
      index: true,
      id: 'flowManage/dispatchManage',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/nodeFlowManage',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-nodeflowmanage-index" */ '@/pages/flowManage/nodeFlowManage/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/nodeFlowManage',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/nodeFlowManage',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-nodeflowmanage-index',
      index: true,
      id: 'flowManage/nodeFlowManage',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/dict/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-dict-useformconfig" */ '@/pages/systemManage/dict/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/dict/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/dict/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-dict-useformconfig',
      index: undefined,
      id: 'systemManage/dict/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/menu/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-menu-useformconfig" */ '@/pages/systemManage/menu/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/menu/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/menu/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-menu-useformconfig',
      index: undefined,
      id: 'systemManage/menu/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/role/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-role-useformconfig" */ '@/pages/systemManage/role/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/role/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/role/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-role-useformconfig',
      index: undefined,
      id: 'systemManage/role/useFormConfig',
      exact: true,
      exports: ["default","roleStatusEunm"],
    },{
      path: 'systemManage/user/useFormConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-user-useformconfig" */ '@/pages/systemManage/user/useFormConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/user/useFormConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/user/useFormConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-user-useformconfig',
      index: undefined,
      id: 'systemManage/user/useFormConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/messageBench/mockData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-messagebench-mockdata" */ '@/pages/workBench/messageBench/mockData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/messageBench/mockData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/messageBench/mockData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-messagebench-mockdata',
      index: undefined,
      id: 'workBench/messageBench/mockData',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/projectBench/mockData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-projectbench-mockdata" */ '@/pages/workBench/projectBench/mockData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/projectBench/mockData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/projectBench/mockData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-projectbench-mockdata',
      index: undefined,
      id: 'workBench/projectBench/mockData',
      exact: true,
      exports: ["default"],
    },{
      path: 'caseManage/caseList/configData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_casemanage-caselist-configdata" */ '@/pages/caseManage/caseList/configData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'caseManage/caseList/configData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'caseManage/caseList/configData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'casemanage-caselist-configdata',
      index: undefined,
      id: 'caseManage/caseList/configData',
      exact: true,
      exports: ["default"],
    },{
      path: 'caseManage/caseList/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_casemanage-caselist-pageconfig" */ '@/pages/caseManage/caseList/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'caseManage/caseList/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'caseManage/caseList/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'casemanage-caselist-pageconfig',
      index: undefined,
      id: 'caseManage/caseList/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'dataManage/callList/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_datamanage-calllist-pageconfig" */ '@/pages/dataManage/callList/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'dataManage/callList/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'dataManage/callList/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'datamanage-calllist-pageconfig',
      index: undefined,
      id: 'dataManage/callList/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'dataManage/realtime/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_datamanage-realtime-pageconfig" */ '@/pages/dataManage/realtime/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'dataManage/realtime/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'dataManage/realtime/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'datamanage-realtime-pageconfig',
      index: undefined,
      id: 'dataManage/realtime/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'flowManage/nodeConfig/mockData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-nodeconfig-mockdata" */ '@/pages/flowManage/nodeConfig/mockData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/nodeConfig/mockData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/nodeConfig/mockData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-nodeconfig-mockdata',
      index: undefined,
      id: 'flowManage/nodeConfig/mockData',
      exact: true,
      exports: ["data1","data2","data3"],
    },{
      path: 'strategyManage/workbenck',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_strategymanage-workbenck-index" */ '@/pages/strategyManage/workbenck/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'strategyManage/workbenck',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'strategyManage/workbenck',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'strategymanage-workbenck-index',
      index: true,
      id: 'strategyManage/workbenck',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/aduitManage',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-aduitmanage-index" */ '@/pages/systemManage/aduitManage/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/aduitManage',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/aduitManage',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-aduitmanage-index',
      index: true,
      id: 'systemManage/aduitManage',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/collectorBench',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-collectorbench-index" */ '@/pages/workBench/collectorBench/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/collectorBench',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/collectorBench',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-collectorbench-index',
      index: true,
      id: 'workBench/collectorBench',
      exact: true,
      exports: ["default"],
    },{
      path: 'caseManage/caseQuery/mockData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_casemanage-casequery-mockdata" */ '@/pages/caseManage/caseQuery/mockData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'caseManage/caseQuery/mockData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'caseManage/caseQuery/mockData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'casemanage-casequery-mockdata',
      index: undefined,
      id: 'caseManage/caseQuery/mockData',
      exact: true,
      exports: ["default"],
    },{
      path: 'dataManage/offline/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_datamanage-offline-pageconfig" */ '@/pages/dataManage/offline/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'dataManage/offline/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'dataManage/offline/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'datamanage-offline-pageconfig',
      index: undefined,
      id: 'dataManage/offline/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'flowManage/program/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-program-pageconfig" */ '@/pages/flowManage/program/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/program/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/program/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-program-pageconfig',
      index: undefined,
      id: 'flowManage/program/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'dataManage/smsTemplate',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_datamanage-smstemplate-index" */ '@/pages/dataManage/smsTemplate/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'dataManage/smsTemplate',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'dataManage/smsTemplate',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'datamanage-smstemplate-index',
      index: true,
      id: 'dataManage/smsTemplate',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/labelManage',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-labelmanage-index" */ '@/pages/flowManage/labelManage/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/labelManage',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/labelManage',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-labelmanage-index',
      index: true,
      id: 'flowManage/labelManage',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/dict/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-dict-pageconfig" */ '@/pages/systemManage/dict/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/dict/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/dict/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-dict-pageconfig',
      index: undefined,
      id: 'systemManage/dict/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'systemManage/menu/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-menu-pageconfig" */ '@/pages/systemManage/menu/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/menu/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/menu/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-menu-pageconfig',
      index: undefined,
      id: 'systemManage/menu/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'systemManage/role/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-role-pageconfig" */ '@/pages/systemManage/role/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/role/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/role/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-role-pageconfig',
      index: undefined,
      id: 'systemManage/role/pageConfig',
      exact: true,
      exports: ["mountstatusEunm","pageConfig","roleStatusEunm","userPageConfig"],
    },{
      path: 'systemManage/user/pageConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-user-pageconfig" */ '@/pages/systemManage/user/pageConfig');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/user/pageConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/user/pageConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-user-pageconfig',
      index: undefined,
      id: 'systemManage/user/pageConfig',
      exact: true,
      exports: ["dictEnum","pageConfig"],
    },{
      path: 'workBench/messageBench',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-messagebench-index" */ '@/pages/workBench/messageBench/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/messageBench',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/messageBench',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-messagebench-index',
      index: true,
      id: 'workBench/messageBench',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/projectBench',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-projectbench-index" */ '@/pages/workBench/projectBench/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/projectBench',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/projectBench',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-projectbench-index',
      index: true,
      id: 'workBench/projectBench',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/nodeConfig',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-nodeconfig-index" */ '@/pages/flowManage/nodeConfig/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/nodeConfig',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/nodeConfig',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-nodeconfig-index',
      index: true,
      id: 'flowManage/nodeConfig',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/position',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-position-index" */ '@/pages/systemManage/position/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/position',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/position',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-position-index',
      index: true,
      id: 'systemManage/position',
      exact: true,
      exports: ["default"],
    },{
      path: 'caseManage/caseQuery',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_casemanage-casequery-index" */ '@/pages/caseManage/caseQuery/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'caseManage/caseQuery',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'caseManage/caseQuery',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'casemanage-casequery-index',
      index: true,
      id: 'caseManage/caseQuery',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/menu/iconData',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-menu-icondata" */ '@/pages/systemManage/menu/iconData');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/menu/iconData',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/menu/iconData',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-menu-icondata',
      index: undefined,
      id: 'systemManage/menu/iconData',
      exact: true,
      exports: ["dataOutlined","directionOutlined","editorOutlined","logoOutlined","otherOutlined","suggestionOutlined"],
    },{
      path: 'systemManage/org/constants',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-org-constants" */ '@/pages/systemManage/org/constants');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/org/constants',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/org/constants',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-org-constants',
      index: undefined,
      id: 'systemManage/org/constants',
      exact: true,
      exports: ["ORG_DETAIL_MAP","ORG_TYPE_OPTIONS","PARENT_ORG_OPTIONS","default"],
    },{
      path: 'caseManage/caseList',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_casemanage-caselist-index" */ '@/pages/caseManage/caseList/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'caseManage/caseList',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'caseManage/caseList',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'casemanage-caselist-index',
      index: true,
      id: 'caseManage/caseList',
      exact: true,
      exports: ["default"],
    },{
      path: 'dataManage/callList',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_datamanage-calllist-index" */ '@/pages/dataManage/callList/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'dataManage/callList',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'dataManage/callList',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'datamanage-calllist-index',
      index: true,
      id: 'dataManage/callList',
      exact: true,
      exports: ["default"],
    },{
      path: 'dataManage/realtime',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_datamanage-realtime-index" */ '@/pages/dataManage/realtime/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'dataManage/realtime',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'dataManage/realtime',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'datamanage-realtime-index',
      index: true,
      id: 'dataManage/realtime',
      exact: true,
      exports: ["default"],
    },{
      path: 'workBench/workBench',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_workbench-workbench-index" */ '@/pages/workBench/workBench/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'workBench/workBench',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'workBench/workBench',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'workbench-workbench-index',
      index: true,
      id: 'workBench/workBench',
      exact: true,
      exports: ["default"],
    },{
      path: 'dataManage/offline',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_datamanage-offline-index" */ '@/pages/dataManage/offline/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'dataManage/offline',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'dataManage/offline',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'datamanage-offline-index',
      index: true,
      id: 'dataManage/offline',
      exact: true,
      exports: ["default"],
    },{
      path: 'flowManage/program',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_flowmanage-program-index" */ '@/pages/flowManage/program/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'flowManage/program',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'flowManage/program',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'flowmanage-program-index',
      index: true,
      id: 'flowManage/program',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/dict',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-dict-index" */ '@/pages/systemManage/dict/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/dict',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/dict',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-dict-index',
      index: true,
      id: 'systemManage/dict',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/menu',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-menu-index" */ '@/pages/systemManage/menu/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/menu',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/menu',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-menu-index',
      index: true,
      id: 'systemManage/menu',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/role',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-role-index" */ '@/pages/systemManage/role/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/role',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/role',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-role-index',
      index: true,
      id: 'systemManage/role',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/user',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-user-index" */ '@/pages/systemManage/user/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/user',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/user',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-user-index',
      index: true,
      id: 'systemManage/user',
      exact: true,
      exports: ["default"],
    },{
      path: 'systemManage/menu/util',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-menu-util" */ '@/pages/systemManage/menu/util');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/menu/util',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/menu/util',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-menu-util',
      index: undefined,
      id: 'systemManage/menu/util',
      exact: true,
      exports: ["statusEunm","tabTypeEunm"],
    },{
      path: 'systemManage/org',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_systemmanage-org-index" */ '@/pages/systemManage/org/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'systemManage/org',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'systemManage/org',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'systemmanage-org-index',
      index: true,
      id: 'systemManage/org',
      exact: true,
      exports: ["default"],
    },{
      path: 'login',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_login-index" */ '@/pages/login/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: 'login',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: 'login',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'login-index',
      index: true,
      id: 'login',
      exact: true,
      exports: ["default"],
    },{
      path: '',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_index" */ '@/pages/index');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: '/',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: '/',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: 'index',
      index: true,
      id: '/',
      exact: true,
      exports: ["default"],
    },{
      path: '*',
      async lazy() {
      const componentModule = await import(/* webpackChunkName: "p_$" */ '@/pages/$');
      return {
        ...componentModule,
        Component: () => WrapRouteComponent({
          routeId: '*',
          isLayout: false,
          routeExports: componentModule,
        }),
        loader: createRouteLoader({
          routeId: '*',
          requestContext,
          renderMode,
          module: componentModule,
        }),
      };
    },
      errorElement: <RouteErrorComponent />,
      componentName: '$',
      index: undefined,
      id: '*',
      exact: true,
      exports: ["default"],
    },]
  },
]);
export default createRoutes;
