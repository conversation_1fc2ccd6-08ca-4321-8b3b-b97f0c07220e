import { pageConfig as pageConfig_workBench_workBench_components_ContentCom_pageConfig } from '@/pages/workBench/workBench/components/ContentCom/pageConfig';
import { pageConfig as pageConfig_operationCenter_rules_ruleFactor_pageConfig } from '@/pages/operationCenter/rules/ruleFactor/pageConfig';
import { pageConfig as pageConfig_strategyManage_stationManagement_pageConfig } from '@/pages/strategyManage/stationManagement/pageConfig';
import { pageConfig as pageConfig_strategyManage_staffManagement_pageConfig } from '@/pages/strategyManage/staffManagement/pageConfig';
import { pageConfig as pageConfig_strategyManage_strategySetting_pageConfig } from '@/pages/strategyManage/strategySetting/pageConfig';
import { pageConfig as pageConfig_strategyManage_weightSetting_pageConfig } from '@/pages/strategyManage/weightSetting/pageConfig';
import { pageConfig as pageConfig_sourceManage_case_caseInfo_pageConfig } from '@/pages/sourceManage/case/caseInfo/pageConfig';
import { pageConfig as pageConfig_strategyManage_sortSetting_pageConfig } from '@/pages/strategyManage/sortSetting/pageConfig';
import { pageConfig as pageConfig_flowManage_dispatchManage_pageConfig } from '@/pages/flowManage/dispatchManage/pageConfig';
import { pageConfig as pageConfig_flowManage_nodeFlowManage_pageConfig } from '@/pages/flowManage/nodeFlowManage/pageConfig';
import { pageConfig as pageConfig_strategyManage_workbenck_pageConfig } from '@/pages/strategyManage/workbenck/pageConfig';
import { pageConfig as pageConfig_systemManage_aduitManage_pageConfig } from '@/pages/systemManage/aduitManage/pageConfig';
import { pageConfig as pageConfig_dataManage_smsTemplate_pageConfig } from '@/pages/dataManage/smsTemplate/pageConfig';
import { pageConfig as pageConfig_flowManage_labelManage_pageConfig } from '@/pages/flowManage/labelManage/pageConfig';
import { pageConfig as pageConfig_flowManage_nodeConfig_pageConfig } from '@/pages/flowManage/nodeConfig/pageConfig';
import { pageConfig as pageConfig_systemManage_position_pageConfig } from '@/pages/systemManage/position/pageConfig';
import { pageConfig as pageConfig_caseManage_caseQuery_pageConfig } from '@/pages/caseManage/caseQuery/pageConfig';
import { pageConfig as pageConfig_caseManage_caseList_pageConfig } from '@/pages/caseManage/caseList/pageConfig';
import { pageConfig as pageConfig_dataManage_callList_pageConfig } from '@/pages/dataManage/callList/pageConfig';
import { pageConfig as pageConfig_dataManage_realtime_pageConfig } from '@/pages/dataManage/realtime/pageConfig';
import { pageConfig as pageConfig_dataManage_offline_pageConfig } from '@/pages/dataManage/offline/pageConfig';
import { pageConfig as pageConfig_flowManage_program_pageConfig } from '@/pages/flowManage/program/pageConfig';
import { pageConfig as pageConfig_systemManage_dict_pageConfig } from '@/pages/systemManage/dict/pageConfig';
import { pageConfig as pageConfig_systemManage_menu_pageConfig } from '@/pages/systemManage/menu/pageConfig';
import { pageConfig as pageConfig_systemManage_role_pageConfig } from '@/pages/systemManage/role/pageConfig';
import { pageConfig as pageConfig_systemManage_user_pageConfig } from '@/pages/systemManage/user/pageConfig';

export default {
  'workBench/workBench/components/ContentCom/pageConfig': pageConfig_workBench_workBench_components_ContentCom_pageConfig,
  'operationCenter/rules/ruleFactor/pageConfig': pageConfig_operationCenter_rules_ruleFactor_pageConfig,
  'strategyManage/stationManagement/pageConfig': pageConfig_strategyManage_stationManagement_pageConfig,
  'strategyManage/staffManagement/pageConfig': pageConfig_strategyManage_staffManagement_pageConfig,
  'strategyManage/strategySetting/pageConfig': pageConfig_strategyManage_strategySetting_pageConfig,
  'strategyManage/weightSetting/pageConfig': pageConfig_strategyManage_weightSetting_pageConfig,
  'sourceManage/case/caseInfo/pageConfig': pageConfig_sourceManage_case_caseInfo_pageConfig,
  'strategyManage/sortSetting/pageConfig': pageConfig_strategyManage_sortSetting_pageConfig,
  'flowManage/dispatchManage/pageConfig': pageConfig_flowManage_dispatchManage_pageConfig,
  'flowManage/nodeFlowManage/pageConfig': pageConfig_flowManage_nodeFlowManage_pageConfig,
  'strategyManage/workbenck/pageConfig': pageConfig_strategyManage_workbenck_pageConfig,
  'systemManage/aduitManage/pageConfig': pageConfig_systemManage_aduitManage_pageConfig,
  'dataManage/smsTemplate/pageConfig': pageConfig_dataManage_smsTemplate_pageConfig,
  'flowManage/labelManage/pageConfig': pageConfig_flowManage_labelManage_pageConfig,
  'flowManage/nodeConfig/pageConfig': pageConfig_flowManage_nodeConfig_pageConfig,
  'systemManage/position/pageConfig': pageConfig_systemManage_position_pageConfig,
  'caseManage/caseQuery/pageConfig': pageConfig_caseManage_caseQuery_pageConfig,
  'caseManage/caseList/pageConfig': pageConfig_caseManage_caseList_pageConfig,
  'dataManage/callList/pageConfig': pageConfig_dataManage_callList_pageConfig,
  'dataManage/realtime/pageConfig': pageConfig_dataManage_realtime_pageConfig,
  'dataManage/offline/pageConfig': pageConfig_dataManage_offline_pageConfig,
  'flowManage/program/pageConfig': pageConfig_flowManage_program_pageConfig,
  'systemManage/dict/pageConfig': pageConfig_systemManage_dict_pageConfig,
  'systemManage/menu/pageConfig': pageConfig_systemManage_menu_pageConfig,
  'systemManage/role/pageConfig': pageConfig_systemManage_role_pageConfig,
  'systemManage/user/pageConfig': pageConfig_systemManage_user_pageConfig,
};
