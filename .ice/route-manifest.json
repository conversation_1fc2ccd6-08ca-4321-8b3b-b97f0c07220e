[{"id": "layout", "file": "layout.tsx", "componentName": "layout", "layout": true, "exports": ["default"], "children": [{"path": "operationCenter/rules/rulesConfig/hooks/usePageFormAction", "id": "operationCenter/rules/rulesConfig/hooks/usePageFormAction", "parentId": "layout", "file": "operationCenter/rules/rulesConfig/hooks/usePageFormAction.tsx", "componentName": "operationcenter-rules-rulesconfig-hooks-usepageformaction", "layout": false, "exports": ["default"]}, {"path": "workBench/collectorBench/components/customLayoutConstants", "id": "workBench/collectorBench/components/customLayoutConstants", "parentId": "layout", "file": "workBench/collectorBench/components/customLayoutConstants.ts", "componentName": "workbench-collectorbench-components-customlayoutconstants", "layout": false, "exports": ["COLLECTION_FIELDS", "CUSTOMER_FIELDS", "DEFAULT_LAYOUT"]}, {"path": "caseManage/caseList/components/DispatchDrawer/mockData", "id": "caseManage/caseList/components/DispatchDrawer/mockData", "parentId": "layout", "file": "caseManage/caseList/components/DispatchDrawer/mockData.js", "componentName": "casemanage-caselist-components-dispatchdrawer-mockdata", "layout": false, "exports": ["tableColumns", "tableData"]}, {"path": "operationCenter/rules/rulesConfig/components/AiContent", "id": "operationCenter/rules/rulesConfig/components/AiContent", "parentId": "layout", "file": "operationCenter/rules/rulesConfig/components/AiContent.tsx", "componentName": "operationcenter-rules-rulesconfig-components-aicontent", "layout": false, "exports": ["default"]}, {"path": "operationCenter/rules/ruleFactor/componnets/DictModal", "id": "operationCenter/rules/ruleFactor/componnets/DictModal", "parentId": "layout", "file": "operationCenter/rules/ruleFactor/componnets/DictModal.tsx", "componentName": "operationcenter-rules-rulefactor-componnets-dictmodal", "layout": false, "exports": ["default"]}, {"path": "operationCenter/rules/rulesConfig/hooks/usePageSearch", "id": "operationCenter/rules/rulesConfig/hooks/usePageSearch", "parentId": "layout", "file": "operationCenter/rules/rulesConfig/hooks/usePageSearch.tsx", "componentName": "operationcenter-rules-rulesconfig-hooks-usepagesearch", "layout": false, "exports": ["default"]}, {"path": "operationCenter/rules/rulesConfig/hooks/usePageTable", "id": "operationCenter/rules/rulesConfig/hooks/usePageTable", "parentId": "layout", "file": "operationCenter/rules/rulesConfig/hooks/usePageTable.tsx", "componentName": "operationcenter-rules-rulesconfig-hooks-usepagetable", "layout": false, "exports": ["default"]}, {"path": "workBench/workBench/components/ContentCom/pageConfig", "id": "workBench/workBench/components/ContentCom/pageConfig", "parentId": "layout", "file": "workBench/workBench/components/ContentCom/pageConfig.ts", "componentName": "workbench-workbench-components-contentcom-pageconfig", "layout": false, "exports": ["pageConfig"]}, {"path": "caseManage/caseList/components/DispatchDrawer", "index": true, "id": "caseManage/caseList/components/DispatchDrawer", "parentId": "layout", "file": "caseManage/caseList/components/DispatchDrawer/index.tsx", "componentName": "casemanage-caselist-components-dispatchdrawer-index", "layout": false, "exports": ["default"]}, {"path": "flowManage/nodeConfig/components/ParamSettingDrawer", "id": "flowManage/nodeConfig/components/ParamSettingDrawer", "parentId": "layout", "file": "flowManage/nodeConfig/components/ParamSettingDrawer.tsx", "componentName": "flowmanage-nodeconfig-components-paramsettingdrawer", "layout": false, "exports": ["default"]}, {"path": "operationCenter/rules/rulesConfig/components/IRules", "id": "operationCenter/rules/rulesConfig/components/IRules", "parentId": "layout", "file": "operationCenter/rules/rulesConfig/components/IRules.ts", "componentName": "operationcenter-rules-rulesconfig-components-irules", "layout": false, "exports": []}, {"path": "workBench/collectorBench/components/CustomLayoutCom", "id": "workBench/collectorBench/components/CustomLayoutCom", "parentId": "layout", "file": "workBench/collectorBench/components/CustomLayoutCom.tsx", "componentName": "workbench-collectorbench-components-customlayoutcom", "layout": false, "exports": ["default"]}, {"path": "flowManage/program/components/ParamSettingDrawer", "id": "flowManage/program/components/ParamSettingDrawer", "parentId": "layout", "file": "flowManage/program/components/ParamSettingDrawer.tsx", "componentName": "flowmanage-program-components-paramsettingdrawer", "layout": false, "exports": ["default"]}, {"path": "caseManage/caseList/components/CaseDrawer", "index": true, "id": "caseManage/caseList/components/CaseDrawer", "parentId": "layout", "file": "caseManage/caseList/components/CaseDrawer/index.tsx", "componentName": "casemanage-caselist-components-casedrawer-index", "layout": false, "exports": ["default"]}, {"path": "workBench/components/DebtCollectionCom/mockData", "id": "workBench/components/DebtCollectionCom/mockData", "parentId": "layout", "file": "workBench/components/DebtCollectionCom/mockData.ts", "componentName": "workbench-components-debtcollectioncom-mockdata", "layout": false, "exports": ["default"]}, {"path": "workBench/workBench/components/ContentCom", "index": true, "id": "workBench/workBench/components/ContentCom", "parentId": "layout", "file": "workBench/workBench/components/ContentCom/index.tsx", "componentName": "workbench-workbench-components-contentcom-index", "layout": false, "exports": ["default"]}, {"path": "caseManage/caseList/components/CaseModal", "index": true, "id": "caseManage/caseList/components/CaseModal", "parentId": "layout", "file": "caseManage/caseList/components/CaseModal/index.tsx", "componentName": "casemanage-caselist-components-casemodal-index", "layout": false, "exports": ["default"]}, {"path": "flowManage/labelManage/componnets/LabelSetting", "id": "flowManage/labelManage/componnets/LabelSetting", "parentId": "layout", "file": "flowManage/labelManage/componnets/LabelSetting.tsx", "componentName": "flowmanage-labelmanage-componnets-labelsetting", "layout": false, "exports": ["default"]}, {"path": "flowManage/labelManage/hooks/usePageFormAction", "id": "flowManage/labelManage/hooks/usePageFormAction", "parentId": "layout", "file": "flowManage/labelManage/hooks/usePageFormAction.tsx", "componentName": "flowmanage-labelmanage-hooks-usepageformaction", "layout": false, "exports": ["default"]}, {"path": "operationCenter/rules/ruleFactor/useFormConfig", "id": "operationCenter/rules/ruleFactor/useFormConfig", "parentId": "layout", "file": "operationCenter/rules/ruleFactor/useFormConfig.tsx", "componentName": "operationcenter-rules-rulefactor-useformconfig", "layout": false, "exports": ["default"]}, {"path": "strategyManage/stationManagement/useFormConfig", "id": "strategyManage/stationManagement/useFormConfig", "parentId": "layout", "file": "strategyManage/stationManagement/useFormConfig.tsx", "componentName": "strategymanage-stationmanagement-useformconfig", "layout": false, "exports": ["default"]}, {"path": "workBench/workBench/components/HeaderCom", "index": true, "id": "workBench/workBench/components/HeaderCom", "parentId": "layout", "file": "workBench/workBench/components/HeaderCom/index.tsx", "componentName": "workbench-workbench-components-headercom-index", "layout": false, "exports": ["default"]}, {"path": "operationCenter/rules/rulesConfig/pageConfig", "id": "operationCenter/rules/rulesConfig/pageConfig", "parentId": "layout", "file": "operationCenter/rules/rulesConfig/pageConfig.ts", "componentName": "operationcenter-rules-rulesconfig-pageconfig", "layout": false, "exports": ["cardTitle", "defOptionList", "formActionPermissionObj", "prefix", "resetValue", "tableProps", "url<PERSON>bj"]}, {"path": "strategyManage/staffManagement/useFormConfig", "id": "strategyManage/staffManagement/useFormConfig", "parentId": "layout", "file": "strategyManage/staffManagement/useFormConfig.tsx", "componentName": "strategymanage-staffmanagement-useformconfig", "layout": false, "exports": ["default"]}, {"path": "strategyManage/strategySetting/useFormConfig", "id": "strategyManage/strategySetting/useFormConfig", "parentId": "layout", "file": "strategyManage/strategySetting/useFormConfig.tsx", "componentName": "strategymanage-strategysetting-useformconfig", "layout": false, "exports": ["default"]}, {"path": "workBench/components/DebtCollectionCom", "index": true, "id": "workBench/components/DebtCollectionCom", "parentId": "layout", "file": "workBench/components/DebtCollectionCom/index.tsx", "componentName": "workbench-components-debtcollectioncom-index", "layout": false, "exports": ["default"]}, {"path": "flowManage/nodeConfig/components/ParamTable", "id": "flowManage/nodeConfig/components/ParamTable", "parentId": "layout", "file": "flowManage/nodeConfig/components/ParamTable.tsx", "componentName": "flowmanage-nodeconfig-components-paramtable", "layout": false, "exports": ["default"]}, {"path": "operationCenter/rules/ruleFactor/pageConfig", "id": "operationCenter/rules/ruleFactor/pageConfig", "parentId": "layout", "file": "operationCenter/rules/ruleFactor/pageConfig.tsx", "componentName": "operationcenter-rules-rulefactor-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "strategyManage/stationManagement/pageConfig", "id": "strategyManage/stationManagement/pageConfig", "parentId": "layout", "file": "strategyManage/stationManagement/pageConfig.tsx", "componentName": "strategymanage-stationmanagement-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "flowManage/labelManage/hooks/useFormConfig", "id": "flowManage/labelManage/hooks/useFormConfig", "parentId": "layout", "file": "flowManage/labelManage/hooks/useFormConfig.tsx", "componentName": "flowmanage-labelmanage-hooks-useformconfig", "layout": false, "exports": ["default"]}, {"path": "flowManage/program/hooks/usePageFormAction", "id": "flowManage/program/hooks/usePageFormAction", "parentId": "layout", "file": "flowManage/program/hooks/usePageFormAction.tsx", "componentName": "flowmanage-program-hooks-usepageformaction", "layout": false, "exports": ["default"]}, {"path": "operationCenter/intelligence/aiRuels", "index": true, "id": "operationCenter/intelligence/aiRuels", "parentId": "layout", "file": "operationCenter/intelligence/aiRuels/index.tsx", "componentName": "operationcenter-intelligence-airuels-index", "layout": false, "exports": ["default"]}, {"path": "strategyManage/weightSetting/useFormConfig", "id": "strategyManage/weightSetting/useFormConfig", "parentId": "layout", "file": "strategyManage/weightSetting/useFormConfig.tsx", "componentName": "strategymanage-weightsetting-useformconfig", "layout": false, "exports": ["default"]}, {"path": "strategyManage/staffManagement/pageConfig", "id": "strategyManage/staffManagement/pageConfig", "parentId": "layout", "file": "strategyManage/staffManagement/pageConfig.tsx", "componentName": "strategymanage-staffmanagement-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "strategyManage/stationManagement/mockData", "id": "strategyManage/stationManagement/mockData", "parentId": "layout", "file": "strategyManage/stationManagement/mockData.js", "componentName": "strategymanage-stationmanagement-mockdata", "layout": false, "exports": ["default"]}, {"path": "strategyManage/strategySetting/pageConfig", "id": "strategyManage/strategySetting/pageConfig", "parentId": "layout", "file": "strategyManage/strategySetting/pageConfig.tsx", "componentName": "strategymanage-strategysetting-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "systemManage/menu/hooks/usePageFormAction", "id": "systemManage/menu/hooks/usePageFormAction", "parentId": "layout", "file": "systemManage/menu/hooks/usePageFormAction.tsx", "componentName": "systemmanage-menu-hooks-usepageformaction", "layout": false, "exports": ["default"]}, {"path": "systemManage/org/components/OrgDetailCard", "id": "systemManage/org/components/OrgDetailCard", "parentId": "layout", "file": "systemManage/org/components/OrgDetailCard.tsx", "componentName": "systemmanage-org-components-orgdetailcard", "layout": false, "exports": ["default"]}, {"path": "flowManage/nodeConfig/ParamSettingDrawer", "id": "flowManage/nodeConfig/ParamSettingDrawer", "parentId": "layout", "file": "flowManage/nodeConfig/ParamSettingDrawer.tsx", "componentName": "flowmanage-nodeconfig-paramsettingdrawer", "layout": false, "exports": ["default"]}, {"path": "flowManage/program/components/ParamTable", "id": "flowManage/program/components/ParamTable", "parentId": "layout", "file": "flowManage/program/components/ParamTable.tsx", "componentName": "flowmanage-program-components-paramtable", "layout": false, "exports": ["default"]}, {"path": "sourceManage/case/caseInfo/useFormConfig", "id": "sourceManage/case/caseInfo/useFormConfig", "parentId": "layout", "file": "sourceManage/case/caseInfo/useFormConfig.tsx", "componentName": "sourcemanage-case-caseinfo-useformconfig", "layout": false, "exports": ["default"]}, {"path": "strategyManage/sortSetting/useFormConfig", "id": "strategyManage/sortSetting/useFormConfig", "parentId": "layout", "file": "strategyManage/sortSetting/useFormConfig.tsx", "componentName": "strategymanage-sortsetting-useformconfig", "layout": false, "exports": ["default"]}, {"path": "systemManage/aduitManage/hooks/useSearch", "id": "systemManage/aduitManage/hooks/useSearch", "parentId": "layout", "file": "systemManage/aduitManage/hooks/useSearch.tsx", "componentName": "systemmanage-aduitmanage-hooks-usesearch", "layout": false, "exports": ["default"]}, {"path": "systemManage/role/components/MenuAuthCom", "id": "systemManage/role/components/MenuAuthCom", "parentId": "layout", "file": "systemManage/role/components/MenuAuthCom.tsx", "componentName": "systemmanage-role-components-menuauthcom", "layout": false, "exports": ["default"]}, {"path": "flowManage/nodeFlowManage/useFormConfig", "id": "flowManage/nodeFlowManage/useFormConfig", "parentId": "layout", "file": "flowManage/nodeFlowManage/useFormConfig.tsx", "componentName": "flowmanage-nodeflowmanage-useformconfig", "layout": false, "exports": ["default"]}, {"path": "operationCenter/rules/rulesConfig", "index": true, "id": "operationCenter/rules/rulesConfig", "parentId": "layout", "file": "operationCenter/rules/rulesConfig/index.tsx", "componentName": "operationcenter-rules-rulesconfig-index", "layout": false, "exports": ["default"]}, {"path": "strategyManage/staffManagement/mockData", "id": "strategyManage/staffManagement/mockData", "parentId": "layout", "file": "strategyManage/staffManagement/mockData.js", "componentName": "strategymanage-staffmanagement-mockdata", "layout": false, "exports": ["default"]}, {"path": "strategyManage/weightSetting/pageConfig", "id": "strategyManage/weightSetting/pageConfig", "parentId": "layout", "file": "strategyManage/weightSetting/pageConfig.tsx", "componentName": "strategymanage-weightsetting-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "systemManage/aduitManage/hooks/useTable", "id": "systemManage/aduitManage/hooks/useTable", "parentId": "layout", "file": "systemManage/aduitManage/hooks/useTable.tsx", "componentName": "systemmanage-aduitmanage-hooks-usetable", "layout": false, "exports": ["default"]}, {"path": "systemManage/org/components/AddOrgModal", "id": "systemManage/org/components/AddOrgModal", "parentId": "layout", "file": "systemManage/org/components/AddOrgModal.tsx", "componentName": "systemmanage-org-components-addorgmodal", "layout": false, "exports": ["default"]}, {"path": "flowManage/labelManage/hooks/useSearch", "id": "flowManage/labelManage/hooks/useSearch", "parentId": "layout", "file": "flowManage/labelManage/hooks/useSearch.tsx", "componentName": "flowmanage-labelmanage-hooks-usesearch", "layout": false, "exports": ["default"]}, {"path": "flowManage/program/hooks/useFormConfig", "id": "flowManage/program/hooks/useFormConfig", "parentId": "layout", "file": "flowManage/program/hooks/useFormConfig.tsx", "componentName": "flowmanage-program-hooks-useformconfig", "layout": false, "exports": ["default"]}, {"path": "operationCenter/rules/ruleFactor", "index": true, "id": "operationCenter/rules/ruleFactor", "parentId": "layout", "file": "operationCenter/rules/ruleFactor/index.tsx", "componentName": "operationcenter-rules-rulefactor-index", "layout": false, "exports": ["default"]}, {"path": "operationCenter/rules/ruleFactor/utill", "id": "operationCenter/rules/ruleFactor/utill", "parentId": "layout", "file": "operationCenter/rules/ruleFactor/utill.ts", "componentName": "operationcenter-rules-rulefactor-utill", "layout": false, "exports": ["convertToArray"]}, {"path": "strategyManage/stationManagement", "index": true, "id": "strategyManage/stationManagement", "parentId": "layout", "file": "strategyManage/stationManagement/index.tsx", "componentName": "strategymanage-stationmanagement-index", "layout": false, "exports": ["default"]}, {"path": "strategyManage/workbenck/useFormConfig", "id": "strategyManage/workbenck/useFormConfig", "parentId": "layout", "file": "strategyManage/workbenck/useFormConfig.tsx", "componentName": "strategymanage-workbenck-useformconfig", "layout": false, "exports": ["default"]}, {"path": "flowManage/labelManage/hooks/useTable", "id": "flowManage/labelManage/hooks/useTable", "parentId": "layout", "file": "flowManage/labelManage/hooks/useTable.tsx", "componentName": "flowmanage-labelmanage-hooks-usetable", "layout": false, "exports": ["default"]}, {"path": "sourceManage/case/caseInfo/pageConfig", "id": "sourceManage/case/caseInfo/pageConfig", "parentId": "layout", "file": "sourceManage/case/caseInfo/pageConfig.tsx", "componentName": "sourcemanage-case-caseinfo-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "strategyManage/sortSetting/pageConfig", "id": "strategyManage/sortSetting/pageConfig", "parentId": "layout", "file": "strategyManage/sortSetting/pageConfig.tsx", "componentName": "strategymanage-sortsetting-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "dataManage/smsTemplate/useFormConfig", "id": "dataManage/smsTemplate/useFormConfig", "parentId": "layout", "file": "dataManage/smsTemplate/useFormConfig.tsx", "componentName": "datamanage-smstemplate-useformconfig", "layout": false, "exports": ["default"]}, {"path": "flowManage/dispatchManage/pageConfig", "id": "flowManage/dispatchManage/pageConfig", "parentId": "layout", "file": "flowManage/dispatchManage/pageConfig.tsx", "componentName": "flowmanage-dispatchmanage-pageconfig", "layout": false, "exports": ["pageConfig"]}, {"path": "flowManage/nodeFlowManage/pageConfig", "id": "flowManage/nodeFlowManage/pageConfig", "parentId": "layout", "file": "flowManage/nodeFlowManage/pageConfig.tsx", "componentName": "flowmanage-nodeflowmanage-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "strategyManage/staffManagement", "index": true, "id": "strategyManage/staffManagement", "parentId": "layout", "file": "strategyManage/staffManagement/index.tsx", "componentName": "strategymanage-staffmanagement-index", "layout": false, "exports": ["default"]}, {"path": "strategyManage/strategySetting", "index": true, "id": "strategyManage/strategySetting", "parentId": "layout", "file": "strategyManage/strategySetting/index.tsx", "componentName": "strategymanage-strategysetting-index", "layout": false, "exports": ["default"]}, {"path": "workBench/components/HeaderCom", "index": true, "id": "workBench/components/HeaderCom", "parentId": "layout", "file": "workBench/components/HeaderCom/index.tsx", "componentName": "workbench-components-headercom-index", "layout": false, "exports": ["default"]}, {"path": "workBench/messageBench/useFormConfig", "id": "workBench/messageBench/useFormConfig", "parentId": "layout", "file": "workBench/messageBench/useFormConfig.tsx", "componentName": "workbench-messagebench-useformconfig", "layout": false, "exports": ["default"]}, {"path": "workBench/workBench/constansts", "index": true, "id": "workBench/workBench/constansts", "parentId": "layout", "file": "workBench/workBench/constansts/index.tsx", "componentName": "workbench-workbench-constansts-index", "layout": false, "exports": ["default"]}, {"path": "flowManage/nodeConfig/useFormConfig", "id": "flowManage/nodeConfig/useFormConfig", "parentId": "layout", "file": "flowManage/nodeConfig/useFormConfig.tsx", "componentName": "flowmanage-nodeconfig-useformconfig", "layout": false, "exports": ["default"]}, {"path": "sourceManage/case/caseInfo/mockData", "id": "sourceManage/case/caseInfo/mockData", "parentId": "layout", "file": "sourceManage/case/caseInfo/mockData.ts", "componentName": "sourcemanage-case-caseinfo-mockdata", "layout": false, "exports": ["default"]}, {"path": "strategyManage/workbenck/pageConfig", "id": "strategyManage/workbenck/pageConfig", "parentId": "layout", "file": "strategyManage/workbenck/pageConfig.tsx", "componentName": "strategymanage-workbenck-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "systemManage/aduitManage/pageConfig", "id": "systemManage/aduitManage/pageConfig", "parentId": "layout", "file": "systemManage/aduitManage/pageConfig.tsx", "componentName": "systemmanage-aduitmanage-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "systemManage/org/components/OrgTree", "id": "systemManage/org/components/OrgTree", "parentId": "layout", "file": "systemManage/org/components/OrgTree.tsx", "componentName": "systemmanage-org-components-orgtree", "layout": false, "exports": ["default"]}, {"path": "systemManage/position/useFormConfig", "id": "systemManage/position/useFormConfig", "parentId": "layout", "file": "systemManage/position/useFormConfig.tsx", "componentName": "systemmanage-position-useformconfig", "layout": false, "exports": ["default"]}, {"path": "caseManage/caseQuery/useFormConfig", "id": "caseManage/caseQuery/useFormConfig", "parentId": "layout", "file": "caseManage/caseQuery/useFormConfig.tsx", "componentName": "casemanage-casequery-useformconfig", "layout": false, "exports": ["default"]}, {"path": "flowManage/dispatchManage/mockData", "id": "flowManage/dispatchManage/mockData", "parentId": "layout", "file": "flowManage/dispatchManage/mockData.ts", "componentName": "flowmanage-dispatchmanage-mockdata", "layout": false, "exports": ["data1", "data2", "data3"]}, {"path": "flowManage/program/hooks/useSearch", "id": "flowManage/program/hooks/useSearch", "parentId": "layout", "file": "flowManage/program/hooks/useSearch.tsx", "componentName": "flowmanage-program-hooks-usesearch", "layout": false, "exports": ["default"]}, {"path": "strategyManage/weightSetting", "index": true, "id": "strategyManage/weightSetting", "parentId": "layout", "file": "strategyManage/weightSetting/index.tsx", "componentName": "strategymanage-weightsetting-index", "layout": false, "exports": ["default"]}, {"path": "caseManage/caseList/useFormConfig", "id": "caseManage/caseList/useFormConfig", "parentId": "layout", "file": "caseManage/caseList/useFormConfig.tsx", "componentName": "casemanage-caselist-useformconfig", "layout": false, "exports": ["default"]}, {"path": "dataManage/callList/useFormConfig", "id": "dataManage/callList/useFormConfig", "parentId": "layout", "file": "dataManage/callList/useFormConfig.tsx", "componentName": "datamanage-calllist-useformconfig", "layout": false, "exports": ["default"]}, {"path": "dataManage/realtime/useFormConfig", "id": "dataManage/realtime/useFormConfig", "parentId": "layout", "file": "dataManage/realtime/useFormConfig.tsx", "componentName": "datamanage-realtime-useformconfig", "layout": false, "exports": ["default"]}, {"path": "dataManage/smsTemplate/pageConfig", "id": "dataManage/smsTemplate/pageConfig", "parentId": "layout", "file": "dataManage/smsTemplate/pageConfig.tsx", "componentName": "datamanage-smstemplate-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "flowManage/labelManage/pageConfig", "id": "flowManage/labelManage/pageConfig", "parentId": "layout", "file": "flowManage/labelManage/pageConfig.tsx", "componentName": "flowmanage-labelmanage-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "flowManage/program/hooks/useTable", "id": "flowManage/program/hooks/useTable", "parentId": "layout", "file": "flowManage/program/hooks/useTable.tsx", "componentName": "flowmanage-program-hooks-usetable", "layout": false, "exports": ["default"]}, {"path": "systemManage/menu/hooks/useSearch", "id": "systemManage/menu/hooks/useSearch", "parentId": "layout", "file": "systemManage/menu/hooks/useSearch.tsx", "componentName": "systemmanage-menu-hooks-usesearch", "layout": false, "exports": ["default"]}, {"path": "workBench/collectorBench/mockData", "id": "workBench/collectorBench/mockData", "parentId": "layout", "file": "workBench/collectorBench/mockData.ts", "componentName": "workbench-collectorbench-mockdata", "layout": false, "exports": ["default"]}, {"path": "workBench/messageBench/pageConfig", "id": "workBench/messageBench/pageConfig", "parentId": "layout", "file": "workBench/messageBench/pageConfig.ts", "componentName": "workbench-messagebench-pageconfig", "layout": false, "exports": ["anchorData", "descData", "segmentedData"]}, {"path": "dataManage/offline/useFormConfig", "id": "dataManage/offline/useFormConfig", "parentId": "layout", "file": "dataManage/offline/useFormConfig.tsx", "componentName": "datamanage-offline-useformconfig", "layout": false, "exports": ["default"]}, {"path": "flowManage/dispatchManage/Detail", "id": "flowManage/dispatchManage/Detail", "parentId": "layout", "file": "flowManage/dispatchManage/Detail.tsx", "componentName": "flowmanage-dispatchmanage-detail", "layout": false, "exports": ["default"]}, {"path": "flowManage/nodeConfig/pageConfig", "id": "flowManage/nodeConfig/pageConfig", "parentId": "layout", "file": "flowManage/nodeConfig/pageConfig.tsx", "componentName": "flowmanage-nodeconfig-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "sourceManage/case/caseInfo", "index": true, "id": "sourceManage/case/caseInfo", "parentId": "layout", "file": "sourceManage/case/caseInfo/index.tsx", "componentName": "sourcemanage-case-caseinfo-index", "layout": false, "exports": ["default"]}, {"path": "strategyManage/sortSetting", "index": true, "id": "strategyManage/sortSetting", "parentId": "layout", "file": "strategyManage/sortSetting/index.tsx", "componentName": "strategymanage-sortsetting-index", "layout": false, "exports": ["default"]}, {"path": "systemManage/menu/hooks/useTable", "id": "systemManage/menu/hooks/useTable", "parentId": "layout", "file": "systemManage/menu/hooks/useTable.tsx", "componentName": "systemmanage-menu-hooks-usetable", "layout": false, "exports": ["default"]}, {"path": "systemManage/position/pageConfig", "id": "systemManage/position/pageConfig", "parentId": "layout", "file": "systemManage/position/pageConfig.tsx", "componentName": "systemmanage-position-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "caseManage/caseQuery/pageConfig", "id": "caseManage/caseQuery/pageConfig", "parentId": "layout", "file": "caseManage/caseQuery/pageConfig.tsx", "componentName": "casemanage-casequery-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "flowManage/dispatchManage", "index": true, "id": "flowManage/dispatchManage", "parentId": "layout", "file": "flowManage/dispatchManage/index.tsx", "componentName": "flowmanage-dispatchmanage-index", "layout": false, "exports": ["default"]}, {"path": "flowManage/nodeFlowManage", "index": true, "id": "flowManage/nodeFlowManage", "parentId": "layout", "file": "flowManage/nodeFlowManage/index.tsx", "componentName": "flowmanage-nodeflowmanage-index", "layout": false, "exports": ["default"]}, {"path": "systemManage/dict/useFormConfig", "id": "systemManage/dict/useFormConfig", "parentId": "layout", "file": "systemManage/dict/useFormConfig.tsx", "componentName": "systemmanage-dict-useformconfig", "layout": false, "exports": ["default"]}, {"path": "systemManage/menu/useFormConfig", "id": "systemManage/menu/useFormConfig", "parentId": "layout", "file": "systemManage/menu/useFormConfig.tsx", "componentName": "systemmanage-menu-useformconfig", "layout": false, "exports": ["default"]}, {"path": "systemManage/role/useFormConfig", "id": "systemManage/role/useFormConfig", "parentId": "layout", "file": "systemManage/role/useFormConfig.tsx", "componentName": "systemmanage-role-useformconfig", "layout": false, "exports": ["default", "roleStatusEunm"]}, {"path": "systemManage/user/useFormConfig", "id": "systemManage/user/useFormConfig", "parentId": "layout", "file": "systemManage/user/useFormConfig.tsx", "componentName": "systemmanage-user-useformconfig", "layout": false, "exports": ["default"]}, {"path": "workBench/messageBench/mockData", "id": "workBench/messageBench/mockData", "parentId": "layout", "file": "workBench/messageBench/mockData.ts", "componentName": "workbench-messagebench-mockdata", "layout": false, "exports": ["default"]}, {"path": "workBench/projectBench/mockData", "id": "workBench/projectBench/mockData", "parentId": "layout", "file": "workBench/projectBench/mockData.ts", "componentName": "workbench-projectbench-mockdata", "layout": false, "exports": ["default"]}, {"path": "caseManage/caseList/configData", "id": "caseManage/caseList/configData", "parentId": "layout", "file": "caseManage/caseList/configData.tsx", "componentName": "casemanage-caselist-configdata", "layout": false, "exports": ["default"]}, {"path": "caseManage/caseList/pageConfig", "id": "caseManage/caseList/pageConfig", "parentId": "layout", "file": "caseManage/caseList/pageConfig.tsx", "componentName": "casemanage-caselist-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "dataManage/callList/pageConfig", "id": "dataManage/callList/pageConfig", "parentId": "layout", "file": "dataManage/callList/pageConfig.tsx", "componentName": "datamanage-calllist-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "dataManage/realtime/pageConfig", "id": "dataManage/realtime/pageConfig", "parentId": "layout", "file": "dataManage/realtime/pageConfig.tsx", "componentName": "datamanage-realtime-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "flowManage/nodeConfig/mockData", "id": "flowManage/nodeConfig/mockData", "parentId": "layout", "file": "flowManage/nodeConfig/mockData.ts", "componentName": "flowmanage-nodeconfig-mockdata", "layout": false, "exports": ["data1", "data2", "data3"]}, {"path": "strategyManage/workbenck", "index": true, "id": "strategyManage/workbenck", "parentId": "layout", "file": "strategyManage/workbenck/index.tsx", "componentName": "strategymanage-workbenck-index", "layout": false, "exports": ["default"]}, {"path": "systemManage/aduitManage", "index": true, "id": "systemManage/aduitManage", "parentId": "layout", "file": "systemManage/aduitManage/index.tsx", "componentName": "systemmanage-aduitmanage-index", "layout": false, "exports": ["default"]}, {"path": "workBench/collectorBench", "index": true, "id": "workBench/collectorBench", "parentId": "layout", "file": "workBench/collectorBench/index.tsx", "componentName": "workbench-collectorbench-index", "layout": false, "exports": ["default"]}, {"path": "caseManage/caseQuery/mockData", "id": "caseManage/caseQuery/mockData", "parentId": "layout", "file": "caseManage/caseQuery/mockData.ts", "componentName": "casemanage-casequery-mockdata", "layout": false, "exports": ["default"]}, {"path": "dataManage/offline/pageConfig", "id": "dataManage/offline/pageConfig", "parentId": "layout", "file": "dataManage/offline/pageConfig.tsx", "componentName": "datamanage-offline-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "flowManage/program/pageConfig", "id": "flowManage/program/pageConfig", "parentId": "layout", "file": "flowManage/program/pageConfig.tsx", "componentName": "flowmanage-program-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "dataManage/smsTemplate", "index": true, "id": "dataManage/smsTemplate", "parentId": "layout", "file": "dataManage/smsTemplate/index.tsx", "componentName": "datamanage-smstemplate-index", "layout": false, "exports": ["default"]}, {"path": "flowManage/labelManage", "index": true, "id": "flowManage/labelManage", "parentId": "layout", "file": "flowManage/labelManage/index.tsx", "componentName": "flowmanage-labelmanage-index", "layout": false, "exports": ["default"]}, {"path": "systemManage/dict/pageConfig", "id": "systemManage/dict/pageConfig", "parentId": "layout", "file": "systemManage/dict/pageConfig.tsx", "componentName": "systemmanage-dict-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "systemManage/menu/pageConfig", "id": "systemManage/menu/pageConfig", "parentId": "layout", "file": "systemManage/menu/pageConfig.tsx", "componentName": "systemmanage-menu-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "systemManage/role/pageConfig", "id": "systemManage/role/pageConfig", "parentId": "layout", "file": "systemManage/role/pageConfig.tsx", "componentName": "systemmanage-role-pageconfig", "layout": false, "exports": ["mountstatusEunm", "pageConfig", "roleStatusEunm", "userPageConfig"]}, {"path": "systemManage/user/pageConfig", "id": "systemManage/user/pageConfig", "parentId": "layout", "file": "systemManage/user/pageConfig.tsx", "componentName": "systemmanage-user-pageconfig", "layout": false, "exports": ["dictEnum", "pageConfig"]}, {"path": "workBench/messageBench", "index": true, "id": "workBench/messageBench", "parentId": "layout", "file": "workBench/messageBench/index.tsx", "componentName": "workbench-messagebench-index", "layout": false, "exports": ["default"]}, {"path": "workBench/projectBench", "index": true, "id": "workBench/projectBench", "parentId": "layout", "file": "workBench/projectBench/index.tsx", "componentName": "workbench-projectbench-index", "layout": false, "exports": ["default"]}, {"path": "flowManage/nodeConfig", "index": true, "id": "flowManage/nodeConfig", "parentId": "layout", "file": "flowManage/nodeConfig/index.tsx", "componentName": "flowmanage-nodeconfig-index", "layout": false, "exports": ["default"]}, {"path": "systemManage/position", "index": true, "id": "systemManage/position", "parentId": "layout", "file": "systemManage/position/index.tsx", "componentName": "systemmanage-position-index", "layout": false, "exports": ["default"]}, {"path": "caseManage/caseQuery", "index": true, "id": "caseManage/caseQuery", "parentId": "layout", "file": "caseManage/caseQuery/index.tsx", "componentName": "casemanage-casequery-index", "layout": false, "exports": ["default"]}, {"path": "systemManage/menu/iconData", "id": "systemManage/menu/iconData", "parentId": "layout", "file": "systemManage/menu/iconData.ts", "componentName": "systemmanage-menu-icondata", "layout": false, "exports": ["dataOutlined", "directionOutlined", "editorOut<PERSON>", "logoOutlined", "otherOutlined", "suggestionOutlined"]}, {"path": "systemManage/org/constants", "id": "systemManage/org/constants", "parentId": "layout", "file": "systemManage/org/constants.ts", "componentName": "systemmanage-org-constants", "layout": false, "exports": ["ORG_DETAIL_MAP", "ORG_TYPE_OPTIONS", "PARENT_ORG_OPTIONS", "default"]}, {"path": "caseManage/caseList", "index": true, "id": "caseManage/caseList", "parentId": "layout", "file": "caseManage/caseList/index.tsx", "componentName": "casemanage-caselist-index", "layout": false, "exports": ["default"]}, {"path": "dataManage/callList", "index": true, "id": "dataManage/callList", "parentId": "layout", "file": "dataManage/callList/index.tsx", "componentName": "datamanage-calllist-index", "layout": false, "exports": ["default"]}, {"path": "dataManage/realtime", "index": true, "id": "dataManage/realtime", "parentId": "layout", "file": "dataManage/realtime/index.tsx", "componentName": "datamanage-realtime-index", "layout": false, "exports": ["default"]}, {"path": "workBench/workBench", "index": true, "id": "workBench/workBench", "parentId": "layout", "file": "workBench/workBench/index.tsx", "componentName": "workbench-workbench-index", "layout": false, "exports": ["default"]}, {"path": "dataManage/offline", "index": true, "id": "dataManage/offline", "parentId": "layout", "file": "dataManage/offline/index.tsx", "componentName": "datamanage-offline-index", "layout": false, "exports": ["default"]}, {"path": "flowManage/program", "index": true, "id": "flowManage/program", "parentId": "layout", "file": "flowManage/program/index.tsx", "componentName": "flowmanage-program-index", "layout": false, "exports": ["default"]}, {"path": "systemManage/dict", "index": true, "id": "systemManage/dict", "parentId": "layout", "file": "systemManage/dict/index.tsx", "componentName": "systemmanage-dict-index", "layout": false, "exports": ["default"]}, {"path": "systemManage/menu", "index": true, "id": "systemManage/menu", "parentId": "layout", "file": "systemManage/menu/index.tsx", "componentName": "systemmanage-menu-index", "layout": false, "exports": ["default"]}, {"path": "systemManage/role", "index": true, "id": "systemManage/role", "parentId": "layout", "file": "systemManage/role/index.tsx", "componentName": "systemmanage-role-index", "layout": false, "exports": ["default"]}, {"path": "systemManage/user", "index": true, "id": "systemManage/user", "parentId": "layout", "file": "systemManage/user/index.tsx", "componentName": "systemmanage-user-index", "layout": false, "exports": ["default"]}, {"path": "systemManage/menu/util", "id": "systemManage/menu/util", "parentId": "layout", "file": "systemManage/menu/util.tsx", "componentName": "systemmanage-menu-util", "layout": false, "exports": ["statusEunm", "tabTypeEunm"]}, {"path": "systemManage/org", "index": true, "id": "systemManage/org", "parentId": "layout", "file": "systemManage/org/index.tsx", "componentName": "systemmanage-org-index", "layout": false, "exports": ["default"]}, {"path": "login", "index": true, "id": "login", "parentId": "layout", "file": "login/index.tsx", "componentName": "login-index", "layout": false, "exports": ["default"]}, {"index": true, "id": "/", "parentId": "layout", "file": "index.tsx", "componentName": "index", "layout": false, "exports": ["default"]}, {"path": "*", "id": "*", "parentId": "layout", "file": "$.tsx", "componentName": "$", "layout": false, "exports": ["default"]}]}]