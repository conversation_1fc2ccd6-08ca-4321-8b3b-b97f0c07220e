export default {
    'GET /api/RoleInfo': {
        header: {
            requestId: '99999999',
            gid: '888888',
            errorCode: '000000',
            errorMsg: '操作成功',
            success: true,
        },
        data: [
            {
                roleName: '全局管理员',
                roleId: '1',
                roleStatus: '1',
                mountStatus: '1',
                createUser: '张三',
                updateUser: '李四',
                createTs: '2024-6-21 13:13:13',
                updateTs: '2024-6-21 13:13:13',
                roleDesc: '全局管理员',
            },
            {
                roleName: '成员',
                roleId: '2',
                roleStatus: '1',
                mountStatus: '1',
                createUser: '张三',
                updateUser: '李四',
                createTs: '2024-6-21 13:13:14',
                updateTs: '2024-6-21 13:13:14',
                roleDesc: '成员',
            },
            {
                roleName: '外部访客',
                roleId: '3',
                roleStatus: '0',
                mountStatus: '0',
                createUser: '张三',
                updateUser: '李四',
                createTs: '2024-6-21 13:13:15',
                updateTs: '2024-6-21 13:13:15',
                roleDesc: '外部访客',
            },
        ],
    },
    'POST /api/RoleInfo': {
        header: {
            requestId: '99999999',
            gid: '888888',
            errorCode: '000000',
            errorMsg: '操作成功',
            success: true,
        },
        data: {
            roleName: '外部访客',
            roleId: '3',
            roleStatus: '1',
            mountStatus: '0',
            createUser: '张三',
            updateUser: '李四',
            createTs: '2024-6-21 13:13:15',
            updateTs: '2024-6-21 13:13:15',
            roleDesc: '外部访客',
        },
    },
    'GET /api/RoleInfo/MountDataById': {
        header: {
            requestId: '99999999',
            gid: '888888',
            errorCode: '000000',
            errorMsg: '操作成功',
            success: true,
        },
        data: {
            roleName: '外部访客',
            roleId: '3',
            menuInfoList: [
                { menuId: 'workbench', menuName: '工作台', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'workbench-1', menuName: '仪表盘', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param', menuName: '参数管理', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param-1', menuName: '参数视图', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param-2', menuName: '系统参数', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param-2-1', menuName: '机构参数', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param-3', menuName: '额度管理', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param-3-1', menuName: '额度节点', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param-3-2', menuName: '额度规则', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'system', menuName: '系统管理', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'system-1', menuName: '组织机构', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'system-2', menuName: '用户管理', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'system-3', menuName: '角色管理', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
            ],
        },
    },
    'POST /api/RoleInfo/updateMountData': {
        header: {
            requestId: '99999999',
            gid: '888888',
            errorCode: '000000',
            errorMsg: '操作成功',
            success: true,
        },
        data: {
            roleName: '外部访客',
            roleId: '3',
            menuInfoList: [
                { menuId: 'workbench', menuName: '工作台', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'workbench-1', menuName: '仪表盘', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param', menuName: '参数管理', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param-1', menuName: '参数视图', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param-2', menuName: '系统参数', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param-2-1', menuName: '机构参数', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param-3', menuName: '额度管理', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param-3-1', menuName: '额度节点', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'param-3-2', menuName: '额度规则', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'system', menuName: '系统管理', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'system-1', menuName: '组织机构', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'system-2', menuName: '用户管理', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
                { menuId: 'system-3', menuName: '角色管理', createUser: 'admin', createTs: '2024-6-20 12:12:12', updateUser: 'heshan', updateTs: '2024-6-22 12:12:12' },
            ],
        },
    },
};
