export default {
  // 查询字典配置页面数据
  'POST /api/system/param/dictionary/query': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        incId: '1',
        dictId: '111',
        dictCode: '111235',
        dictName: 'test011',
        dictDesc: '测测测测测测',
        status: 'Y',
      },
    ],
  },
  // 查询字典值配置页面数据
  'POST /api/system/param/dictionary/variable/query': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        incId: '1',
        dictCode: '111235',
        dictKey: '11112233',
        dictValue: 'dictValue',
        parentDictKey: 'parentDictKey',
      },
    ],
  },
  // 查询登录日志数据
  'POST /api/system/audit/loginLog/query': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        incId: 1,
        userId: '00001',
        userName: 'ABC',
        loginType: '1',
        loginTime: '2024-12-24 00:00:00.000',
        loginStatus: 'N',
        clientIp: '*************',
        serverName: 'ABABABABBA',
        createUser: '111',
        updateUser: '222',
      },
    ],
  },
  // 查询操作日志数据
  'POST /api/system/audit/operateLog/query': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        incId: 1,
        menuName: '111',
        userId: '00001',
        userName: 'ABC',
        operateType: 'R',
        operateTime: '2024-12-24 00:00:00.000',
        operateStatus: 'N',
        operateDetail: 'ABABABABBA',
        createUser: '111',
        updateUser: '222',
        operateServName: 'testetsteststtestsetstsettse',
      },
    ],
  },
};
