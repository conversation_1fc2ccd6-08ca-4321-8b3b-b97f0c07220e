# A new ice.js project

飞冰官方地址：https://v3.ice.work/

## 依赖安装

- 安装nodejs环境，版本在node 14或以上，我们暂定使用 node 20
- 安装npm插件

```bash
# 建议安装 nvm 进行 node 多版本管理
$ curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.1/install.sh | bash
$ nvm install 20
$ nvm use 20


# 注册淘宝源
$ npm config set registry http://registry.npm.taobao.org/
$ npm install
$ npm start

# 推荐使用
$ npm install -g yarn
$ yarn

# 推荐使用
$ npm i pnpm -g --register=https://registry.npmmirror.com/
$ pnpm -v

```

## 目录

```md
.
.
├── README.md
├── ice.config.mts # The project config
├── package.json
├── .browserslistrc # Browsers that we support
├── public
│   ├── favicon.ico  
├── src
| ├── assets
| │   ├── images # 图片文件（建议少用）
| │   ├── styles # 样式文件
| │   ├── svgs # 矢量图（建议替换图片）
| ├── components # 公共组件
| ├── constants # 公共常量
| ├── hooks # 公共业务逻辑
| ├── layouts # 页面布局组件
| ├── locales # 国际化语言包
| ├── models # 数据状态
| ├── pages # 页面文件
| ├── services # 服务文件（被pages、models使用）
| ├── types # 类型文件（约束pages、models数据类型）
| ├── utils # 帮助类文件
| ├── app.ts # 入口文件
| ├── document.tsx # html模板
| ├── global.css # 全局样式
| ├── menuConfig.tsx # 菜单配置
| ├── store.ts # 数据仓库（引用models）
| └── typings.d.ts
└── tsconfig.json
```

## 编码条约

- 命名

  > 1、目录、文件，均使用 Camel Case 格式，即小写开头；  
  > 2、tsx/jsx 组件命名，包括文件名，均使用 Pascal Case 格式，即大写开头，文件名index除外；  
  > 3、常量均为大写，多个单词下划线隔开，比如 EN_NUM；  
  > 4、事件命名格式 handle+触发动作，比如 handleSelect；  
  > 5、属性回调命名格式 on+触发动作，比如 onClick；

- 样式

  > 1、公共变量统一放在global.css，统一写原生css文件；  
  > 2、公共样式统一放在styles/custom.css里面；  
  > 3、覆盖antd样式统一放在styles/cover.css里面；  
  > 4、最终global.css，引入custom和cover，全局使用；  
  > 5、单独页面样式文件 module.css，只写独有样式，或者需要覆盖的样式；  
  > 6、非特殊情况，**不得使用!important**；  
  > 7、样式声明顺序：定位 -> 布局 -> 盒子 –> 寸尺 -> 表现；  
  > 8、像素单位统一使用rem；

- 编码
  > 1、hooks 组件顺序：内部变量 -> 副作用 -> 逻辑处理 –> 事件处理 -> return；  
  > 2、class 组件顺序：constructor -> componentDidMounted -> shouldComponentUpdated -> render -> 事件处理 -> 逻辑处理；  
  > 3、html 属性顺序：ref -> 数据属性 -> className -> style -> 事件；  
  > 4、所有与“新增、修改”接口的操作，都需要加入防抖功能；  
  > 5、除去异常处理打印，业务代码不允许出现console；

```angular2svg
<button ref={btnRef}
        attr-data='内嵌数据'
        className='flex-right'
        style={{height: 30}}
        onClick={handleClick}
>
    按钮
</button>
```

- 提交
  > 1、先 pull 远程分支代码，如有冲突，先处理完冲突，再提交代码；  
  > 2、统一以 eslint 风格，提交代码前，先执行风格统一命令：stylelint:fix 和 eslint:fix；

## 权限选择组件使用说明

### MenuAuthCom 组件

这是一个用于配置用户菜单权限和按钮权限的组件，提供了树形结构选择界面。

#### 功能特性

- **左侧权限树**: 显示所有可选的菜单和按钮权限，支持搜索和全选
- **右侧已选列表**: 实时显示已选择的权限及其完整路径
- **搜索功能**: 支持按权限名称搜索
- **全选/清空**: 一键全选或清空所有权限
- **树形展示**: 支持展开/折叠，清晰展示权限层级关系

#### 使用方法

```tsx
import MenuAuthCom from './components/MenuAuthCom';

const MyComponent = () => {
  const [menuAuthVisible, setMenuAuthVisible] = useState(false);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  const handlePermissionSubmit = (permissions: string[]) => {
    console.log('选中的权限:', permissions);
    setSelectedPermissions(permissions);
    setMenuAuthVisible(false);
  };

  return (
    <div>
      <Button onClick={() => setMenuAuthVisible(true)}>配置权限</Button>

      <MenuAuthCom
        open={menuAuthVisible}
        onSubmit={handlePermissionSubmit}
        onCancel={() => setMenuAuthVisible(false)}
        initialSelected={selectedPermissions}
      />
    </div>
  );
};
```

#### 组件属性

| 属性名          | 类型                            | 必填 | 说明                           |
| --------------- | ------------------------------- | ---- | ------------------------------ |
| open            | boolean                         | 是   | 控制组件显示/隐藏              |
| onSubmit        | (permissions: string[]) => void | 是   | 提交回调，返回选中的权限ID数组 |
| onCancel        | (visible: boolean) => void      | 是   | 取消回调                       |
| initialSelected | string[]                        | 否   | 初始选中的权限ID数组           |

#### 数据结构

权限数据基于 `TSysMenuItem` 类型，包含以下主要字段：

- `menuId`: 权限ID
- `menuName`: 权限名称
- `menuType`: 权限类型 ('0': 菜单, '1': 按钮)
- `parentMenuId`: 父级权限ID
- `permissionId`: 权限标识符

#### 示例效果

组件界面分为左右两栏：

- **左侧**: 权限选择树，支持搜索和全选
- **右侧**: 已选权限列表，显示完整路径

支持的功能：

- ✅ 树形权限选择
- ✅ 权限搜索过滤
- ✅ 全选/清空操作
- ✅ 实时显示选中数量
- ✅ 权限路径展示
- ✅ 响应式布局
